import type { CollectionConfig } from 'payload'
import { auditFields } from '../constants/fields/audit.field'
import { GROUPS } from '../constants/groups'
import { checkRole } from '../access/utils'

export const Media: CollectionConfig = {
  slug: 'media',
  admin: {
    group: GROUPS.MEDIA,
  },
  access: {
    read: () => true,
    create: ({ req: { user } }) => checkRole(['admin', 'media-manager'], user),
    update: ({ req: { user } }) => checkRole(['admin', 'media-manager'], user),
    delete: ({ req: { user } }) => checkRole(['admin', 'media-manager'], user),
  },
  fields: [
    {
      name: 'alt',
      type: 'text',
      required: true,
    },
    ...auditFields({
      visible: true
    }),
  ],
  upload: true,
}
