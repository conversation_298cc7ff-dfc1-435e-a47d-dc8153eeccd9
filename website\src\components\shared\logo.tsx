import { cn } from "@/lib/utils";
import React from "react";

interface LogoProps extends React.HTMLProps<HTMLDivElement> {
  variant?: "light" | "dark";
  withText?: boolean;
}

export default function Logo({
  variant = "light",
  withText = true,
  ...props
}: LogoProps) {
  return (
    <div className="flex items-center gap-2 max-w-60" {...props}>
      <img 
        src="/logo.svg" 
        alt="Academia Peruana de Doctores" 
        className={cn("w-20 aspect-square", variant === "dark" ? "invert" : "")} 
      />
      {withText && (
        <span
            className={cn(
              "text-xl font-bold",
              "font-(family-name:--font-playfair-display)",
              variant === "dark" ? "text-white" : "text-primary"
            )}
          >
            Academia Peruana de Doctores
          </span>
      )}
    </div>
  )
}