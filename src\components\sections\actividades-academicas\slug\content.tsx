'use client'
import { AcademicActivity, Speaker } from '@/payload-types'
import { FadeUp } from '@/components/motion'
import { type MotionTab, MotionTabs } from '@/components/ui/motion/motion-tabs'
import { title } from 'process'
import SpeakersTab from './tabs/speakers-tab'

interface AcademicActivityContentProps {
  activity: AcademicActivity
}

export default function AcademicActivityContent(
  { activity }: AcademicActivityContentProps
) {
  const agendaBlock = activity.eventBlocks?.find(block => block.blockType === 'agenda')
  const speakersBlock = activity.eventBlocks?.find(block => block.blockType === 'speakers')
  const requirementsBlock = activity.eventBlocks?.find(block => block.blockType === 'requirements')

  const speakers = speakersBlock?.speakers as Speaker[] || []

  const tabs: MotionTab[] = [
    {
      title: "Información general",
      value: "informacion-general",
      content: (
        <div>
          Contenido
        </div>
      )
    },
    ...(agendaBlock ? [{
      title: agendaBlock.blockName || "Agenda",
      value: "agenda",
      content: (<div>
        {JSON.stringify(agendaBlock.items)}
      </div>)
    }] : []),
    ...(speakersBlock && speakers.length > 0 ? [{
      title: speakersBlock.blockName || "Ponentes",
      value: "ponentes",
      content: (<div>
        <SpeakersTab speakers={speakers} />
      </div>)
    }] : []),
    ...(requirementsBlock ? [{
      title: requirementsBlock.blockName || "Requisitos",
      value: "requisitos",
      content: (<div>
        {JSON.stringify(requirementsBlock.items)}
      </div>)
    }] : [])
  ]

  return (
    <div className="h-[20rem] md:h-[40rem] [perspective:1000px] relative b flex flex-col max-w-5xl mx-auto w-full items-start justify-start">
      <MotionTabs tabs={tabs} />
    </div>
  )
}

// export default function AcademicActivityContent({ activity }: AcademicActivityContentProps) {
//   return (
//     <div className="space-y-6">
//       {/* Excerpt/Resumen */}
//       {activity.excerpt && (
//         <FadeUp>
//           <div className="text-lg text-muted-foreground leading-relaxed">
//             {activity.excerpt}
//           </div>
//         </FadeUp>
//       )}

//       {/* Contenido Principal */}
//       {activity.content && (
//         <FadeUp delay={0.1}>
//           <div
//             className="prose prose-sm max-w-none prose-headings:text-primary prose-a:text-primary hover:prose-a:text-primary/80"
//             dangerouslySetInnerHTML={{ __html: activity.content }}
//           />
//         </FadeUp>
//       )}
//     </div>
//   )
// }
