import { checkRole } from '@/backend/access/utils'
import { GROUPS } from '@/backend/constants/groups'
import type { CollectionConfig } from 'payload'

export const AcademicActivityCategories: CollectionConfig = {
  slug: 'academic-activity-categories',
  dbName: 'actCategories',
  labels: {
    singular: 'Tipo',
    plural: 'Tipos',
  },
  admin: {
    useAsTitle: 'name',
    group: GROUPS.EVENTS,
    description: 'Categorías para clasificar las actividades académicas',
  },
  access: {
    read: () => true,
    create: ({ req: { user } }) => checkRole(['admin', 'activities-coordinator'], user),
    update: ({ req: { user } }) => checkRole(['admin', 'activities-coordinator'], user),
    delete: ({ req: { user } }) => checkRole(['admin'], user),
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Nombre',
      admin: {
        description: 'Nombre de la categoría',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Descripción',
      admin: {
        description: 'Descripción opcional de la categoría',
      },
    },
    {
      name: 'color',
      type: 'text',
      label: 'Color',
      admin: {
        description: 'Color hexadecimal para identificar la categoría (ej: #FF5733)',
      },
    },
    {
      name: 'active',
      type: 'checkbox',
      label: 'Activa',
      defaultValue: true,
      admin: {
        description: 'Determina si esta categoría está disponible para usar',
      },
    },
  ],
}
