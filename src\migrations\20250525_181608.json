{"id": "36d3bae0-1686-4dcc-b842-1d21177f1e83", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.users_roles": {"name": "users_roles", "schema": "", "columns": {"order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "enum_users_roles", "typeSchema": "public", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}}, "indexes": {"users_roles_order_idx": {"name": "users_roles_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_roles_parent_idx": {"name": "users_roles_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"users_roles_parent_fk": {"name": "users_roles_parent_fk", "tableFrom": "users_roles", "tableTo": "users", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "reset_password_token": {"name": "reset_password_token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "reset_password_expiration": {"name": "reset_password_expiration", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "salt": {"name": "salt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "hash": {"name": "hash", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "login_attempts": {"name": "login_attempts", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "lock_until": {"name": "lock_until", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"users_updated_at_idx": {"name": "users_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_created_at_idx": {"name": "users_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.home_carousel_buttons": {"name": "home_carousel_buttons", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "text": {"name": "text", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "href": {"name": "href", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "icon": {"name": "icon", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "variant": {"name": "variant", "type": "enum_home_carousel_buttons_variant", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'default'"}, "size": {"name": "size", "type": "enum_home_carousel_buttons_size", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'default'"}}, "indexes": {"home_carousel_buttons_order_idx": {"name": "home_carousel_buttons_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "home_carousel_buttons_parent_id_idx": {"name": "home_carousel_buttons_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"home_carousel_buttons_parent_id_fk": {"name": "home_carousel_buttons_parent_id_fk", "tableFrom": "home_carousel_buttons", "tableTo": "home_carousel", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.home_carousel": {"name": "home_carousel", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "image_id": {"name": "image_id", "type": "integer", "primaryKey": false, "notNull": true}, "order": {"name": "order", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"home_carousel_image_idx": {"name": "home_carousel_image_idx", "columns": [{"expression": "image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "home_carousel_updated_at_idx": {"name": "home_carousel_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "home_carousel_created_at_idx": {"name": "home_carousel_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"home_carousel_image_id_home_carousel_media_id_fk": {"name": "home_carousel_image_id_home_carousel_media_id_fk", "tableFrom": "home_carousel", "tableTo": "home_carousel_media", "columnsFrom": ["image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.home_carousel_media": {"name": "home_carousel_media", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "alt": {"name": "alt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "thumbnail_u_r_l": {"name": "thumbnail_u_r_l", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filename": {"name": "filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filesize": {"name": "filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false}, "height": {"name": "height", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_x": {"name": "focal_x", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_y": {"name": "focal_y", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_url": {"name": "sizes_thumbnail_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_thumbnail_width": {"name": "sizes_thumbnail_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_height": {"name": "sizes_thumbnail_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_mime_type": {"name": "sizes_thumbnail_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_thumbnail_filesize": {"name": "sizes_thumbnail_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_filename": {"name": "sizes_thumbnail_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_desktop_url": {"name": "sizes_desktop_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_desktop_width": {"name": "sizes_desktop_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_desktop_height": {"name": "sizes_desktop_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_desktop_mime_type": {"name": "sizes_desktop_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_desktop_filesize": {"name": "sizes_desktop_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_desktop_filename": {"name": "sizes_desktop_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"home_carousel_media_updated_at_idx": {"name": "home_carousel_media_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "home_carousel_media_created_at_idx": {"name": "home_carousel_media_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "home_carousel_media_filename_idx": {"name": "home_carousel_media_filename_idx", "columns": [{"expression": "filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "home_carousel_media_sizes_thumbnail_sizes_thumbnail_filename_idx": {"name": "home_carousel_media_sizes_thumbnail_sizes_thumbnail_filename_idx", "columns": [{"expression": "sizes_thumbnail_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "home_carousel_media_sizes_desktop_sizes_desktop_filename_idx": {"name": "home_carousel_media_sizes_desktop_sizes_desktop_filename_idx", "columns": [{"expression": "sizes_desktop_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.media": {"name": "media", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "alt": {"name": "alt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "thumbnail_u_r_l": {"name": "thumbnail_u_r_l", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filename": {"name": "filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filesize": {"name": "filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false}, "height": {"name": "height", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_x": {"name": "focal_x", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_y": {"name": "focal_y", "type": "numeric", "primaryKey": false, "notNull": false}}, "indexes": {"media_updated_at_idx": {"name": "media_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_created_at_idx": {"name": "media_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_filename_idx": {"name": "media_filename_idx", "columns": [{"expression": "filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.historia_list_images": {"name": "historia_list_images", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "image_id": {"name": "image_id", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {"historia_list_images_order_idx": {"name": "historia_list_images_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "historia_list_images_parent_id_idx": {"name": "historia_list_images_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "historia_list_images_image_idx": {"name": "historia_list_images_image_idx", "columns": [{"expression": "image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"historia_list_images_image_id_media_id_fk": {"name": "historia_list_images_image_id_media_id_fk", "tableFrom": "historia_list_images", "tableTo": "media", "columnsFrom": ["image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "historia_list_images_parent_id_fk": {"name": "historia_list_images_parent_id_fk", "tableFrom": "historia_list_images", "tableTo": "historia_list", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.historia_list": {"name": "historia_list", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "year": {"name": "year", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "resume": {"name": "resume", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "jsonb", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"historia_list_updated_at_idx": {"name": "historia_list_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "historia_list_created_at_idx": {"name": "historia_list_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_locked_documents": {"name": "payload_locked_documents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "global_slug": {"name": "global_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_locked_documents_global_slug_idx": {"name": "payload_locked_documents_global_slug_idx", "columns": [{"expression": "global_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_updated_at_idx": {"name": "payload_locked_documents_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_created_at_idx": {"name": "payload_locked_documents_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_locked_documents_rels": {"name": "payload_locked_documents_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false}, "home_carousel_id": {"name": "home_carousel_id", "type": "integer", "primaryKey": false, "notNull": false}, "home_carousel_media_id": {"name": "home_carousel_media_id", "type": "integer", "primaryKey": false, "notNull": false}, "media_id": {"name": "media_id", "type": "integer", "primaryKey": false, "notNull": false}, "historia_list_id": {"name": "historia_list_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"payload_locked_documents_rels_order_idx": {"name": "payload_locked_documents_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_parent_idx": {"name": "payload_locked_documents_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_path_idx": {"name": "payload_locked_documents_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_users_id_idx": {"name": "payload_locked_documents_rels_users_id_idx", "columns": [{"expression": "users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_home_carousel_id_idx": {"name": "payload_locked_documents_rels_home_carousel_id_idx", "columns": [{"expression": "home_carousel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_home_carousel_media_id_idx": {"name": "payload_locked_documents_rels_home_carousel_media_id_idx", "columns": [{"expression": "home_carousel_media_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_media_id_idx": {"name": "payload_locked_documents_rels_media_id_idx", "columns": [{"expression": "media_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_historia_list_id_idx": {"name": "payload_locked_documents_rels_historia_list_id_idx", "columns": [{"expression": "historia_list_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_locked_documents_rels_parent_fk": {"name": "payload_locked_documents_rels_parent_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "payload_locked_documents", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_users_fk": {"name": "payload_locked_documents_rels_users_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "users", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_home_carousel_fk": {"name": "payload_locked_documents_rels_home_carousel_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "home_carousel", "columnsFrom": ["home_carousel_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_home_carousel_media_fk": {"name": "payload_locked_documents_rels_home_carousel_media_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "home_carousel_media", "columnsFrom": ["home_carousel_media_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_media_fk": {"name": "payload_locked_documents_rels_media_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "media", "columnsFrom": ["media_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_historia_list_fk": {"name": "payload_locked_documents_rels_historia_list_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "historia_list", "columnsFrom": ["historia_list_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_preferences": {"name": "payload_preferences", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "jsonb", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_preferences_key_idx": {"name": "payload_preferences_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_updated_at_idx": {"name": "payload_preferences_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_created_at_idx": {"name": "payload_preferences_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_preferences_rels": {"name": "payload_preferences_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"payload_preferences_rels_order_idx": {"name": "payload_preferences_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_parent_idx": {"name": "payload_preferences_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_path_idx": {"name": "payload_preferences_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_users_id_idx": {"name": "payload_preferences_rels_users_id_idx", "columns": [{"expression": "users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_preferences_rels_parent_fk": {"name": "payload_preferences_rels_parent_fk", "tableFrom": "payload_preferences_rels", "tableTo": "payload_preferences", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_preferences_rels_users_fk": {"name": "payload_preferences_rels_users_fk", "tableFrom": "payload_preferences_rels", "tableTo": "users", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_migrations": {"name": "payload_migrations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "batch": {"name": "batch", "type": "numeric", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_migrations_updated_at_idx": {"name": "payload_migrations_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_migrations_created_at_idx": {"name": "payload_migrations_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.home_about_us_buttons": {"name": "home_about_us_buttons", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "text": {"name": "text", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "href": {"name": "href", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "icon": {"name": "icon", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "variant": {"name": "variant", "type": "enum_home_about_us_buttons_variant", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'default'"}, "size": {"name": "size", "type": "enum_home_about_us_buttons_size", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'default'"}}, "indexes": {"home_about_us_buttons_order_idx": {"name": "home_about_us_buttons_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "home_about_us_buttons_parent_id_idx": {"name": "home_about_us_buttons_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"home_about_us_buttons_parent_id_fk": {"name": "home_about_us_buttons_parent_id_fk", "tableFrom": "home_about_us_buttons", "tableTo": "home_about_us", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.home_about_us_highlights": {"name": "home_about_us_highlights", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "number": {"name": "number", "type": "numeric", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "plus": {"name": "plus", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {"home_about_us_highlights_order_idx": {"name": "home_about_us_highlights_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "home_about_us_highlights_parent_id_idx": {"name": "home_about_us_highlights_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"home_about_us_highlights_parent_id_fk": {"name": "home_about_us_highlights_parent_id_fk", "tableFrom": "home_about_us_highlights", "tableTo": "home_about_us", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.home_about_us_gallery": {"name": "home_about_us_gallery", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "image_id": {"name": "image_id", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {"home_about_us_gallery_order_idx": {"name": "home_about_us_gallery_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "home_about_us_gallery_parent_id_idx": {"name": "home_about_us_gallery_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "home_about_us_gallery_image_idx": {"name": "home_about_us_gallery_image_idx", "columns": [{"expression": "image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"home_about_us_gallery_image_id_media_id_fk": {"name": "home_about_us_gallery_image_id_media_id_fk", "tableFrom": "home_about_us_gallery", "tableTo": "media", "columnsFrom": ["image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "home_about_us_gallery_parent_id_fk": {"name": "home_about_us_gallery_parent_id_fk", "tableFrom": "home_about_us_gallery", "tableTo": "home_about_us", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.home_about_us": {"name": "home_about_us", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "subtitle": {"name": "subtitle", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.work_areas_items": {"name": "work_areas_items", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "icon": {"name": "icon", "type": "enum_work_areas_items_icon", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'home'"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"work_areas_items_order_idx": {"name": "work_areas_items_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "work_areas_items_parent_id_idx": {"name": "work_areas_items_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"work_areas_items_parent_id_fk": {"name": "work_areas_items_parent_id_fk", "tableFrom": "work_areas_items", "tableTo": "work_areas", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.work_areas": {"name": "work_areas", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "background_image_id": {"name": "background_image_id", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"work_areas_background_image_idx": {"name": "work_areas_background_image_idx", "columns": [{"expression": "background_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"work_areas_background_image_id_media_id_fk": {"name": "work_areas_background_image_id_media_id_fk", "tableFrom": "work_areas", "tableTo": "media", "columnsFrom": ["background_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.about_us_page_banner": {"name": "about_us_page_banner", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "banner_id": {"name": "banner_id", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"about_us_page_banner_banner_idx": {"name": "about_us_page_banner_banner_idx", "columns": [{"expression": "banner_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"about_us_page_banner_banner_id_media_id_fk": {"name": "about_us_page_banner_banner_id_media_id_fk", "tableFrom": "about_us_page_banner", "tableTo": "media", "columnsFrom": ["banner_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.about_us_page_historia_resumen": {"name": "about_us_page_historia_resumen", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "pre_title": {"name": "pre_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "historia": {"name": "historia", "type": "jsonb", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.about_us_page_mision_vision": {"name": "about_us_page_mision_vision", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "mision": {"name": "mision", "type": "jsonb", "primaryKey": false, "notNull": true}, "vision": {"name": "vision", "type": "jsonb", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.enum_users_roles": {"name": "enum_users_roles", "schema": "public", "values": ["admin"]}, "public.enum_home_carousel_buttons_variant": {"name": "enum_home_carousel_buttons_variant", "schema": "public", "values": ["white", "secondary", "default", "link", "destructive"]}, "public.enum_home_carousel_buttons_size": {"name": "enum_home_carousel_buttons_size", "schema": "public", "values": ["default", "sm", "lg"]}, "public.enum_home_about_us_buttons_variant": {"name": "enum_home_about_us_buttons_variant", "schema": "public", "values": ["white", "secondary", "default", "link", "destructive"]}, "public.enum_home_about_us_buttons_size": {"name": "enum_home_about_us_buttons_size", "schema": "public", "values": ["default", "sm", "lg"]}, "public.enum_work_areas_items_icon": {"name": "enum_work_areas_items_icon", "schema": "public", "values": ["a-arrow-down", "a-arrow-down-icon", "a-arrow-up", "a-arrow-up-icon", "a-large-small", "a-large-small-icon", "accessibility", "accessibility-icon", "activity", "activity-icon", "activity-square", "activity-square-icon", "air-vent", "air-vent-icon", "airplay", "airplay-icon", "alarm-check", "alarm-check-icon", "alarm-clock", "alarm-clock-check", "alarm-clock-check-icon", "alarm-clock-icon", "alarm-clock-minus", "alarm-clock-minus-icon", "alarm-clock-off", "alarm-clock-off-icon", "alarm-clock-plus", "alarm-clock-plus-icon", "alarm-minus", "alarm-minus-icon", "alarm-plus", "alarm-plus-icon", "alarm-smoke", "alarm-smoke-icon", "album", "album-icon", "alert-circle", "alert-circle-icon", "alert-octagon", "alert-octagon-icon", "alert-triangle", "alert-triangle-icon", "align-center", "align-center-horizontal", "align-center-horizontal-icon", "align-center-icon", "align-center-vertical", "align-center-vertical-icon", "align-end-horizontal", "align-end-horizontal-icon", "align-end-vertical", "align-end-vertical-icon", "align-horizontal-distribute-center", "align-horizontal-distribute-center-icon", "align-horizontal-distribute-end", "align-horizontal-distribute-end-icon", "align-horizontal-distribute-start", "align-horizontal-distribute-start-icon", "align-horizontal-justify-center", "align-horizontal-justify-center-icon", "align-horizontal-justify-end", "align-horizontal-justify-end-icon", "align-horizontal-justify-start", "align-horizontal-justify-start-icon", "align-horizontal-space-around", "align-horizontal-space-around-icon", "align-horizontal-space-between", "align-horizontal-space-between-icon", "align-justify", "align-justify-icon", "align-left", "align-left-icon", "align-right", "align-right-icon", "align-start-horizontal", "align-start-horizontal-icon", "align-start-vertical", "align-start-vertical-icon", "align-vertical-distribute-center", "align-vertical-distribute-center-icon", "align-vertical-distribute-end", "align-vertical-distribute-end-icon", "align-vertical-distribute-start", "align-vertical-distribute-start-icon", "align-vertical-justify-center", "align-vertical-justify-center-icon", "align-vertical-justify-end", "align-vertical-justify-end-icon", "align-vertical-justify-start", "align-vertical-justify-start-icon", "align-vertical-space-around", "align-vertical-space-around-icon", "align-vertical-space-between", "align-vertical-space-between-icon", "ambulance", "ambulance-icon", "ampersand", "ampersand-icon", "ampersands", "ampersands-icon", "amphora", "amphora-icon", "anchor", "anchor-icon", "angry", "angry-icon", "annoyed", "annoyed-icon", "antenna", "antenna-icon", "anvil", "anvil-icon", "aperture", "aperture-icon", "app-window", "app-window-icon", "app-window-mac", "app-window-mac-icon", "apple", "apple-icon", "archive", "archive-icon", "archive-restore", "archive-restore-icon", "archive-x", "archive-x-icon", "area-chart", "area-chart-icon", "armchair", "armchair-icon", "arrow-big-down", "arrow-big-down-dash", "arrow-big-down-dash-icon", "arrow-big-down-icon", "arrow-big-left", "arrow-big-left-dash", "arrow-big-left-dash-icon", "arrow-big-left-icon", "arrow-big-right", "arrow-big-right-dash", "arrow-big-right-dash-icon", "arrow-big-right-icon", "arrow-big-up", "arrow-big-up-dash", "arrow-big-up-dash-icon", "arrow-big-up-icon", "arrow-down", "arrow-down01", "arrow-down01-icon", "arrow-down10", "arrow-down10-icon", "arrow-down-az", "arrow-down-az-icon", "arrow-down-az", "arrow-down-az-icon", "arrow-down-circle", "arrow-down-circle-icon", "arrow-down-from-line", "arrow-down-from-line-icon", "arrow-down-icon", "arrow-down-left", "arrow-down-left-from-circle", "arrow-down-left-from-circle-icon", "arrow-down-left-from-square", "arrow-down-left-from-square-icon", "arrow-down-left-icon", "arrow-down-left-square", "arrow-down-left-square-icon", "arrow-down-narrow-wide", "arrow-down-narrow-wide-icon", "arrow-down-right", "arrow-down-right-from-circle", "arrow-down-right-from-circle-icon", "arrow-down-right-from-square", "arrow-down-right-from-square-icon", "arrow-down-right-icon", "arrow-down-right-square", "arrow-down-right-square-icon", "arrow-down-square", "arrow-down-square-icon", "arrow-down-to-dot", "arrow-down-to-dot-icon", "arrow-down-to-line", "arrow-down-to-line-icon", "arrow-down-up", "arrow-down-up-icon", "arrow-down-wide-narrow", "arrow-down-wide-narrow-icon", "arrow-down-za", "arrow-down-za-icon", "arrow-down-za", "arrow-down-za-icon", "arrow-left", "arrow-left-circle", "arrow-left-circle-icon", "arrow-left-from-line", "arrow-left-from-line-icon", "arrow-left-icon", "arrow-left-right", "arrow-left-right-icon", "arrow-left-square", "arrow-left-square-icon", "arrow-left-to-line", "arrow-left-to-line-icon", "arrow-right", "arrow-right-circle", "arrow-right-circle-icon", "arrow-right-from-line", "arrow-right-from-line-icon", "arrow-right-icon", "arrow-right-left", "arrow-right-left-icon", "arrow-right-square", "arrow-right-square-icon", "arrow-right-to-line", "arrow-right-to-line-icon", "arrow-up", "arrow-up01", "arrow-up01-icon", "arrow-up10", "arrow-up10-icon", "arrow-up-az", "arrow-up-az-icon", "arrow-up-az", "arrow-up-az-icon", "arrow-up-circle", "arrow-up-circle-icon", "arrow-up-down", "arrow-up-down-icon", "arrow-up-from-dot", "arrow-up-from-dot-icon", "arrow-up-from-line", "arrow-up-from-line-icon", "arrow-up-icon", "arrow-up-left", "arrow-up-left-from-circle", "arrow-up-left-from-circle-icon", "arrow-up-left-from-square", "arrow-up-left-from-square-icon", "arrow-up-left-icon", "arrow-up-left-square", "arrow-up-left-square-icon", "arrow-up-narrow-wide", "arrow-up-narrow-wide-icon", "arrow-up-right", "arrow-up-right-from-circle", "arrow-up-right-from-circle-icon", "arrow-up-right-from-square", "arrow-up-right-from-square-icon", "arrow-up-right-icon", "arrow-up-right-square", "arrow-up-right-square-icon", "arrow-up-square", "arrow-up-square-icon", "arrow-up-to-line", "arrow-up-to-line-icon", "arrow-up-wide-narrow", "arrow-up-wide-narrow-icon", "arrow-up-za", "arrow-up-za-icon", "arrow-up-za", "arrow-up-za-icon", "arrows-up-from-line", "arrows-up-from-line-icon", "asterisk", "asterisk-icon", "asterisk-square", "asterisk-square-icon", "at-sign", "at-sign-icon", "atom", "atom-icon", "audio-lines", "audio-lines-icon", "audio-waveform", "audio-waveform-icon", "award", "award-icon", "axe", "axe-icon", "axis3-d", "axis3-d-icon", "axis3d", "axis3d-icon", "baby", "baby-icon", "backpack", "backpack-icon", "badge", "badge-alert", "badge-alert-icon", "badge-cent", "badge-cent-icon", "badge-check", "badge-check-icon", "badge-dollar-sign", "badge-dollar-sign-icon", "badge-euro", "badge-euro-icon", "badge-help", "badge-help-icon", "badge-icon", "badge-indian-rupee", "badge-indian-rupee-icon", "badge-info", "badge-info-icon", "badge-japanese-yen", "badge-japanese-yen-icon", "badge-minus", "badge-minus-icon", "badge-percent", "badge-percent-icon", "badge-plus", "badge-plus-icon", "badge-pound-sterling", "badge-pound-sterling-icon", "badge-russian-ruble", "badge-russian-ruble-icon", "badge-swiss-franc", "badge-swiss-franc-icon", "badge-x", "badge-x-icon", "baggage-claim", "baggage-claim-icon", "ban", "ban-icon", "banana", "banana-icon", "bandage", "bandage-icon", "banknote", "banknote-arrow-down", "banknote-arrow-down-icon", "banknote-arrow-up", "banknote-arrow-up-icon", "banknote-icon", "banknote-x", "banknote-x-icon", "bar-chart", "bar-chart2", "bar-chart2-icon", "bar-chart3", "bar-chart3-icon", "bar-chart4", "bar-chart4-icon", "bar-chart-big", "bar-chart-big-icon", "bar-chart-horizontal", "bar-chart-horizontal-big", "bar-chart-horizontal-big-icon", "bar-chart-horizontal-icon", "bar-chart-icon", "barcode", "barcode-icon", "baseline", "baseline-icon", "bath", "bath-icon", "battery", "battery-charging", "battery-charging-icon", "battery-full", "battery-full-icon", "battery-icon", "battery-low", "battery-low-icon", "battery-medium", "battery-medium-icon", "battery-plus", "battery-plus-icon", "battery-warning", "battery-warning-icon", "beaker", "beaker-icon", "bean", "bean-icon", "bean-off", "bean-off-icon", "bed", "bed-double", "bed-double-icon", "bed-icon", "bed-single", "bed-single-icon", "beef", "beef-icon", "beer", "beer-icon", "beer-off", "beer-off-icon", "bell", "bell-dot", "bell-dot-icon", "bell-electric", "bell-electric-icon", "bell-icon", "bell-minus", "bell-minus-icon", "bell-off", "bell-off-icon", "bell-plus", "bell-plus-icon", "bell-ring", "bell-ring-icon", "between-horizonal-end", "between-horizonal-end-icon", "between-horizonal-start", "between-horizonal-start-icon", "between-horizontal-end", "between-horizontal-end-icon", "between-horizontal-start", "between-horizontal-start-icon", "between-vertical-end", "between-vertical-end-icon", "between-vertical-start", "between-vertical-start-icon", "biceps-flexed", "biceps-flexed-icon", "bike", "bike-icon", "binary", "binary-icon", "binoculars", "binoculars-icon", "biohazard", "biohazard-icon", "bird", "bird-icon", "bitcoin", "bitcoin-icon", "blend", "blend-icon", "blinds", "blinds-icon", "blocks", "blocks-icon", "bluetooth", "bluetooth-connected", "bluetooth-connected-icon", "bluetooth-icon", "bluetooth-off", "bluetooth-off-icon", "bluetooth-searching", "bluetooth-searching-icon", "bold", "bold-icon", "bolt", "bolt-icon", "bomb", "bomb-icon", "bone", "bone-icon", "book", "book-a", "book-a-icon", "book-audio", "book-audio-icon", "book-check", "book-check-icon", "book-copy", "book-copy-icon", "book-dashed", "book-dashed-icon", "book-down", "book-down-icon", "book-headphones", "book-headphones-icon", "book-heart", "book-heart-icon", "book-icon", "book-image", "book-image-icon", "book-key", "book-key-icon", "book-lock", "book-lock-icon", "book-marked", "book-marked-icon", "book-minus", "book-minus-icon", "book-open", "book-open-check", "book-open-check-icon", "book-open-icon", "book-open-text", "book-open-text-icon", "book-plus", "book-plus-icon", "book-template", "book-template-icon", "book-text", "book-text-icon", "book-type", "book-type-icon", "book-up", "book-up2", "book-up2-icon", "book-up-icon", "book-user", "book-user-icon", "book-x", "book-x-icon", "bookmark", "bookmark-check", "bookmark-check-icon", "bookmark-icon", "bookmark-minus", "bookmark-minus-icon", "bookmark-plus", "bookmark-plus-icon", "bookmark-x", "bookmark-x-icon", "boom-box", "boom-box-icon", "bot", "bot-icon", "bot-message-square", "bot-message-square-icon", "bot-off", "bot-off-icon", "bow-arrow", "bow-arrow-icon", "box", "box-icon", "box-select", "box-select-icon", "boxes", "boxes-icon", "braces", "braces-icon", "brackets", "brackets-icon", "brain", "brain-circuit", "brain-circuit-icon", "brain-cog", "brain-cog-icon", "brain-icon", "brick-wall", "brick-wall-fire", "brick-wall-fire-icon", "brick-wall-icon", "briefcase", "briefcase-business", "briefcase-business-icon", "briefcase-conveyor-belt", "briefcase-conveyor-belt-icon", "briefcase-icon", "briefcase-medical", "briefcase-medical-icon", "bring-to-front", "bring-to-front-icon", "brush", "brush-cleaning", "brush-cleaning-icon", "brush-icon", "bubbles", "bubbles-icon", "bug", "bug-icon", "bug-off", "bug-off-icon", "bug-play", "bug-play-icon", "building", "building2", "building2-icon", "building-icon", "bus", "bus-front", "bus-front-icon", "bus-icon", "cable", "cable-car", "cable-car-icon", "cable-icon", "cake", "cake-icon", "cake-slice", "cake-slice-icon", "calculator", "calculator-icon", "calendar", "calendar1", "calendar1-icon", "calendar-arrow-down", "calendar-arrow-down-icon", "calendar-arrow-up", "calendar-arrow-up-icon", "calendar-check", "calendar-check2", "calendar-check2-icon", "calendar-check-icon", "calendar-clock", "calendar-clock-icon", "calendar-cog", "calendar-cog-icon", "calendar-days", "calendar-days-icon", "calendar-fold", "calendar-fold-icon", "calendar-heart", "calendar-heart-icon", "calendar-icon", "calendar-minus", "calendar-minus2", "calendar-minus2-icon", "calendar-minus-icon", "calendar-off", "calendar-off-icon", "calendar-plus", "calendar-plus2", "calendar-plus2-icon", "calendar-plus-icon", "calendar-range", "calendar-range-icon", "calendar-search", "calendar-search-icon", "calendar-sync", "calendar-sync-icon", "calendar-x", "calendar-x2", "calendar-x2-icon", "calendar-x-icon", "camera", "camera-icon", "camera-off", "camera-off-icon", "candlestick-chart", "candlestick-chart-icon", "candy", "candy-cane", "candy-cane-icon", "candy-icon", "candy-off", "candy-off-icon", "cannabis", "cannabis-icon", "captions", "captions-icon", "captions-off", "captions-off-icon", "car", "car-front", "car-front-icon", "car-icon", "car-taxi-front", "car-taxi-front-icon", "caravan", "caravan-icon", "carrot", "carrot-icon", "case-lower", "case-lower-icon", "case-sensitive", "case-sensitive-icon", "case-upper", "case-upper-icon", "cassette-tape", "cassette-tape-icon", "cast", "cast-icon", "castle", "castle-icon", "cat", "cat-icon", "cctv", "cctv-icon", "chart-area", "chart-area-icon", "chart-bar", "chart-bar-big", "chart-bar-big-icon", "chart-bar-decreasing", "chart-bar-decreasing-icon", "chart-bar-icon", "chart-bar-increasing", "chart-bar-increasing-icon", "chart-bar-stacked", "chart-bar-stacked-icon", "chart-candlestick", "chart-candlestick-icon", "chart-column", "chart-column-big", "chart-column-big-icon", "chart-column-decreasing", "chart-column-decreasing-icon", "chart-column-icon", "chart-column-increasing", "chart-column-increasing-icon", "chart-column-stacked", "chart-column-stacked-icon", "chart-gantt", "chart-gantt-icon", "chart-line", "chart-line-icon", "chart-network", "chart-network-icon", "chart-no-axes-column", "chart-no-axes-column-decreasing", "chart-no-axes-column-decreasing-icon", "chart-no-axes-column-icon", "chart-no-axes-column-increasing", "chart-no-axes-column-increasing-icon", "chart-no-axes-combined", "chart-no-axes-combined-icon", "chart-no-axes-gantt", "chart-no-axes-gantt-icon", "chart-pie", "chart-pie-icon", "chart-scatter", "chart-scatter-icon", "chart-spline", "chart-spline-icon", "check", "check-check", "check-check-icon", "check-circle", "check-circle2", "check-circle2-icon", "check-circle-icon", "check-icon", "check-line", "check-line-icon", "check-square", "check-square2", "check-square2-icon", "check-square-icon", "chef-hat", "chef-hat-icon", "cherry", "cherry-icon", "chevron-down", "chevron-down-circle", "chevron-down-circle-icon", "chevron-down-icon", "chevron-down-square", "chevron-down-square-icon", "chevron-first", "chevron-first-icon", "chevron-last", "chevron-last-icon", "chevron-left", "chevron-left-circle", "chevron-left-circle-icon", "chevron-left-icon", "chevron-left-square", "chevron-left-square-icon", "chevron-right", "chevron-right-circle", "chevron-right-circle-icon", "chevron-right-icon", "chevron-right-square", "chevron-right-square-icon", "chevron-up", "chevron-up-circle", "chevron-up-circle-icon", "chevron-up-icon", "chevron-up-square", "chevron-up-square-icon", "chevrons-down", "chevrons-down-icon", "chevrons-down-up", "chevrons-down-up-icon", "chevrons-left", "chevrons-left-icon", "chevrons-left-right", "chevrons-left-right-ellipsis", "chevrons-left-right-ellipsis-icon", "chevrons-left-right-icon", "chevrons-right", "chevrons-right-icon", "chevrons-right-left", "chevrons-right-left-icon", "chevrons-up", "chevrons-up-down", "chevrons-up-down-icon", "chevrons-up-icon", "chrome", "chrome-icon", "church", "church-icon", "cigarette", "cigarette-icon", "cigarette-off", "cigarette-off-icon", "circle", "circle-alert", "circle-alert-icon", "circle-arrow-down", "circle-arrow-down-icon", "circle-arrow-left", "circle-arrow-left-icon", "circle-arrow-out-down-left", "circle-arrow-out-down-left-icon", "circle-arrow-out-down-right", "circle-arrow-out-down-right-icon", "circle-arrow-out-up-left", "circle-arrow-out-up-left-icon", "circle-arrow-out-up-right", "circle-arrow-out-up-right-icon", "circle-arrow-right", "circle-arrow-right-icon", "circle-arrow-up", "circle-arrow-up-icon", "circle-check", "circle-check-big", "circle-check-big-icon", "circle-check-icon", "circle-chevron-down", "circle-chevron-down-icon", "circle-chevron-left", "circle-chevron-left-icon", "circle-chevron-right", "circle-chevron-right-icon", "circle-chevron-up", "circle-chevron-up-icon", "circle-dashed", "circle-dashed-icon", "circle-divide", "circle-divide-icon", "circle-dollar-sign", "circle-dollar-sign-icon", "circle-dot", "circle-dot-dashed", "circle-dot-dashed-icon", "circle-dot-icon", "circle-ellipsis", "circle-ellipsis-icon", "circle-equal", "circle-equal-icon", "circle-fading-arrow-up", "circle-fading-arrow-up-icon", "circle-fading-plus", "circle-fading-plus-icon", "circle-gauge", "circle-gauge-icon", "circle-help", "circle-help-icon", "circle-icon", "circle-minus", "circle-minus-icon", "circle-off", "circle-off-icon", "circle-parking", "circle-parking-icon", "circle-parking-off", "circle-parking-off-icon", "circle-pause", "circle-pause-icon", "circle-percent", "circle-percent-icon", "circle-play", "circle-play-icon", "circle-plus", "circle-plus-icon", "circle-power", "circle-power-icon", "circle-slash", "circle-slash2", "circle-slash2-icon", "circle-slash-icon", "circle-slashed", "circle-slashed-icon", "circle-small", "circle-small-icon", "circle-stop", "circle-stop-icon", "circle-user", "circle-user-icon", "circle-user-round", "circle-user-round-icon", "circle-x", "circle-x-icon", "circuit-board", "circuit-board-icon", "citrus", "citrus-icon", "clapperboard", "clapperboard-icon", "clipboard", "clipboard-check", "clipboard-check-icon", "clipboard-copy", "clipboard-copy-icon", "clipboard-edit", "clipboard-edit-icon", "clipboard-icon", "clipboard-list", "clipboard-list-icon", "clipboard-minus", "clipboard-minus-icon", "clipboard-paste", "clipboard-paste-icon", "clipboard-pen", "clipboard-pen-icon", "clipboard-pen-line", "clipboard-pen-line-icon", "clipboard-plus", "clipboard-plus-icon", "clipboard-signature", "clipboard-signature-icon", "clipboard-type", "clipboard-type-icon", "clipboard-x", "clipboard-x-icon", "clock", "clock1", "clock10", "clock10-icon", "clock11", "clock11-icon", "clock12", "clock12-icon", "clock1-icon", "clock2", "clock2-icon", "clock3", "clock3-icon", "clock4", "clock4-icon", "clock5", "clock5-icon", "clock6", "clock6-icon", "clock7", "clock7-icon", "clock8", "clock8-icon", "clock9", "clock9-icon", "clock-alert", "clock-alert-icon", "clock-arrow-down", "clock-arrow-down-icon", "clock-arrow-up", "clock-arrow-up-icon", "clock-fading", "clock-fading-icon", "clock-icon", "clock-plus", "clock-plus-icon", "cloud", "cloud-alert", "cloud-alert-icon", "cloud-cog", "cloud-cog-icon", "cloud-download", "cloud-download-icon", "cloud-drizzle", "cloud-drizzle-icon", "cloud-fog", "cloud-fog-icon", "cloud-hail", "cloud-hail-icon", "cloud-icon", "cloud-lightning", "cloud-lightning-icon", "cloud-moon", "cloud-moon-icon", "cloud-moon-rain", "cloud-moon-rain-icon", "cloud-off", "cloud-off-icon", "cloud-rain", "cloud-rain-icon", "cloud-rain-wind", "cloud-rain-wind-icon", "cloud-snow", "cloud-snow-icon", "cloud-sun", "cloud-sun-icon", "cloud-sun-rain", "cloud-sun-rain-icon", "cloud-upload", "cloud-upload-icon", "cloudy", "cloudy-icon", "clover", "clover-icon", "club", "club-icon", "code", "code2", "code2-icon", "code-icon", "code-square", "code-square-icon", "code-xml", "code-xml-icon", "codepen", "codepen-icon", "codesandbox", "codesandbox-icon", "coffee", "coffee-icon", "cog", "cog-icon", "coins", "coins-icon", "columns", "columns2", "columns2-icon", "columns3", "columns3-cog", "columns3-cog-icon", "columns3-icon", "columns4", "columns4-icon", "columns-icon", "columns-settings", "columns-settings-icon", "combine", "combine-icon", "command", "command-icon", "compass", "compass-icon", "component", "component-icon", "computer", "computer-icon", "concierge-bell", "concierge-bell-icon", "cone", "cone-icon", "construction", "construction-icon", "contact", "contact2", "contact2-icon", "contact-icon", "contact-round", "contact-round-icon", "container", "container-icon", "contrast", "contrast-icon", "cookie", "cookie-icon", "cooking-pot", "cooking-pot-icon", "copy", "copy-check", "copy-check-icon", "copy-icon", "copy-minus", "copy-minus-icon", "copy-plus", "copy-plus-icon", "copy-slash", "copy-slash-icon", "copy-x", "copy-x-icon", "copyleft", "copyleft-icon", "copyright", "copyright-icon", "corner-down-left", "corner-down-left-icon", "corner-down-right", "corner-down-right-icon", "corner-left-down", "corner-left-down-icon", "corner-left-up", "corner-left-up-icon", "corner-right-down", "corner-right-down-icon", "corner-right-up", "corner-right-up-icon", "corner-up-left", "corner-up-left-icon", "corner-up-right", "corner-up-right-icon", "cpu", "cpu-icon", "creative-commons", "creative-commons-icon", "credit-card", "credit-card-icon", "croissant", "croissant-icon", "crop", "crop-icon", "cross", "cross-icon", "crosshair", "crosshair-icon", "crown", "crown-icon", "cuboid", "cuboid-icon", "cup-soda", "cup-soda-icon", "curly-braces", "curly-braces-icon", "currency", "currency-icon", "cylinder", "cylinder-icon", "dam", "dam-icon", "database", "database-backup", "database-backup-icon", "database-icon", "database-zap", "database-zap-icon", "decimals-arrow-left", "decimals-arrow-left-icon", "decimals-arrow-right", "decimals-arrow-right-icon", "delete", "delete-icon", "dessert", "dessert-icon", "diameter", "diameter-icon", "diamond", "diamond-icon", "diamond-minus", "diamond-minus-icon", "diamond-percent", "diamond-percent-icon", "diamond-plus", "diamond-plus-icon", "dice1", "dice1-icon", "dice2", "dice2-icon", "dice3", "dice3-icon", "dice4", "dice4-icon", "dice5", "dice5-icon", "dice6", "dice6-icon", "dices", "dices-icon", "diff", "diff-icon", "disc", "disc2", "disc2-icon", "disc3", "disc3-icon", "disc-album", "disc-album-icon", "disc-icon", "divide", "divide-circle", "divide-circle-icon", "divide-icon", "divide-square", "divide-square-icon", "dna", "dna-icon", "dna-off", "dna-off-icon", "dock", "dock-icon", "dog", "dog-icon", "dollar-sign", "dollar-sign-icon", "donut", "donut-icon", "door-closed", "door-closed-icon", "door-closed-locked", "door-closed-locked-icon", "door-open", "door-open-icon", "dot", "dot-icon", "dot-square", "dot-square-icon", "download", "download-cloud", "download-cloud-icon", "download-icon", "drafting-compass", "drafting-compass-icon", "drama", "drama-icon", "dribbble", "dribbble-icon", "drill", "drill-icon", "droplet", "droplet-icon", "droplet-off", "droplet-off-icon", "droplets", "droplets-icon", "drum", "drum-icon", "drumstick", "drumstick-icon", "dumbbell", "dumbbell-icon", "ear", "ear-icon", "ear-off", "ear-off-icon", "earth", "earth-icon", "earth-lock", "earth-lock-icon", "eclipse", "eclipse-icon", "edit", "edit2", "edit2-icon", "edit3", "edit3-icon", "edit-icon", "egg", "egg-fried", "egg-fried-icon", "egg-icon", "egg-off", "egg-off-icon", "ellipsis", "ellipsis-icon", "ellipsis-vertical", "ellipsis-vertical-icon", "equal", "equal-approximately", "equal-approximately-icon", "equal-icon", "equal-not", "equal-not-icon", "equal-square", "equal-square-icon", "eraser", "eraser-icon", "ethernet-port", "ethernet-port-icon", "euro", "euro-icon", "expand", "expand-icon", "external-link", "external-link-icon", "eye", "eye-closed", "eye-closed-icon", "eye-icon", "eye-off", "eye-off-icon", "facebook", "facebook-icon", "factory", "factory-icon", "fan", "fan-icon", "fast-forward", "fast-forward-icon", "feather", "feather-icon", "fence", "fence-icon", "ferris-wheel", "ferris-wheel-icon", "figma", "figma-icon", "file", "file-archive", "file-archive-icon", "file-audio", "file-audio2", "file-audio2-icon", "file-audio-icon", "file-axis3-d", "file-axis3-d-icon", "file-axis3d", "file-axis3d-icon", "file-badge", "file-badge2", "file-badge2-icon", "file-badge-icon", "file-bar-chart", "file-bar-chart2", "file-bar-chart2-icon", "file-bar-chart-icon", "file-box", "file-box-icon", "file-chart-column", "file-chart-column-icon", "file-chart-column-increasing", "file-chart-column-increasing-icon", "file-chart-line", "file-chart-line-icon", "file-chart-pie", "file-chart-pie-icon", "file-check", "file-check2", "file-check2-icon", "file-check-icon", "file-clock", "file-clock-icon", "file-code", "file-code2", "file-code2-icon", "file-code-icon", "file-cog", "file-cog2", "file-cog2-icon", "file-cog-icon", "file-diff", "file-diff-icon", "file-digit", "file-digit-icon", "file-down", "file-down-icon", "file-edit", "file-edit-icon", "file-heart", "file-heart-icon", "file-icon", "file-image", "file-image-icon", "file-input", "file-input-icon", "file-json", "file-json2", "file-json2-icon", "file-json-icon", "file-key", "file-key2", "file-key2-icon", "file-key-icon", "file-line-chart", "file-line-chart-icon", "file-lock", "file-lock2", "file-lock2-icon", "file-lock-icon", "file-minus", "file-minus2", "file-minus2-icon", "file-minus-icon", "file-music", "file-music-icon", "file-output", "file-output-icon", "file-pen", "file-pen-icon", "file-pen-line", "file-pen-line-icon", "file-pie-chart", "file-pie-chart-icon", "file-plus", "file-plus2", "file-plus2-icon", "file-plus-icon", "file-question", "file-question-icon", "file-scan", "file-scan-icon", "file-search", "file-search2", "file-search2-icon", "file-search-icon", "file-signature", "file-signature-icon", "file-sliders", "file-sliders-icon", "file-spreadsheet", "file-spreadsheet-icon", "file-stack", "file-stack-icon", "file-symlink", "file-symlink-icon", "file-terminal", "file-terminal-icon", "file-text", "file-text-icon", "file-type", "file-type2", "file-type2-icon", "file-type-icon", "file-up", "file-up-icon", "file-user", "file-user-icon", "file-video", "file-video2", "file-video2-icon", "file-video-icon", "file-volume", "file-volume2", "file-volume2-icon", "file-volume-icon", "file-warning", "file-warning-icon", "file-x", "file-x2", "file-x2-icon", "file-x-icon", "files", "files-icon", "film", "film-icon", "filter", "filter-icon", "filter-x", "filter-x-icon", "fingerprint", "fingerprint-icon", "fire-extinguisher", "fire-extinguisher-icon", "fish", "fish-icon", "fish-off", "fish-off-icon", "fish-symbol", "fish-symbol-icon", "flag", "flag-icon", "flag-off", "flag-off-icon", "flag-triangle-left", "flag-triangle-left-icon", "flag-triangle-right", "flag-triangle-right-icon", "flame", "flame-icon", "flame-kindling", "flame-kindling-icon", "flashlight", "flashlight-icon", "flashlight-off", "flashlight-off-icon", "flask-conical", "flask-conical-icon", "flask-conical-off", "flask-conical-off-icon", "flask-round", "flask-round-icon", "flip-horizontal", "flip-horizontal2", "flip-horizontal2-icon", "flip-horizontal-icon", "flip-vertical", "flip-vertical2", "flip-vertical2-icon", "flip-vertical-icon", "flower", "flower2", "flower2-icon", "flower-icon", "focus", "focus-icon", "fold-horizontal", "fold-horizontal-icon", "fold-vertical", "fold-vertical-icon", "folder", "folder-archive", "folder-archive-icon", "folder-check", "folder-check-icon", "folder-clock", "folder-clock-icon", "folder-closed", "folder-closed-icon", "folder-code", "folder-code-icon", "folder-cog", "folder-cog2", "folder-cog2-icon", "folder-cog-icon", "folder-dot", "folder-dot-icon", "folder-down", "folder-down-icon", "folder-edit", "folder-edit-icon", "folder-git", "folder-git2", "folder-git2-icon", "folder-git-icon", "folder-heart", "folder-heart-icon", "folder-icon", "folder-input", "folder-input-icon", "folder-kanban", "folder-kanban-icon", "folder-key", "folder-key-icon", "folder-lock", "folder-lock-icon", "folder-minus", "folder-minus-icon", "folder-open", "folder-open-dot", "folder-open-dot-icon", "folder-open-icon", "folder-output", "folder-output-icon", "folder-pen", "folder-pen-icon", "folder-plus", "folder-plus-icon", "folder-root", "folder-root-icon", "folder-search", "folder-search2", "folder-search2-icon", "folder-search-icon", "folder-symlink", "folder-symlink-icon", "folder-sync", "folder-sync-icon", "folder-tree", "folder-tree-icon", "folder-up", "folder-up-icon", "folder-x", "folder-x-icon", "folders", "folders-icon", "footprints", "footprints-icon", "fork-knife", "fork-knife-crossed", "fork-knife-crossed-icon", "fork-knife-icon", "forklift", "forklift-icon", "form-input", "form-input-icon", "forward", "forward-icon", "frame", "frame-icon", "framer", "framer-icon", "frown", "frown-icon", "fuel", "fuel-icon", "fullscreen", "fullscreen-icon", "function-square", "function-square-icon", "funnel", "funnel-icon", "funnel-plus", "funnel-plus-icon", "funnel-x", "funnel-x-icon", "gallery-horizontal", "gallery-horizontal-end", "gallery-horizontal-end-icon", "gallery-horizontal-icon", "gallery-thumbnails", "gallery-thumbnails-icon", "gallery-vertical", "gallery-vertical-end", "gallery-vertical-end-icon", "gallery-vertical-icon", "gamepad", "gamepad2", "gamepad2-icon", "gamepad-icon", "gantt-chart", "gantt-chart-icon", "gantt-chart-square", "gantt-chart-square-icon", "gauge", "gauge-circle", "gauge-circle-icon", "gauge-icon", "gavel", "gavel-icon", "gem", "gem-icon", "ghost", "ghost-icon", "gift", "gift-icon", "git-branch", "git-branch-icon", "git-branch-plus", "git-branch-plus-icon", "git-commit", "git-commit-horizontal", "git-commit-horizontal-icon", "git-commit-icon", "git-commit-vertical", "git-commit-vertical-icon", "git-compare", "git-compare-arrows", "git-compare-arrows-icon", "git-compare-icon", "git-fork", "git-fork-icon", "git-graph", "git-graph-icon", "git-merge", "git-merge-icon", "git-pull-request", "git-pull-request-arrow", "git-pull-request-arrow-icon", "git-pull-request-closed", "git-pull-request-closed-icon", "git-pull-request-create", "git-pull-request-create-arrow", "git-pull-request-create-arrow-icon", "git-pull-request-create-icon", "git-pull-request-draft", "git-pull-request-draft-icon", "git-pull-request-icon", "github", "github-icon", "gitlab", "gitlab-icon", "glass-water", "glass-water-icon", "glasses", "glasses-icon", "globe", "globe2", "globe2-icon", "globe-icon", "globe-lock", "globe-lock-icon", "goal", "goal-icon", "gpu", "gpu-icon", "grab", "grab-icon", "graduation-cap", "graduation-cap-icon", "grape", "grape-icon", "grid", "grid2-x2", "grid2-x2-check", "grid2-x2-check-icon", "grid2-x2-icon", "grid2-x2-plus", "grid2-x2-plus-icon", "grid2-x2-x", "grid2-x2-x-icon", "grid2x2", "grid2x2-check", "grid2x2-check-icon", "grid2x2-icon", "grid2x2-plus", "grid2x2-plus-icon", "grid2x2-x", "grid2x2-x-icon", "grid3-x3", "grid3-x3-icon", "grid3x3", "grid3x3-icon", "grid-icon", "grip", "grip-horizontal", "grip-horizontal-icon", "grip-icon", "grip-vertical", "grip-vertical-icon", "group", "group-icon", "guitar", "guitar-icon", "ham", "ham-icon", "hamburger", "hamburger-icon", "hammer", "hammer-icon", "hand", "hand-coins", "hand-coins-icon", "hand-heart", "hand-heart-icon", "hand-helping", "hand-helping-icon", "hand-icon", "hand-metal", "hand-metal-icon", "hand-platter", "hand-platter-icon", "handshake", "handshake-icon", "hard-drive", "hard-drive-download", "hard-drive-download-icon", "hard-drive-icon", "hard-drive-upload", "hard-drive-upload-icon", "hard-hat", "hard-hat-icon", "hash", "hash-icon", "haze", "haze-icon", "hdmi-port", "hdmi-port-icon", "heading", "heading1", "heading1-icon", "heading2", "heading2-icon", "heading3", "heading3-icon", "heading4", "heading4-icon", "heading5", "heading5-icon", "heading6", "heading6-icon", "heading-icon", "headphone-off", "headphone-off-icon", "headphones", "headphones-icon", "headset", "headset-icon", "heart", "heart-crack", "heart-crack-icon", "heart-handshake", "heart-handshake-icon", "heart-icon", "heart-minus", "heart-minus-icon", "heart-off", "heart-off-icon", "heart-plus", "heart-plus-icon", "heart-pulse", "heart-pulse-icon", "heater", "heater-icon", "help-circle", "help-circle-icon", "helping-hand", "helping-hand-icon", "hexagon", "hexagon-icon", "highlighter", "highlighter-icon", "history", "history-icon", "home", "home-icon", "hop", "hop-icon", "hop-off", "hop-off-icon", "hospital", "hospital-icon", "hotel", "hotel-icon", "hourglass", "hourglass-icon", "house", "house-icon", "house-plug", "house-plug-icon", "house-plus", "house-plus-icon", "house-wifi", "house-wifi-icon", "ice-cream", "ice-cream2", "ice-cream2-icon", "ice-cream-bowl", "ice-cream-bowl-icon", "ice-cream-cone", "ice-cream-cone-icon", "ice-cream-icon", "icon", "id-card", "id-card-icon", "image", "image-down", "image-down-icon", "image-icon", "image-minus", "image-minus-icon", "image-off", "image-off-icon", "image-play", "image-play-icon", "image-plus", "image-plus-icon", "image-up", "image-up-icon", "image-upscale", "image-upscale-icon", "images", "images-icon", "import", "import-icon", "inbox", "inbox-icon", "indent", "indent-decrease", "indent-decrease-icon", "indent-icon", "indent-increase", "indent-increase-icon", "indian-rupee", "indian-rupee-icon", "infinity", "infinity-icon", "info", "info-icon", "inspect", "inspect-icon", "inspection-panel", "inspection-panel-icon", "instagram", "instagram-icon", "italic", "italic-icon", "iteration-ccw", "iteration-ccw-icon", "iteration-cw", "iteration-cw-icon", "japanese-yen", "japanese-yen-icon", "joystick", "joystick-icon", "kanban", "kanban-icon", "kanban-square", "kanban-square-dashed", "kanban-square-dashed-icon", "kanban-square-icon", "key", "key-icon", "key-round", "key-round-icon", "key-square", "key-square-icon", "keyboard", "keyboard-icon", "keyboard-music", "keyboard-music-icon", "keyboard-off", "keyboard-off-icon", "lamp", "lamp-ceiling", "lamp-ceiling-icon", "lamp-desk", "lamp-desk-icon", "lamp-floor", "lamp-floor-icon", "lamp-icon", "lamp-wall-down", "lamp-wall-down-icon", "lamp-wall-up", "lamp-wall-up-icon", "land-plot", "land-plot-icon", "landmark", "landmark-icon", "languages", "languages-icon", "laptop", "laptop2", "laptop2-icon", "laptop-icon", "laptop-minimal", "laptop-minimal-check", "laptop-minimal-check-icon", "laptop-minimal-icon", "lasso", "lasso-icon", "lasso-select", "lasso-select-icon", "laugh", "laugh-icon", "layers", "layers2", "layers2-icon", "layers3", "layers3-icon", "layers-icon", "layout", "layout-dashboard", "layout-dashboard-icon", "layout-grid", "layout-grid-icon", "layout-icon", "layout-list", "layout-list-icon", "layout-panel-left", "layout-panel-left-icon", "layout-panel-top", "layout-panel-top-icon", "layout-template", "layout-template-icon", "leaf", "leaf-icon", "leafy-green", "leafy-green-icon", "lectern", "lectern-icon", "letter-text", "letter-text-icon", "library", "library-big", "library-big-icon", "library-icon", "library-square", "library-square-icon", "life-buoy", "life-buoy-icon", "ligature", "ligature-icon", "lightbulb", "lightbulb-icon", "lightbulb-off", "lightbulb-off-icon", "line-chart", "line-chart-icon", "link", "link2", "link2-icon", "link2-off", "link2-off-icon", "link-icon", "linkedin", "linkedin-icon", "list", "list-check", "list-check-icon", "list-checks", "list-checks-icon", "list-collapse", "list-collapse-icon", "list-end", "list-end-icon", "list-filter", "list-filter-icon", "list-filter-plus", "list-filter-plus-icon", "list-icon", "list-minus", "list-minus-icon", "list-music", "list-music-icon", "list-ordered", "list-ordered-icon", "list-plus", "list-plus-icon", "list-restart", "list-restart-icon", "list-start", "list-start-icon", "list-todo", "list-todo-icon", "list-tree", "list-tree-icon", "list-video", "list-video-icon", "list-x", "list-x-icon", "loader", "loader2", "loader2-icon", "loader-circle", "loader-circle-icon", "loader-icon", "loader-pinwheel", "loader-pinwheel-icon", "locate", "locate-fixed", "locate-fixed-icon", "locate-icon", "locate-off", "locate-off-icon", "location-edit", "location-edit-icon", "lock", "lock-icon", "lock-keyhole", "lock-keyhole-icon", "lock-keyhole-open", "lock-keyhole-open-icon", "lock-open", "lock-open-icon", "log-in", "log-in-icon", "log-out", "log-out-icon", "logs", "logs-icon", "lollipop", "lollipop-icon", "lucide-a-arrow-down", "lucide-a-arrow-up", "lucide-a-large-small", "lucide-accessibility", "lucide-activity", "lucide-activity-square", "lucide-air-vent", "lucide-airplay", "lucide-alarm-check", "lucide-alarm-clock", "lucide-alarm-clock-check", "lucide-alarm-clock-minus", "lucide-alarm-clock-off", "lucide-alarm-clock-plus", "lucide-alarm-minus", "lucide-alarm-plus", "lucide-alarm-smoke", "lucide-album", "lucide-alert-circle", "lucide-alert-octagon", "lucide-alert-triangle", "lucide-align-center", "lucide-align-center-horizontal", "lucide-align-center-vertical", "lucide-align-end-horizontal", "lucide-align-end-vertical", "lucide-align-horizontal-distribute-center", "lucide-align-horizontal-distribute-end", "lucide-align-horizontal-distribute-start", "lucide-align-horizontal-justify-center", "lucide-align-horizontal-justify-end", "lucide-align-horizontal-justify-start", "lucide-align-horizontal-space-around", "lucide-align-horizontal-space-between", "lucide-align-justify", "lucide-align-left", "lucide-align-right", "lucide-align-start-horizontal", "lucide-align-start-vertical", "lucide-align-vertical-distribute-center", "lucide-align-vertical-distribute-end", "lucide-align-vertical-distribute-start", "lucide-align-vertical-justify-center", "lucide-align-vertical-justify-end", "lucide-align-vertical-justify-start", "lucide-align-vertical-space-around", "lucide-align-vertical-space-between", "lucide-ambulance", "lucide-ampersand", "lucide-ampersands", "lucide-amphora", "lucide-anchor", "lucide-angry", "lucide-annoyed", "lucide-antenna", "lucide-anvil", "lucide-aperture", "lucide-app-window", "lucide-app-window-mac", "lucide-apple", "lucide-archive", "lucide-archive-restore", "lucide-archive-x", "lucide-area-chart", "lucide-armchair", "lucide-arrow-big-down", "lucide-arrow-big-down-dash", "lucide-arrow-big-left", "lucide-arrow-big-left-dash", "lucide-arrow-big-right", "lucide-arrow-big-right-dash", "lucide-arrow-big-up", "lucide-arrow-big-up-dash", "lucide-arrow-down", "lucide-arrow-down01", "lucide-arrow-down10", "lucide-arrow-down-az", "lucide-arrow-down-az", "lucide-arrow-down-circle", "lucide-arrow-down-from-line", "lucide-arrow-down-left", "lucide-arrow-down-left-from-circle", "lucide-arrow-down-left-from-square", "lucide-arrow-down-left-square", "lucide-arrow-down-narrow-wide", "lucide-arrow-down-right", "lucide-arrow-down-right-from-circle", "lucide-arrow-down-right-from-square", "lucide-arrow-down-right-square", "lucide-arrow-down-square", "lucide-arrow-down-to-dot", "lucide-arrow-down-to-line", "lucide-arrow-down-up", "lucide-arrow-down-wide-narrow", "lucide-arrow-down-za", "lucide-arrow-down-za", "lucide-arrow-left", "lucide-arrow-left-circle", "lucide-arrow-left-from-line", "lucide-arrow-left-right", "lucide-arrow-left-square", "lucide-arrow-left-to-line", "lucide-arrow-right", "lucide-arrow-right-circle", "lucide-arrow-right-from-line", "lucide-arrow-right-left", "lucide-arrow-right-square", "lucide-arrow-right-to-line", "lucide-arrow-up", "lucide-arrow-up01", "lucide-arrow-up10", "lucide-arrow-up-az", "lucide-arrow-up-az", "lucide-arrow-up-circle", "lucide-arrow-up-down", "lucide-arrow-up-from-dot", "lucide-arrow-up-from-line", "lucide-arrow-up-left", "lucide-arrow-up-left-from-circle", "lucide-arrow-up-left-from-square", "lucide-arrow-up-left-square", "lucide-arrow-up-narrow-wide", "lucide-arrow-up-right", "lucide-arrow-up-right-from-circle", "lucide-arrow-up-right-from-square", "lucide-arrow-up-right-square", "lucide-arrow-up-square", "lucide-arrow-up-to-line", "lucide-arrow-up-wide-narrow", "lucide-arrow-up-za", "lucide-arrow-up-za", "lucide-arrows-up-from-line", "lucide-asterisk", "lucide-asterisk-square", "lucide-at-sign", "lucide-atom", "lucide-audio-lines", "lucide-audio-waveform", "lucide-award", "lucide-axe", "lucide-axis3-d", "lucide-axis3d", "lucide-baby", "lucide-backpack", "lucide-badge", "lucide-badge-alert", "lucide-badge-cent", "lucide-badge-check", "lucide-badge-dollar-sign", "lucide-badge-euro", "lucide-badge-help", "lucide-badge-indian-rupee", "lucide-badge-info", "lucide-badge-japanese-yen", "lucide-badge-minus", "lucide-badge-percent", "lucide-badge-plus", "lucide-badge-pound-sterling", "lucide-badge-russian-ruble", "lucide-badge-swiss-franc", "lucide-badge-x", "lucide-baggage-claim", "lucide-ban", "lucide-banana", "lucide-bandage", "lucide-banknote", "lucide-banknote-arrow-down", "lucide-banknote-arrow-up", "lucide-banknote-x", "lucide-bar-chart", "lucide-bar-chart2", "lucide-bar-chart3", "lucide-bar-chart4", "lucide-bar-chart-big", "lucide-bar-chart-horizontal", "lucide-bar-chart-horizontal-big", "lucide-barcode", "lucide-baseline", "lucide-bath", "lucide-battery", "lucide-battery-charging", "lucide-battery-full", "lucide-battery-low", "lucide-battery-medium", "lucide-battery-plus", "lucide-battery-warning", "lucide-beaker", "lucide-bean", "lucide-bean-off", "lucide-bed", "lucide-bed-double", "lucide-bed-single", "lucide-beef", "lucide-beer", "lucide-beer-off", "lucide-bell", "lucide-bell-dot", "lucide-bell-electric", "lucide-bell-minus", "lucide-bell-off", "lucide-bell-plus", "lucide-bell-ring", "lucide-between-horizonal-end", "lucide-between-horizonal-start", "lucide-between-horizontal-end", "lucide-between-horizontal-start", "lucide-between-vertical-end", "lucide-between-vertical-start", "lucide-biceps-flexed", "lucide-bike", "lucide-binary", "lucide-binoculars", "lucide-biohazard", "lucide-bird", "lucide-bitcoin", "lucide-blend", "lucide-blinds", "lucide-blocks", "lucide-bluetooth", "lucide-bluetooth-connected", "lucide-bluetooth-off", "lucide-bluetooth-searching", "lucide-bold", "lucide-bolt", "lucide-bomb", "lucide-bone", "lucide-book", "lucide-book-a", "lucide-book-audio", "lucide-book-check", "lucide-book-copy", "lucide-book-dashed", "lucide-book-down", "lucide-book-headphones", "lucide-book-heart", "lucide-book-image", "lucide-book-key", "lucide-book-lock", "lucide-book-marked", "lucide-book-minus", "lucide-book-open", "lucide-book-open-check", "lucide-book-open-text", "lucide-book-plus", "lucide-book-template", "lucide-book-text", "lucide-book-type", "lucide-book-up", "lucide-book-up2", "lucide-book-user", "lucide-book-x", "lucide-bookmark", "lucide-bookmark-check", "lucide-bookmark-minus", "lucide-bookmark-plus", "lucide-bookmark-x", "lucide-boom-box", "lucide-bot", "lucide-bot-message-square", "lucide-bot-off", "lucide-bow-arrow", "lucide-box", "lucide-box-select", "lucide-boxes", "lucide-braces", "lucide-brackets", "lucide-brain", "lucide-brain-circuit", "lucide-brain-cog", "lucide-brick-wall", "lucide-brick-wall-fire", "lucide-briefcase", "lucide-briefcase-business", "lucide-briefcase-conveyor-belt", "lucide-briefcase-medical", "lucide-bring-to-front", "lucide-brush", "lucide-brush-cleaning", "lucide-bubbles", "lucide-bug", "lucide-bug-off", "lucide-bug-play", "lucide-building", "lucide-building2", "lucide-bus", "lucide-bus-front", "lucide-cable", "lucide-cable-car", "lucide-cake", "lucide-cake-slice", "lucide-calculator", "lucide-calendar", "lucide-calendar1", "lucide-calendar-arrow-down", "lucide-calendar-arrow-up", "lucide-calendar-check", "lucide-calendar-check2", "lucide-calendar-clock", "lucide-calendar-cog", "lucide-calendar-days", "lucide-calendar-fold", "lucide-calendar-heart", "lucide-calendar-minus", "lucide-calendar-minus2", "lucide-calendar-off", "lucide-calendar-plus", "lucide-calendar-plus2", "lucide-calendar-range", "lucide-calendar-search", "lucide-calendar-sync", "lucide-calendar-x", "lucide-calendar-x2", "lucide-camera", "lucide-camera-off", "lucide-candlestick-chart", "lucide-candy", "lucide-candy-cane", "lucide-candy-off", "lucide-cannabis", "lucide-captions", "lucide-captions-off", "lucide-car", "lucide-car-front", "lucide-car-taxi-front", "lucide-caravan", "lucide-carrot", "lucide-case-lower", "lucide-case-sensitive", "lucide-case-upper", "lucide-cassette-tape", "lucide-cast", "lucide-castle", "lucide-cat", "lucide-cctv", "lucide-chart-area", "lucide-chart-bar", "lucide-chart-bar-big", "lucide-chart-bar-decreasing", "lucide-chart-bar-increasing", "lucide-chart-bar-stacked", "lucide-chart-candlestick", "lucide-chart-column", "lucide-chart-column-big", "lucide-chart-column-decreasing", "lucide-chart-column-increasing", "lucide-chart-column-stacked", "lucide-chart-gantt", "lucide-chart-line", "lucide-chart-network", "lucide-chart-no-axes-column", "lucide-chart-no-axes-column-decreasing", "lucide-chart-no-axes-column-increasing", "lucide-chart-no-axes-combined", "lucide-chart-no-axes-gantt", "lucide-chart-pie", "lucide-chart-scatter", "lucide-chart-spline", "lucide-check", "lucide-check-check", "lucide-check-circle", "lucide-check-circle2", "lucide-check-line", "lucide-check-square", "lucide-check-square2", "lucide-chef-hat", "lucide-cherry", "lucide-chevron-down", "lucide-chevron-down-circle", "lucide-chevron-down-square", "lucide-chevron-first", "lucide-chevron-last", "lucide-chevron-left", "lucide-chevron-left-circle", "lucide-chevron-left-square", "lucide-chevron-right", "lucide-chevron-right-circle", "lucide-chevron-right-square", "lucide-chevron-up", "lucide-chevron-up-circle", "lucide-chevron-up-square", "lucide-chevrons-down", "lucide-chevrons-down-up", "lucide-chevrons-left", "lucide-chevrons-left-right", "lucide-chevrons-left-right-ellipsis", "lucide-chevrons-right", "lucide-chevrons-right-left", "lucide-chevrons-up", "lucide-chevrons-up-down", "lucide-chrome", "lucide-church", "lucide-cigarette", "lucide-cigarette-off", "lucide-circle", "lucide-circle-alert", "lucide-circle-arrow-down", "lucide-circle-arrow-left", "lucide-circle-arrow-out-down-left", "lucide-circle-arrow-out-down-right", "lucide-circle-arrow-out-up-left", "lucide-circle-arrow-out-up-right", "lucide-circle-arrow-right", "lucide-circle-arrow-up", "lucide-circle-check", "lucide-circle-check-big", "lucide-circle-chevron-down", "lucide-circle-chevron-left", "lucide-circle-chevron-right", "lucide-circle-chevron-up", "lucide-circle-dashed", "lucide-circle-divide", "lucide-circle-dollar-sign", "lucide-circle-dot", "lucide-circle-dot-dashed", "lucide-circle-ellipsis", "lucide-circle-equal", "lucide-circle-fading-arrow-up", "lucide-circle-fading-plus", "lucide-circle-gauge", "lucide-circle-help", "lucide-circle-minus", "lucide-circle-off", "lucide-circle-parking", "lucide-circle-parking-off", "lucide-circle-pause", "lucide-circle-percent", "lucide-circle-play", "lucide-circle-plus", "lucide-circle-power", "lucide-circle-slash", "lucide-circle-slash2", "lucide-circle-slashed", "lucide-circle-small", "lucide-circle-stop", "lucide-circle-user", "lucide-circle-user-round", "lucide-circle-x", "lucide-circuit-board", "lucide-citrus", "lucide-clapperboard", "lucide-clipboard", "lucide-clipboard-check", "lucide-clipboard-copy", "lucide-clipboard-edit", "lucide-clipboard-list", "lucide-clipboard-minus", "lucide-clipboard-paste", "lucide-clipboard-pen", "lucide-clipboard-pen-line", "lucide-clipboard-plus", "lucide-clipboard-signature", "lucide-clipboard-type", "lucide-clipboard-x", "lucide-clock", "lucide-clock1", "lucide-clock10", "lucide-clock11", "lucide-clock12", "lucide-clock2", "lucide-clock3", "lucide-clock4", "lucide-clock5", "lucide-clock6", "lucide-clock7", "lucide-clock8", "lucide-clock9", "lucide-clock-alert", "lucide-clock-arrow-down", "lucide-clock-arrow-up", "lucide-clock-fading", "lucide-clock-plus", "lucide-cloud", "lucide-cloud-alert", "lucide-cloud-cog", "lucide-cloud-download", "lucide-cloud-drizzle", "lucide-cloud-fog", "lucide-cloud-hail", "lucide-cloud-lightning", "lucide-cloud-moon", "lucide-cloud-moon-rain", "lucide-cloud-off", "lucide-cloud-rain", "lucide-cloud-rain-wind", "lucide-cloud-snow", "lucide-cloud-sun", "lucide-cloud-sun-rain", "lucide-cloud-upload", "lucide-cloudy", "lucide-clover", "lucide-club", "lucide-code", "lucide-code2", "lucide-code-square", "lucide-code-xml", "lucide-codepen", "lucide-codesandbox", "lucide-coffee", "lucide-cog", "lucide-coins", "lucide-columns", "lucide-columns2", "lucide-columns3", "lucide-columns3-cog", "lucide-columns4", "lucide-columns-settings", "lucide-combine", "lucide-command", "lucide-compass", "lucide-component", "lucide-computer", "lucide-concierge-bell", "lucide-cone", "lucide-construction", "lucide-contact", "lucide-contact2", "lucide-contact-round", "lucide-container", "lucide-contrast", "lucide-cookie", "lucide-cooking-pot", "lucide-copy", "lucide-copy-check", "lucide-copy-minus", "lucide-copy-plus", "lucide-copy-slash", "lucide-copy-x", "lucide-copyleft", "lucide-copyright", "lucide-corner-down-left", "lucide-corner-down-right", "lucide-corner-left-down", "lucide-corner-left-up", "lucide-corner-right-down", "lucide-corner-right-up", "lucide-corner-up-left", "lucide-corner-up-right", "lucide-cpu", "lucide-creative-commons", "lucide-credit-card", "lucide-croissant", "lucide-crop", "lucide-cross", "lucide-crosshair", "lucide-crown", "lucide-cuboid", "lucide-cup-soda", "lucide-curly-braces", "lucide-currency", "lucide-cylinder", "lucide-dam", "lucide-database", "lucide-database-backup", "lucide-database-zap", "lucide-decimals-arrow-left", "lucide-decimals-arrow-right", "lucide-delete", "lucide-dessert", "lucide-diameter", "lucide-diamond", "lucide-diamond-minus", "lucide-diamond-percent", "lucide-diamond-plus", "lucide-dice1", "lucide-dice2", "lucide-dice3", "lucide-dice4", "lucide-dice5", "lucide-dice6", "lucide-dices", "lucide-diff", "lucide-disc", "lucide-disc2", "lucide-disc3", "lucide-disc-album", "lucide-divide", "lucide-divide-circle", "lucide-divide-square", "lucide-dna", "lucide-dna-off", "lucide-dock", "lucide-dog", "lucide-dollar-sign", "lucide-donut", "lucide-door-closed", "lucide-door-closed-locked", "lucide-door-open", "lucide-dot", "lucide-dot-square", "lucide-download", "lucide-download-cloud", "lucide-drafting-compass", "lucide-drama", "lucide-dribbble", "lucide-drill", "lucide-droplet", "lucide-droplet-off", "lucide-droplets", "lucide-drum", "lucide-drumstick", "lucide-dumbbell", "lucide-ear", "lucide-ear-off", "lucide-earth", "lucide-earth-lock", "lucide-eclipse", "lucide-edit", "lucide-edit2", "lucide-edit3", "lucide-egg", "lucide-egg-fried", "lucide-egg-off", "lucide-ellipsis", "lucide-ellipsis-vertical", "lucide-equal", "lucide-equal-approximately", "lucide-equal-not", "lucide-equal-square", "lucide-eraser", "lucide-ethernet-port", "lucide-euro", "lucide-expand", "lucide-external-link", "lucide-eye", "lucide-eye-closed", "lucide-eye-off", "lucide-facebook", "lucide-factory", "lucide-fan", "lucide-fast-forward", "lucide-feather", "lucide-fence", "lucide-ferris-wheel", "lucide-figma", "lucide-file", "lucide-file-archive", "lucide-file-audio", "lucide-file-audio2", "lucide-file-axis3-d", "lucide-file-axis3d", "lucide-file-badge", "lucide-file-badge2", "lucide-file-bar-chart", "lucide-file-bar-chart2", "lucide-file-box", "lucide-file-chart-column", "lucide-file-chart-column-increasing", "lucide-file-chart-line", "lucide-file-chart-pie", "lucide-file-check", "lucide-file-check2", "lucide-file-clock", "lucide-file-code", "lucide-file-code2", "lucide-file-cog", "lucide-file-cog2", "lucide-file-diff", "lucide-file-digit", "lucide-file-down", "lucide-file-edit", "lucide-file-heart", "lucide-file-image", "lucide-file-input", "lucide-file-json", "lucide-file-json2", "lucide-file-key", "lucide-file-key2", "lucide-file-line-chart", "lucide-file-lock", "lucide-file-lock2", "lucide-file-minus", "lucide-file-minus2", "lucide-file-music", "lucide-file-output", "lucide-file-pen", "lucide-file-pen-line", "lucide-file-pie-chart", "lucide-file-plus", "lucide-file-plus2", "lucide-file-question", "lucide-file-scan", "lucide-file-search", "lucide-file-search2", "lucide-file-signature", "lucide-file-sliders", "lucide-file-spreadsheet", "lucide-file-stack", "lucide-file-symlink", "lucide-file-terminal", "lucide-file-text", "lucide-file-type", "lucide-file-type2", "lucide-file-up", "lucide-file-user", "lucide-file-video", "lucide-file-video2", "lucide-file-volume", "lucide-file-volume2", "lucide-file-warning", "lucide-file-x", "lucide-file-x2", "lucide-files", "lucide-film", "lucide-filter", "lucide-filter-x", "lucide-fingerprint", "lucide-fire-extinguisher", "lucide-fish", "lucide-fish-off", "lucide-fish-symbol", "lucide-flag", "lucide-flag-off", "lucide-flag-triangle-left", "lucide-flag-triangle-right", "lucide-flame", "lucide-flame-kindling", "lucide-flashlight", "lucide-flashlight-off", "lucide-flask-conical", "lucide-flask-conical-off", "lucide-flask-round", "lucide-flip-horizontal", "lucide-flip-horizontal2", "lucide-flip-vertical", "lucide-flip-vertical2", "lucide-flower", "lucide-flower2", "lucide-focus", "lucide-fold-horizontal", "lucide-fold-vertical", "lucide-folder", "lucide-folder-archive", "lucide-folder-check", "lucide-folder-clock", "lucide-folder-closed", "lucide-folder-code", "lucide-folder-cog", "lucide-folder-cog2", "lucide-folder-dot", "lucide-folder-down", "lucide-folder-edit", "lucide-folder-git", "lucide-folder-git2", "lucide-folder-heart", "lucide-folder-input", "lucide-folder-kanban", "lucide-folder-key", "lucide-folder-lock", "lucide-folder-minus", "lucide-folder-open", "lucide-folder-open-dot", "lucide-folder-output", "lucide-folder-pen", "lucide-folder-plus", "lucide-folder-root", "lucide-folder-search", "lucide-folder-search2", "lucide-folder-symlink", "lucide-folder-sync", "lucide-folder-tree", "lucide-folder-up", "lucide-folder-x", "lucide-folders", "lucide-footprints", "lucide-fork-knife", "lucide-fork-knife-crossed", "lucide-forklift", "lucide-form-input", "lucide-forward", "lucide-frame", "lucide-framer", "lucide-frown", "lucide-fuel", "lucide-fullscreen", "lucide-function-square", "lucide-funnel", "lucide-funnel-plus", "lucide-funnel-x", "lucide-gallery-horizontal", "lucide-gallery-horizontal-end", "lucide-gallery-thumbnails", "lucide-gallery-vertical", "lucide-gallery-vertical-end", "lucide-gamepad", "lucide-gamepad2", "lucide-gantt-chart", "lucide-gantt-chart-square", "lucide-gauge", "lucide-gauge-circle", "lucide-gavel", "lucide-gem", "lucide-ghost", "lucide-gift", "lucide-git-branch", "lucide-git-branch-plus", "lucide-git-commit", "lucide-git-commit-horizontal", "lucide-git-commit-vertical", "lucide-git-compare", "lucide-git-compare-arrows", "lucide-git-fork", "lucide-git-graph", "lucide-git-merge", "lucide-git-pull-request", "lucide-git-pull-request-arrow", "lucide-git-pull-request-closed", "lucide-git-pull-request-create", "lucide-git-pull-request-create-arrow", "lucide-git-pull-request-draft", "lucide-github", "lucide-gitlab", "lucide-glass-water", "lucide-glasses", "lucide-globe", "lucide-globe2", "lucide-globe-lock", "lucide-goal", "lucide-gpu", "lucide-grab", "lucide-graduation-cap", "lucide-grape", "lucide-grid", "lucide-grid2-x2", "lucide-grid2-x2-check", "lucide-grid2-x2-plus", "lucide-grid2-x2-x", "lucide-grid2x2", "lucide-grid2x2-check", "lucide-grid2x2-plus", "lucide-grid2x2-x", "lucide-grid3-x3", "lucide-grid3x3", "lucide-grip", "lucide-grip-horizontal", "lucide-grip-vertical", "lucide-group", "lucide-guitar", "lucide-ham", "lucide-hamburger", "lucide-hammer", "lucide-hand", "lucide-hand-coins", "lucide-hand-heart", "lucide-hand-helping", "lucide-hand-metal", "lucide-hand-platter", "lucide-handshake", "lucide-hard-drive", "lucide-hard-drive-download", "lucide-hard-drive-upload", "lucide-hard-hat", "lucide-hash", "lucide-haze", "lucide-hdmi-port", "lucide-heading", "lucide-heading1", "lucide-heading2", "lucide-heading3", "lucide-heading4", "lucide-heading5", "lucide-heading6", "lucide-headphone-off", "lucide-headphones", "lucide-headset", "lucide-heart", "lucide-heart-crack", "lucide-heart-handshake", "lucide-heart-minus", "lucide-heart-off", "lucide-heart-plus", "lucide-heart-pulse", "lucide-heater", "lucide-help-circle", "lucide-helping-hand", "lucide-hexagon", "lucide-highlighter", "lucide-history", "lucide-home", "lucide-hop", "lucide-hop-off", "lucide-hospital", "lucide-hotel", "lucide-hourglass", "lucide-house", "lucide-house-plug", "lucide-house-plus", "lucide-house-wifi", "lucide-ice-cream", "lucide-ice-cream2", "lucide-ice-cream-bowl", "lucide-ice-cream-cone", "lucide-id-card", "lucide-image", "lucide-image-down", "lucide-image-minus", "lucide-image-off", "lucide-image-play", "lucide-image-plus", "lucide-image-up", "lucide-image-upscale", "lucide-images", "lucide-import", "lucide-inbox", "lucide-indent", "lucide-indent-decrease", "lucide-indent-increase", "lucide-indian-rupee", "lucide-infinity", "lucide-info", "lucide-inspect", "lucide-inspection-panel", "lucide-instagram", "lucide-italic", "lucide-iteration-ccw", "lucide-iteration-cw", "lucide-japanese-yen", "lucide-joystick", "lucide-kanban", "lucide-kanban-square", "lucide-kanban-square-dashed", "lucide-key", "lucide-key-round", "lucide-key-square", "lucide-keyboard", "lucide-keyboard-music", "lucide-keyboard-off", "lucide-lamp", "lucide-lamp-ceiling", "lucide-lamp-desk", "lucide-lamp-floor", "lucide-lamp-wall-down", "lucide-lamp-wall-up", "lucide-land-plot", "lucide-landmark", "lucide-languages", "lucide-laptop", "lucide-laptop2", "lucide-laptop-minimal", "lucide-laptop-minimal-check", "lucide-lasso", "lucide-lasso-select", "lucide-laugh", "lucide-layers", "lucide-layers2", "lucide-layers3", "lucide-layout", "lucide-layout-dashboard", "lucide-layout-grid", "lucide-layout-list", "lucide-layout-panel-left", "lucide-layout-panel-top", "lucide-layout-template", "lucide-leaf", "lucide-leafy-green", "lucide-lectern", "lucide-letter-text", "lucide-library", "lucide-library-big", "lucide-library-square", "lucide-life-buoy", "lucide-ligature", "lucide-lightbulb", "lucide-lightbulb-off", "lucide-line-chart", "lucide-link", "lucide-link2", "lucide-link2-off", "lucide-linkedin", "lucide-list", "lucide-list-check", "lucide-list-checks", "lucide-list-collapse", "lucide-list-end", "lucide-list-filter", "lucide-list-filter-plus", "lucide-list-minus", "lucide-list-music", "lucide-list-ordered", "lucide-list-plus", "lucide-list-restart", "lucide-list-start", "lucide-list-todo", "lucide-list-tree", "lucide-list-video", "lucide-list-x", "lucide-loader", "lucide-loader2", "lucide-loader-circle", "lucide-loader-pinwheel", "lucide-locate", "lucide-locate-fixed", "lucide-locate-off", "lucide-location-edit", "lucide-lock", "lucide-lock-keyhole", "lucide-lock-keyhole-open", "lucide-lock-open", "lucide-log-in", "lucide-log-out", "lucide-logs", "lucide-lollipop", "lucide-luggage", "lucide-m-square", "lucide-magnet", "lucide-mail", "lucide-mail-check", "lucide-mail-minus", "lucide-mail-open", "lucide-mail-plus", "lucide-mail-question", "lucide-mail-search", "lucide-mail-warning", "lucide-mail-x", "lucide-mailbox", "lucide-mails", "lucide-map", "lucide-map-pin", "lucide-map-pin-check", "lucide-map-pin-check-inside", "lucide-map-pin-house", "lucide-map-pin-minus", "lucide-map-pin-minus-inside", "lucide-map-pin-off", "lucide-map-pin-plus", "lucide-map-pin-plus-inside", "lucide-map-pin-x", "lucide-map-pin-x-inside", "lucide-map-pinned", "lucide-map-plus", "lucide-mars", "lucide-mars-stroke", "lucide-martini", "lucide-maximize", "lucide-maximize2", "lucide-medal", "lucide-megaphone", "lucide-megaphone-off", "lucide-meh", "lucide-memory-stick", "lucide-menu", "lucide-menu-square", "lucide-merge", "lucide-message-circle", "lucide-message-circle-code", "lucide-message-circle-dashed", "lucide-message-circle-heart", "lucide-message-circle-more", "lucide-message-circle-off", "lucide-message-circle-plus", "lucide-message-circle-question", "lucide-message-circle-reply", "lucide-message-circle-warning", "lucide-message-circle-x", "lucide-message-square", "lucide-message-square-code", "lucide-message-square-dashed", "lucide-message-square-diff", "lucide-message-square-dot", "lucide-message-square-heart", "lucide-message-square-lock", "lucide-message-square-more", "lucide-message-square-off", "lucide-message-square-plus", "lucide-message-square-quote", "lucide-message-square-reply", "lucide-message-square-share", "lucide-message-square-text", "lucide-message-square-warning", "lucide-message-square-x", "lucide-messages-square", "lucide-mic", "lucide-mic2", "lucide-mic-off", "lucide-mic-vocal", "lucide-microchip", "lucide-microscope", "lucide-microwave", "lucide-milestone", "lucide-milk", "lucide-milk-off", "lucide-minimize", "lucide-minimize2", "lucide-minus", "lucide-minus-circle", "lucide-minus-square", "lucide-monitor", "lucide-monitor-check", "lucide-monitor-cog", "lucide-monitor-dot", "lucide-monitor-down", "lucide-monitor-off", "lucide-monitor-pause", "lucide-monitor-play", "lucide-monitor-smartphone", "lucide-monitor-speaker", "lucide-monitor-stop", "lucide-monitor-up", "lucide-monitor-x", "lucide-moon", "lucide-moon-star", "lucide-more-horizontal", "lucide-more-vertical", "lucide-mountain", "lucide-mountain-snow", "lucide-mouse", "lucide-mouse-off", "lucide-mouse-pointer", "lucide-mouse-pointer2", "lucide-mouse-pointer-ban", "lucide-mouse-pointer-click", "lucide-mouse-pointer-square-dashed", "lucide-move", "lucide-move3-d", "lucide-move3d", "lucide-move-diagonal", "lucide-move-diagonal2", "lucide-move-down", "lucide-move-down-left", "lucide-move-down-right", "lucide-move-horizontal", "lucide-move-left", "lucide-move-right", "lucide-move-up", "lucide-move-up-left", "lucide-move-up-right", "lucide-move-vertical", "lucide-music", "lucide-music2", "lucide-music3", "lucide-music4", "lucide-navigation", "lucide-navigation2", "lucide-navigation2-off", "lucide-navigation-off", "lucide-network", "lucide-newspaper", "lucide-nfc", "lucide-non-binary", "lucide-notebook", "lucide-notebook-pen", "lucide-notebook-tabs", "lucide-notebook-text", "lucide-notepad-text", "lucide-notepad-text-dashed", "lucide-nut", "lucide-nut-off", "lucide-octagon", "lucide-octagon-alert", "lucide-octagon-minus", "lucide-octagon-pause", "lucide-octagon-x", "lucide-omega", "lucide-option", "lucide-orbit", "lucide-origami", "lucide-outdent", "lucide-package", "lucide-package2", "lucide-package-check", "lucide-package-minus", "lucide-package-open", "lucide-package-plus", "lucide-package-search", "lucide-package-x", "lucide-paint-bucket", "lucide-paint-roller", "lucide-paintbrush", "lucide-paintbrush2", "lucide-paintbrush-vertical", "lucide-palette", "lucide-palmtree", "lucide-panda", "lucide-panel-bottom", "lucide-panel-bottom-close", "lucide-panel-bottom-dashed", "lucide-panel-bottom-inactive", "lucide-panel-bottom-open", "lucide-panel-left", "lucide-panel-left-close", "lucide-panel-left-dashed", "lucide-panel-left-inactive", "lucide-panel-left-open", "lucide-panel-right", "lucide-panel-right-close", "lucide-panel-right-dashed", "lucide-panel-right-inactive", "lucide-panel-right-open", "lucide-panel-top", "lucide-panel-top-close", "lucide-panel-top-dashed", "lucide-panel-top-inactive", "lucide-panel-top-open", "lucide-panels-left-bottom", "lucide-panels-left-right", "lucide-panels-right-bottom", "lucide-panels-top-bottom", "lucide-panels-top-left", "lucide-paperclip", "lucide-parentheses", "lucide-parking-circle", "lucide-parking-circle-off", "lucide-parking-meter", "lucide-parking-square", "lucide-parking-square-off", "lucide-party-popper", "lucide-pause", "lucide-pause-circle", "lucide-pause-octagon", "lucide-paw-print", "lucide-pc-case", "lucide-pen", "lucide-pen-box", "lucide-pen-line", "lucide-pen-off", "lucide-pen-square", "lucide-pen-tool", "lucide-pencil", "lucide-pencil-line", "lucide-pencil-off", "lucide-pencil-ruler", "lucide-pentagon", "lucide-percent", "lucide-percent-circle", "lucide-percent-diamond", "lucide-percent-square", "lucide-person-standing", "lucide-philippine-peso", "lucide-phone", "lucide-phone-call", "lucide-phone-forwarded", "lucide-phone-incoming", "lucide-phone-missed", "lucide-phone-off", "lucide-phone-outgoing", "lucide-pi", "lucide-pi-square", "lucide-piano", "lucide-pickaxe", "lucide-picture-in-picture", "lucide-picture-in-picture2", "lucide-pie-chart", "lucide-piggy-bank", "lucide-pilcrow", "lucide-pilcrow-left", "lucide-pilcrow-right", "lucide-pilcrow-square", "lucide-pill", "lucide-pill-bottle", "lucide-pin", "lucide-pin-off", "lucide-pipette", "lucide-pizza", "lucide-plane", "lucide-plane-landing", "lucide-plane-takeoff", "lucide-play", "lucide-play-circle", "lucide-play-square", "lucide-plug", "lucide-plug2", "lucide-plug-zap", "lucide-plug-zap2", "lucide-plus", "lucide-plus-circle", "lucide-plus-square", "lucide-pocket", "lucide-pocket-knife", "lucide-podcast", "lucide-pointer", "lucide-pointer-off", "lucide-popcorn", "lucide-popsicle", "lucide-pound-sterling", "lucide-power", "lucide-power-circle", "lucide-power-off", "lucide-power-square", "lucide-presentation", "lucide-printer", "lucide-printer-check", "lucide-projector", "lucide-proportions", "lucide-puzzle", "lucide-pyramid", "lucide-qr-code", "lucide-quote", "lucide-rabbit", "lucide-radar", "lucide-radiation", "lucide-radical", "lucide-radio", "lucide-radio-receiver", "lucide-radio-tower", "lucide-radius", "lucide-rail-symbol", "lucide-rainbow", "lucide-rat", "lucide-ratio", "lucide-receipt", "lucide-receipt-cent", "lucide-receipt-euro", "lucide-receipt-indian-rupee", "lucide-receipt-japanese-yen", "lucide-receipt-pound-sterling", "lucide-receipt-russian-ruble", "lucide-receipt-swiss-franc", "lucide-receipt-text", "lucide-rectangle-ellipsis", "lucide-rectangle-goggles", "lucide-rectangle-horizontal", "lucide-rectangle-vertical", "lucide-recycle", "lucide-redo", "lucide-redo2", "lucide-redo-dot", "lucide-refresh-ccw", "lucide-refresh-ccw-dot", "lucide-refresh-cw", "lucide-refresh-cw-off", "lucide-refrigerator", "lucide-regex", "lucide-remove-formatting", "lucide-repeat", "lucide-repeat1", "lucide-repeat2", "lucide-replace", "lucide-replace-all", "lucide-reply", "lucide-reply-all", "lucide-rewind", "lucide-ribbon", "lucide-rocket", "lucide-rocking-chair", "lucide-roller-coaster", "lucide-rotate3-d", "lucide-rotate3d", "lucide-rotate-ccw", "lucide-rotate-ccw-key", "lucide-rotate-ccw-square", "lucide-rotate-cw", "lucide-rotate-cw-square", "lucide-route", "lucide-route-off", "lucide-router", "lucide-rows", "lucide-rows2", "lucide-rows3", "lucide-rows4", "lucide-rss", "lucide-ruler", "lucide-ruler-dimension-line", "lucide-russian-ruble", "lucide-sailboat", "lucide-salad", "lucide-sandwich", "lucide-satellite", "lucide-satellite-dish", "lucide-saudi-riyal", "lucide-save", "lucide-save-all", "lucide-save-off", "lucide-scale", "lucide-scale3-d", "lucide-scale3d", "lucide-scaling", "lucide-scan", "lucide-scan-barcode", "lucide-scan-eye", "lucide-scan-face", "lucide-scan-heart", "lucide-scan-line", "lucide-scan-qr-code", "lucide-scan-search", "lucide-scan-text", "lucide-scatter-chart", "lucide-school", "lucide-school2", "lucide-scissors", "lucide-scissors-line-dashed", "lucide-scissors-square", "lucide-scissors-square-dashed-bottom", "lucide-screen-share", "lucide-screen-share-off", "lucide-scroll", "lucide-scroll-text", "lucide-search", "lucide-search-check", "lucide-search-code", "lucide-search-slash", "lucide-search-x", "lucide-section", "lucide-send", "lucide-send-horizonal", "lucide-send-horizontal", "lucide-send-to-back", "lucide-separator-horizontal", "lucide-separator-vertical", "lucide-server", "lucide-server-cog", "lucide-server-crash", "lucide-server-off", "lucide-settings", "lucide-settings2", "lucide-shapes", "lucide-share", "lucide-share2", "lucide-sheet", "lucide-shell", "lucide-shield", "lucide-shield-alert", "lucide-shield-ban", "lucide-shield-check", "lucide-shield-close", "lucide-shield-ellipsis", "lucide-shield-half", "lucide-shield-minus", "lucide-shield-off", "lucide-shield-plus", "lucide-shield-question", "lucide-shield-user", "lucide-shield-x", "lucide-ship", "lucide-ship-wheel", "lucide-shirt", "lucide-shopping-bag", "lucide-shopping-basket", "lucide-shopping-cart", "lucide-shovel", "lucide-shower-head", "lucide-shredder", "lucide-shrimp", "lucide-shrink", "lucide-shrub", "lucide-shuffle", "lucide-sidebar", "lucide-sidebar-close", "lucide-sidebar-open", "lucide-sigma", "lucide-sigma-square", "lucide-signal", "lucide-signal-high", "lucide-signal-low", "lucide-signal-medium", "lucide-signal-zero", "lucide-signature", "lucide-signpost", "lucide-signpost-big", "lucide-siren", "lucide-skip-back", "lucide-skip-forward", "lucide-skull", "lucide-slack", "lucide-slash", "lucide-slash-square", "lucide-slice", "lucide-sliders", "lucide-sliders-horizontal", "lucide-sliders-vertical", "lucide-smartphone", "lucide-smartphone-charging", "lucide-smartphone-nfc", "lucide-smile", "lucide-smile-plus", "lucide-snail", "lucide-snowflake", "lucide-soap-dispenser-droplet", "lucide-sofa", "lucide-sort-asc", "lucide-sort-desc", "lucide-soup", "lucide-space", "lucide-spade", "lucide-sparkle", "lucide-sparkles", "lucide-speaker", "lucide-speech", "lucide-spell-check", "lucide-spell-check2", "lucide-spline", "lucide-spline-pointer", "lucide-split", "lucide-split-square-horizontal", "lucide-split-square-vertical", "lucide-spray-can", "lucide-sprout", "lucide-square", "lucide-square-activity", "lucide-square-arrow-down", "lucide-square-arrow-down-left", "lucide-square-arrow-down-right", "lucide-square-arrow-left", "lucide-square-arrow-out-down-left", "lucide-square-arrow-out-down-right", "lucide-square-arrow-out-up-left", "lucide-square-arrow-out-up-right", "lucide-square-arrow-right", "lucide-square-arrow-up", "lucide-square-arrow-up-left", "lucide-square-arrow-up-right", "lucide-square-asterisk", "lucide-square-bottom-dashed-scissors", "lucide-square-chart-gantt", "lucide-square-check", "lucide-square-check-big", "lucide-square-chevron-down", "lucide-square-chevron-left", "lucide-square-chevron-right", "lucide-square-chevron-up", "lucide-square-code", "lucide-square-dashed", "lucide-square-dashed-bottom", "lucide-square-dashed-bottom-code", "lucide-square-dashed-kanban", "lucide-square-dashed-mouse-pointer", "lucide-square-divide", "lucide-square-dot", "lucide-square-equal", "lucide-square-function", "lucide-square-gantt-chart", "lucide-square-kanban", "lucide-square-library", "lucide-square-m", "lucide-square-menu", "lucide-square-minus", "lucide-square-mouse-pointer", "lucide-square-parking", "lucide-square-parking-off", "lucide-square-pen", "lucide-square-percent", "lucide-square-pi", "lucide-square-pilcrow", "lucide-square-play", "lucide-square-plus", "lucide-square-power", "lucide-square-radical", "lucide-square-round-corner", "lucide-square-scissors", "lucide-square-sigma", "lucide-square-slash", "lucide-square-split-horizontal", "lucide-square-split-vertical", "lucide-square-square", "lucide-square-stack", "lucide-square-terminal", "lucide-square-user", "lucide-square-user-round", "lucide-square-x", "lucide-squares-exclude", "lucide-squares-intersect", "lucide-squares-subtract", "lucide-squares-unite", "lucide-squircle", "lucide-squirrel", "lucide-stamp", "lucide-star", "lucide-star-half", "lucide-star-off", "lucide-stars", "lucide-step-back", "lucide-step-forward", "lucide-stethoscope", "lucide-sticker", "lucide-sticky-note", "lucide-stop-circle", "lucide-store", "lucide-stretch-horizontal", "lucide-stretch-vertical", "lucide-strikethrough", "lucide-subscript", "lucide-subtitles", "lucide-sun", "lucide-sun-dim", "lucide-sun-medium", "lucide-sun-moon", "lucide-sun-snow", "lucide-sunrise", "lucide-sunset", "lucide-superscript", "lucide-swatch-book", "lucide-swiss-franc", "lucide-switch-camera", "lucide-sword", "lucide-swords", "lucide-syringe", "lucide-table", "lucide-table2", "lucide-table-cells-merge", "lucide-table-cells-split", "lucide-table-columns-split", "lucide-table-config", "lucide-table-of-contents", "lucide-table-properties", "lucide-table-rows-split", "lucide-tablet", "lucide-tablet-smartphone", "lucide-tablets", "lucide-tag", "lucide-tags", "lucide-tally1", "lucide-tally2", "lucide-tally3", "lucide-tally4", "lucide-tally5", "lucide-tangent", "lucide-target", "lucide-telescope", "lucide-tent", "lucide-tent-tree", "lucide-terminal", "lucide-terminal-square", "lucide-test-tube", "lucide-test-tube2", "lucide-test-tube-diagonal", "lucide-test-tubes", "lucide-text", "lucide-text-cursor", "lucide-text-cursor-input", "lucide-text-quote", "lucide-text-search", "lucide-text-select", "lucide-text-selection", "lucide-theater", "lucide-thermometer", "lucide-thermometer-snowflake", "lucide-thermometer-sun", "lucide-thumbs-down", "lucide-thumbs-up", "lucide-ticket", "lucide-ticket-check", "lucide-ticket-minus", "lucide-ticket-percent", "lucide-ticket-plus", "lucide-ticket-slash", "lucide-ticket-x", "lucide-tickets", "lucide-tickets-plane", "lucide-timer", "lucide-timer-off", "lucide-timer-reset", "lucide-toggle-left", "lucide-toggle-right", "lucide-toilet", "lucide-tornado", "lucide-torus", "lucide-touchpad", "lucide-touchpad-off", "lucide-tower-control", "lucide-toy-brick", "lucide-tractor", "lucide-traffic-cone", "lucide-train", "lucide-train-front", "lucide-train-front-tunnel", "lucide-train-track", "lucide-tram-front", "lucide-transgender", "lucide-trash", "lucide-trash2", "lucide-tree-deciduous", "lucide-tree-palm", "lucide-tree-pine", "lucide-trees", "lucide-trello", "lucide-trending-down", "lucide-trending-up", "lucide-trending-up-down", "lucide-triangle", "lucide-triangle-alert", "lucide-triangle-dashed", "lucide-triangle-right", "lucide-trophy", "lucide-truck", "lucide-truck-electric", "lucide-turtle", "lucide-tv", "lucide-tv2", "lucide-tv-minimal", "lucide-tv-minimal-play", "lucide-twitch", "lucide-twitter", "lucide-type", "lucide-type-outline", "lucide-umbrella", "lucide-umbrella-off", "lucide-underline", "lucide-undo", "lucide-undo2", "lucide-undo-dot", "lucide-unfold-horizontal", "lucide-unfold-vertical", "lucide-ungroup", "lucide-university", "lucide-unlink", "lucide-unlink2", "lucide-unlock", "lucide-unlock-keyhole", "lucide-unplug", "lucide-upload", "lucide-upload-cloud", "lucide-usb", "lucide-user", "lucide-user2", "lucide-user-check", "lucide-user-check2", "lucide-user-circle", "lucide-user-circle2", "lucide-user-cog", "lucide-user-cog2", "lucide-user-lock", "lucide-user-minus", "lucide-user-minus2", "lucide-user-pen", "lucide-user-plus", "lucide-user-plus2", "lucide-user-round", "lucide-user-round-check", "lucide-user-round-cog", "lucide-user-round-minus", "lucide-user-round-pen", "lucide-user-round-plus", "lucide-user-round-search", "lucide-user-round-x", "lucide-user-search", "lucide-user-square", "lucide-user-square2", "lucide-user-x", "lucide-user-x2", "lucide-users", "lucide-users2", "lucide-users-round", "lucide-utensils", "lucide-utensils-crossed", "lucide-utility-pole", "lucide-variable", "lucide-vault", "lucide-vegan", "lucide-venetian-mask", "lucide-venus", "lucide-venus-and-mars", "lucide-verified", "lucide-vibrate", "lucide-vibrate-off", "lucide-video", "lucide-video-off", "lucide-videotape", "lucide-view", "lucide-voicemail", "lucide-volleyball", "lucide-volume", "lucide-volume1", "lucide-volume2", "lucide-volume-off", "lucide-volume-x", "lucide-vote", "lucide-wallet", "lucide-wallet2", "lucide-wallet-cards", "lucide-wallet-minimal", "lucide-wallpaper", "lucide-wand", "lucide-wand2", "lucide-wand-sparkles", "lucide-warehouse", "lucide-washing-machine", "lucide-watch", "lucide-waves", "lucide-waves-ladder", "lucide-waypoints", "lucide-webcam", "lucide-webhook", "lucide-webhook-off", "lucide-weight", "lucide-wheat", "lucide-wheat-off", "lucide-whole-word", "lucide-wifi", "lucide-wifi-high", "lucide-wifi-low", "lucide-wifi-off", "lucide-wifi-pen", "lucide-wifi-zero", "lucide-wind", "lucide-wind-arrow-down", "lucide-wine", "lucide-wine-off", "lucide-workflow", "lucide-worm", "lucide-wrap-text", "lucide-wrench", "lucide-x", "lucide-x-circle", "lucide-x-octagon", "lucide-x-square", "lucide-youtube", "lucide-zap", "lucide-zap-off", "lucide-zoom-in", "lucide-zoom-out", "luggage", "luggage-icon", "m-square", "m-square-icon", "magnet", "magnet-icon", "mail", "mail-check", "mail-check-icon", "mail-icon", "mail-minus", "mail-minus-icon", "mail-open", "mail-open-icon", "mail-plus", "mail-plus-icon", "mail-question", "mail-question-icon", "mail-search", "mail-search-icon", "mail-warning", "mail-warning-icon", "mail-x", "mail-x-icon", "mailbox", "mailbox-icon", "mails", "mails-icon", "map", "map-icon", "map-pin", "map-pin-check", "map-pin-check-icon", "map-pin-check-inside", "map-pin-check-inside-icon", "map-pin-house", "map-pin-house-icon", "map-pin-icon", "map-pin-minus", "map-pin-minus-icon", "map-pin-minus-inside", "map-pin-minus-inside-icon", "map-pin-off", "map-pin-off-icon", "map-pin-plus", "map-pin-plus-icon", "map-pin-plus-inside", "map-pin-plus-inside-icon", "map-pin-x", "map-pin-x-icon", "map-pin-x-inside", "map-pin-x-inside-icon", "map-pinned", "map-pinned-icon", "map-plus", "map-plus-icon", "mars", "mars-icon", "mars-stroke", "mars-stroke-icon", "martini", "martini-icon", "maximize", "maximize2", "maximize2-icon", "maximize-icon", "medal", "medal-icon", "megaphone", "megaphone-icon", "megaphone-off", "megaphone-off-icon", "meh", "meh-icon", "memory-stick", "memory-stick-icon", "menu", "menu-icon", "menu-square", "menu-square-icon", "merge", "merge-icon", "message-circle", "message-circle-code", "message-circle-code-icon", "message-circle-dashed", "message-circle-dashed-icon", "message-circle-heart", "message-circle-heart-icon", "message-circle-icon", "message-circle-more", "message-circle-more-icon", "message-circle-off", "message-circle-off-icon", "message-circle-plus", "message-circle-plus-icon", "message-circle-question", "message-circle-question-icon", "message-circle-reply", "message-circle-reply-icon", "message-circle-warning", "message-circle-warning-icon", "message-circle-x", "message-circle-x-icon", "message-square", "message-square-code", "message-square-code-icon", "message-square-dashed", "message-square-dashed-icon", "message-square-diff", "message-square-diff-icon", "message-square-dot", "message-square-dot-icon", "message-square-heart", "message-square-heart-icon", "message-square-icon", "message-square-lock", "message-square-lock-icon", "message-square-more", "message-square-more-icon", "message-square-off", "message-square-off-icon", "message-square-plus", "message-square-plus-icon", "message-square-quote", "message-square-quote-icon", "message-square-reply", "message-square-reply-icon", "message-square-share", "message-square-share-icon", "message-square-text", "message-square-text-icon", "message-square-warning", "message-square-warning-icon", "message-square-x", "message-square-x-icon", "messages-square", "messages-square-icon", "mic", "mic2", "mic2-icon", "mic-icon", "mic-off", "mic-off-icon", "mic-vocal", "mic-vocal-icon", "microchip", "microchip-icon", "microscope", "microscope-icon", "microwave", "microwave-icon", "milestone", "milestone-icon", "milk", "milk-icon", "milk-off", "milk-off-icon", "minimize", "minimize2", "minimize2-icon", "minimize-icon", "minus", "minus-circle", "minus-circle-icon", "minus-icon", "minus-square", "minus-square-icon", "monitor", "monitor-check", "monitor-check-icon", "monitor-cog", "monitor-cog-icon", "monitor-dot", "monitor-dot-icon", "monitor-down", "monitor-down-icon", "monitor-icon", "monitor-off", "monitor-off-icon", "monitor-pause", "monitor-pause-icon", "monitor-play", "monitor-play-icon", "monitor-smartphone", "monitor-smartphone-icon", "monitor-speaker", "monitor-speaker-icon", "monitor-stop", "monitor-stop-icon", "monitor-up", "monitor-up-icon", "monitor-x", "monitor-x-icon", "moon", "moon-icon", "moon-star", "moon-star-icon", "more-horizontal", "more-horizontal-icon", "more-vertical", "more-vertical-icon", "mountain", "mountain-icon", "mountain-snow", "mountain-snow-icon", "mouse", "mouse-icon", "mouse-off", "mouse-off-icon", "mouse-pointer", "mouse-pointer2", "mouse-pointer2-icon", "mouse-pointer-ban", "mouse-pointer-ban-icon", "mouse-pointer-click", "mouse-pointer-click-icon", "mouse-pointer-icon", "mouse-pointer-square-dashed", "mouse-pointer-square-dashed-icon", "move", "move3-d", "move3-d-icon", "move3d", "move3d-icon", "move-diagonal", "move-diagonal2", "move-diagonal2-icon", "move-diagonal-icon", "move-down", "move-down-icon", "move-down-left", "move-down-left-icon", "move-down-right", "move-down-right-icon", "move-horizontal", "move-horizontal-icon", "move-icon", "move-left", "move-left-icon", "move-right", "move-right-icon", "move-up", "move-up-icon", "move-up-left", "move-up-left-icon", "move-up-right", "move-up-right-icon", "move-vertical", "move-vertical-icon", "music", "music2", "music2-icon", "music3", "music3-icon", "music4", "music4-icon", "music-icon", "navigation", "navigation2", "navigation2-icon", "navigation2-off", "navigation2-off-icon", "navigation-icon", "navigation-off", "navigation-off-icon", "network", "network-icon", "newspaper", "newspaper-icon", "nfc", "nfc-icon", "non-binary", "non-binary-icon", "notebook", "notebook-icon", "notebook-pen", "notebook-pen-icon", "notebook-tabs", "notebook-tabs-icon", "notebook-text", "notebook-text-icon", "notepad-text", "notepad-text-dashed", "notepad-text-dashed-icon", "notepad-text-icon", "nut", "nut-icon", "nut-off", "nut-off-icon", "octagon", "octagon-alert", "octagon-alert-icon", "octagon-icon", "octagon-minus", "octagon-minus-icon", "octagon-pause", "octagon-pause-icon", "octagon-x", "octagon-x-icon", "omega", "omega-icon", "option", "option-icon", "orbit", "orbit-icon", "origami", "origami-icon", "outdent", "outdent-icon", "package", "package2", "package2-icon", "package-check", "package-check-icon", "package-icon", "package-minus", "package-minus-icon", "package-open", "package-open-icon", "package-plus", "package-plus-icon", "package-search", "package-search-icon", "package-x", "package-x-icon", "paint-bucket", "paint-bucket-icon", "paint-roller", "paint-roller-icon", "paintbrush", "paintbrush2", "paintbrush2-icon", "paintbrush-icon", "paintbrush-vertical", "paintbrush-vertical-icon", "palette", "palette-icon", "palmtree", "palmtree-icon", "panda", "panda-icon", "panel-bottom", "panel-bottom-close", "panel-bottom-close-icon", "panel-bottom-dashed", "panel-bottom-dashed-icon", "panel-bottom-icon", "panel-bottom-inactive", "panel-bottom-inactive-icon", "panel-bottom-open", "panel-bottom-open-icon", "panel-left", "panel-left-close", "panel-left-close-icon", "panel-left-dashed", "panel-left-dashed-icon", "panel-left-icon", "panel-left-inactive", "panel-left-inactive-icon", "panel-left-open", "panel-left-open-icon", "panel-right", "panel-right-close", "panel-right-close-icon", "panel-right-dashed", "panel-right-dashed-icon", "panel-right-icon", "panel-right-inactive", "panel-right-inactive-icon", "panel-right-open", "panel-right-open-icon", "panel-top", "panel-top-close", "panel-top-close-icon", "panel-top-dashed", "panel-top-dashed-icon", "panel-top-icon", "panel-top-inactive", "panel-top-inactive-icon", "panel-top-open", "panel-top-open-icon", "panels-left-bottom", "panels-left-bottom-icon", "panels-left-right", "panels-left-right-icon", "panels-right-bottom", "panels-right-bottom-icon", "panels-top-bottom", "panels-top-bottom-icon", "panels-top-left", "panels-top-left-icon", "paperclip", "paperclip-icon", "parentheses", "parentheses-icon", "parking-circle", "parking-circle-icon", "parking-circle-off", "parking-circle-off-icon", "parking-meter", "parking-meter-icon", "parking-square", "parking-square-icon", "parking-square-off", "parking-square-off-icon", "party-popper", "party-popper-icon", "pause", "pause-circle", "pause-circle-icon", "pause-icon", "pause-octagon", "pause-octagon-icon", "paw-print", "paw-print-icon", "pc-case", "pc-case-icon", "pen", "pen-box", "pen-box-icon", "pen-icon", "pen-line", "pen-line-icon", "pen-off", "pen-off-icon", "pen-square", "pen-square-icon", "pen-tool", "pen-tool-icon", "pencil", "pencil-icon", "pencil-line", "pencil-line-icon", "pencil-off", "pencil-off-icon", "pencil-ruler", "pencil-ruler-icon", "pentagon", "pentagon-icon", "percent", "percent-circle", "percent-circle-icon", "percent-diamond", "percent-diamond-icon", "percent-icon", "percent-square", "percent-square-icon", "person-standing", "person-standing-icon", "philippine-peso", "philippine-peso-icon", "phone", "phone-call", "phone-call-icon", "phone-forwarded", "phone-forwarded-icon", "phone-icon", "phone-incoming", "phone-incoming-icon", "phone-missed", "phone-missed-icon", "phone-off", "phone-off-icon", "phone-outgoing", "phone-outgoing-icon", "pi", "pi-icon", "pi-square", "pi-square-icon", "piano", "piano-icon", "pickaxe", "pickaxe-icon", "picture-in-picture", "picture-in-picture2", "picture-in-picture2-icon", "picture-in-picture-icon", "pie-chart", "pie-chart-icon", "piggy-bank", "piggy-bank-icon", "pilcrow", "pilcrow-icon", "pilcrow-left", "pilcrow-left-icon", "pilcrow-right", "pilcrow-right-icon", "pilcrow-square", "pilcrow-square-icon", "pill", "pill-bottle", "pill-bottle-icon", "pill-icon", "pin", "pin-icon", "pin-off", "pin-off-icon", "pipette", "pipette-icon", "pizza", "pizza-icon", "plane", "plane-icon", "plane-landing", "plane-landing-icon", "plane-takeoff", "plane-takeoff-icon", "play", "play-circle", "play-circle-icon", "play-icon", "play-square", "play-square-icon", "plug", "plug2", "plug2-icon", "plug-icon", "plug-zap", "plug-zap2", "plug-zap2-icon", "plug-zap-icon", "plus", "plus-circle", "plus-circle-icon", "plus-icon", "plus-square", "plus-square-icon", "pocket", "pocket-icon", "pocket-knife", "pocket-knife-icon", "podcast", "podcast-icon", "pointer", "pointer-icon", "pointer-off", "pointer-off-icon", "popcorn", "popcorn-icon", "popsicle", "popsicle-icon", "pound-sterling", "pound-sterling-icon", "power", "power-circle", "power-circle-icon", "power-icon", "power-off", "power-off-icon", "power-square", "power-square-icon", "presentation", "presentation-icon", "printer", "printer-check", "printer-check-icon", "printer-icon", "projector", "projector-icon", "proportions", "proportions-icon", "puzzle", "puzzle-icon", "pyramid", "pyramid-icon", "qr-code", "qr-code-icon", "quote", "quote-icon", "rabbit", "rabbit-icon", "radar", "radar-icon", "radiation", "radiation-icon", "radical", "radical-icon", "radio", "radio-icon", "radio-receiver", "radio-receiver-icon", "radio-tower", "radio-tower-icon", "radius", "radius-icon", "rail-symbol", "rail-symbol-icon", "rainbow", "rainbow-icon", "rat", "rat-icon", "ratio", "ratio-icon", "receipt", "receipt-cent", "receipt-cent-icon", "receipt-euro", "receipt-euro-icon", "receipt-icon", "receipt-indian-rupee", "receipt-indian-rupee-icon", "receipt-japanese-yen", "receipt-japanese-yen-icon", "receipt-pound-sterling", "receipt-pound-sterling-icon", "receipt-russian-ruble", "receipt-russian-ruble-icon", "receipt-swiss-franc", "receipt-swiss-franc-icon", "receipt-text", "receipt-text-icon", "rectangle-ellipsis", "rectangle-ellipsis-icon", "rectangle-goggles", "rectangle-goggles-icon", "rectangle-horizontal", "rectangle-horizontal-icon", "rectangle-vertical", "rectangle-vertical-icon", "recycle", "recycle-icon", "redo", "redo2", "redo2-icon", "redo-dot", "redo-dot-icon", "redo-icon", "refresh-ccw", "refresh-ccw-dot", "refresh-ccw-dot-icon", "refresh-ccw-icon", "refresh-cw", "refresh-cw-icon", "refresh-cw-off", "refresh-cw-off-icon", "refrigerator", "refrigerator-icon", "regex", "regex-icon", "remove-formatting", "remove-formatting-icon", "repeat", "repeat1", "repeat1-icon", "repeat2", "repeat2-icon", "repeat-icon", "replace", "replace-all", "replace-all-icon", "replace-icon", "reply", "reply-all", "reply-all-icon", "reply-icon", "rewind", "rewind-icon", "ribbon", "ribbon-icon", "rocket", "rocket-icon", "rocking-chair", "rocking-chair-icon", "roller-coaster", "roller-coaster-icon", "rotate3-d", "rotate3-d-icon", "rotate3d", "rotate3d-icon", "rotate-ccw", "rotate-ccw-icon", "rotate-ccw-key", "rotate-ccw-key-icon", "rotate-ccw-square", "rotate-ccw-square-icon", "rotate-cw", "rotate-cw-icon", "rotate-cw-square", "rotate-cw-square-icon", "route", "route-icon", "route-off", "route-off-icon", "router", "router-icon", "rows", "rows2", "rows2-icon", "rows3", "rows3-icon", "rows4", "rows4-icon", "rows-icon", "rss", "rss-icon", "ruler", "ruler-dimension-line", "ruler-dimension-line-icon", "ruler-icon", "russian-ruble", "russian-ruble-icon", "sailboat", "sailboat-icon", "salad", "salad-icon", "sandwich", "sandwich-icon", "satellite", "satellite-dish", "satellite-dish-icon", "satellite-icon", "saudi-riyal", "saudi-riyal-icon", "save", "save-all", "save-all-icon", "save-icon", "save-off", "save-off-icon", "scale", "scale3-d", "scale3-d-icon", "scale3d", "scale3d-icon", "scale-icon", "scaling", "scaling-icon", "scan", "scan-barcode", "scan-barcode-icon", "scan-eye", "scan-eye-icon", "scan-face", "scan-face-icon", "scan-heart", "scan-heart-icon", "scan-icon", "scan-line", "scan-line-icon", "scan-qr-code", "scan-qr-code-icon", "scan-search", "scan-search-icon", "scan-text", "scan-text-icon", "scatter-chart", "scatter-chart-icon", "school", "school2", "school2-icon", "school-icon", "scissors", "scissors-icon", "scissors-line-dashed", "scissors-line-dashed-icon", "scissors-square", "scissors-square-dashed-bottom", "scissors-square-dashed-bottom-icon", "scissors-square-icon", "screen-share", "screen-share-icon", "screen-share-off", "screen-share-off-icon", "scroll", "scroll-icon", "scroll-text", "scroll-text-icon", "search", "search-check", "search-check-icon", "search-code", "search-code-icon", "search-icon", "search-slash", "search-slash-icon", "search-x", "search-x-icon", "section", "section-icon", "send", "send-horizonal", "send-horizonal-icon", "send-horizontal", "send-horizontal-icon", "send-icon", "send-to-back", "send-to-back-icon", "separator-horizontal", "separator-horizontal-icon", "separator-vertical", "separator-vertical-icon", "server", "server-cog", "server-cog-icon", "server-crash", "server-crash-icon", "server-icon", "server-off", "server-off-icon", "settings", "settings2", "settings2-icon", "settings-icon", "shapes", "shapes-icon", "share", "share2", "share2-icon", "share-icon", "sheet", "sheet-icon", "shell", "shell-icon", "shield", "shield-alert", "shield-alert-icon", "shield-ban", "shield-ban-icon", "shield-check", "shield-check-icon", "shield-close", "shield-close-icon", "shield-ellipsis", "shield-ellipsis-icon", "shield-half", "shield-half-icon", "shield-icon", "shield-minus", "shield-minus-icon", "shield-off", "shield-off-icon", "shield-plus", "shield-plus-icon", "shield-question", "shield-question-icon", "shield-user", "shield-user-icon", "shield-x", "shield-x-icon", "ship", "ship-icon", "ship-wheel", "ship-wheel-icon", "shirt", "shirt-icon", "shopping-bag", "shopping-bag-icon", "shopping-basket", "shopping-basket-icon", "shopping-cart", "shopping-cart-icon", "shovel", "shovel-icon", "shower-head", "shower-head-icon", "shredder", "shredder-icon", "shrimp", "shrimp-icon", "shrink", "shrink-icon", "shrub", "shrub-icon", "shuffle", "shuffle-icon", "sidebar", "sidebar-close", "sidebar-close-icon", "sidebar-icon", "sidebar-open", "sidebar-open-icon", "sigma", "sigma-icon", "sigma-square", "sigma-square-icon", "signal", "signal-high", "signal-high-icon", "signal-icon", "signal-low", "signal-low-icon", "signal-medium", "signal-medium-icon", "signal-zero", "signal-zero-icon", "signature", "signature-icon", "signpost", "signpost-big", "signpost-big-icon", "signpost-icon", "siren", "siren-icon", "skip-back", "skip-back-icon", "skip-forward", "skip-forward-icon", "skull", "skull-icon", "slack", "slack-icon", "slash", "slash-icon", "slash-square", "slash-square-icon", "slice", "slice-icon", "sliders", "sliders-horizontal", "sliders-horizontal-icon", "sliders-icon", "sliders-vertical", "sliders-vertical-icon", "smartphone", "smartphone-charging", "smartphone-charging-icon", "smartphone-icon", "smartphone-nfc", "smartphone-nfc-icon", "smile", "smile-icon", "smile-plus", "smile-plus-icon", "snail", "snail-icon", "snowflake", "snowflake-icon", "soap-dispenser-droplet", "soap-dispenser-droplet-icon", "sofa", "sofa-icon", "sort-asc", "sort-asc-icon", "sort-desc", "sort-desc-icon", "soup", "soup-icon", "space", "space-icon", "spade", "spade-icon", "sparkle", "sparkle-icon", "sparkles", "sparkles-icon", "speaker", "speaker-icon", "speech", "speech-icon", "spell-check", "spell-check2", "spell-check2-icon", "spell-check-icon", "spline", "spline-icon", "spline-pointer", "spline-pointer-icon", "split", "split-icon", "split-square-horizontal", "split-square-horizontal-icon", "split-square-vertical", "split-square-vertical-icon", "spray-can", "spray-can-icon", "sprout", "sprout-icon", "square", "square-activity", "square-activity-icon", "square-arrow-down", "square-arrow-down-icon", "square-arrow-down-left", "square-arrow-down-left-icon", "square-arrow-down-right", "square-arrow-down-right-icon", "square-arrow-left", "square-arrow-left-icon", "square-arrow-out-down-left", "square-arrow-out-down-left-icon", "square-arrow-out-down-right", "square-arrow-out-down-right-icon", "square-arrow-out-up-left", "square-arrow-out-up-left-icon", "square-arrow-out-up-right", "square-arrow-out-up-right-icon", "square-arrow-right", "square-arrow-right-icon", "square-arrow-up", "square-arrow-up-icon", "square-arrow-up-left", "square-arrow-up-left-icon", "square-arrow-up-right", "square-arrow-up-right-icon", "square-asterisk", "square-asterisk-icon", "square-bottom-dashed-scissors", "square-bottom-dashed-scissors-icon", "square-chart-gantt", "square-chart-gantt-icon", "square-check", "square-check-big", "square-check-big-icon", "square-check-icon", "square-chevron-down", "square-chevron-down-icon", "square-chevron-left", "square-chevron-left-icon", "square-chevron-right", "square-chevron-right-icon", "square-chevron-up", "square-chevron-up-icon", "square-code", "square-code-icon", "square-dashed", "square-dashed-bottom", "square-dashed-bottom-code", "square-dashed-bottom-code-icon", "square-dashed-bottom-icon", "square-dashed-icon", "square-dashed-kanban", "square-dashed-kanban-icon", "square-dashed-mouse-pointer", "square-dashed-mouse-pointer-icon", "square-divide", "square-divide-icon", "square-dot", "square-dot-icon", "square-equal", "square-equal-icon", "square-function", "square-function-icon", "square-gantt-chart", "square-gantt-chart-icon", "square-icon", "square-kanban", "square-kanban-icon", "square-library", "square-library-icon", "square-m", "square-m-icon", "square-menu", "square-menu-icon", "square-minus", "square-minus-icon", "square-mouse-pointer", "square-mouse-pointer-icon", "square-parking", "square-parking-icon", "square-parking-off", "square-parking-off-icon", "square-pen", "square-pen-icon", "square-percent", "square-percent-icon", "square-pi", "square-pi-icon", "square-pilcrow", "square-pilcrow-icon", "square-play", "square-play-icon", "square-plus", "square-plus-icon", "square-power", "square-power-icon", "square-radical", "square-radical-icon", "square-round-corner", "square-round-corner-icon", "square-scissors", "square-scissors-icon", "square-sigma", "square-sigma-icon", "square-slash", "square-slash-icon", "square-split-horizontal", "square-split-horizontal-icon", "square-split-vertical", "square-split-vertical-icon", "square-square", "square-square-icon", "square-stack", "square-stack-icon", "square-terminal", "square-terminal-icon", "square-user", "square-user-icon", "square-user-round", "square-user-round-icon", "square-x", "square-x-icon", "squares-exclude", "squares-exclude-icon", "squares-intersect", "squares-intersect-icon", "squares-subtract", "squares-subtract-icon", "squares-unite", "squares-unite-icon", "squircle", "squircle-icon", "squirrel", "squirrel-icon", "stamp", "stamp-icon", "star", "star-half", "star-half-icon", "star-icon", "star-off", "star-off-icon", "stars", "stars-icon", "step-back", "step-back-icon", "step-forward", "step-forward-icon", "stethoscope", "stethoscope-icon", "sticker", "sticker-icon", "sticky-note", "sticky-note-icon", "stop-circle", "stop-circle-icon", "store", "store-icon", "stretch-horizontal", "stretch-horizontal-icon", "stretch-vertical", "stretch-vertical-icon", "strikethrough", "strikethrough-icon", "subscript", "subscript-icon", "subtitles", "subtitles-icon", "sun", "sun-dim", "sun-dim-icon", "sun-icon", "sun-medium", "sun-medium-icon", "sun-moon", "sun-moon-icon", "sun-snow", "sun-snow-icon", "sunrise", "sunrise-icon", "sunset", "sunset-icon", "superscript", "superscript-icon", "swatch-book", "swatch-book-icon", "swiss-franc", "swiss-franc-icon", "switch-camera", "switch-camera-icon", "sword", "sword-icon", "swords", "swords-icon", "syringe", "syringe-icon", "table", "table2", "table2-icon", "table-cells-merge", "table-cells-merge-icon", "table-cells-split", "table-cells-split-icon", "table-columns-split", "table-columns-split-icon", "table-config", "table-config-icon", "table-icon", "table-of-contents", "table-of-contents-icon", "table-properties", "table-properties-icon", "table-rows-split", "table-rows-split-icon", "tablet", "tablet-icon", "tablet-smartphone", "tablet-smartphone-icon", "tablets", "tablets-icon", "tag", "tag-icon", "tags", "tags-icon", "tally1", "tally1-icon", "tally2", "tally2-icon", "tally3", "tally3-icon", "tally4", "tally4-icon", "tally5", "tally5-icon", "tangent", "tangent-icon", "target", "target-icon", "telescope", "telescope-icon", "tent", "tent-icon", "tent-tree", "tent-tree-icon", "terminal", "terminal-icon", "terminal-square", "terminal-square-icon", "test-tube", "test-tube2", "test-tube2-icon", "test-tube-diagonal", "test-tube-diagonal-icon", "test-tube-icon", "test-tubes", "test-tubes-icon", "text", "text-cursor", "text-cursor-icon", "text-cursor-input", "text-cursor-input-icon", "text-icon", "text-quote", "text-quote-icon", "text-search", "text-search-icon", "text-select", "text-select-icon", "text-selection", "text-selection-icon", "theater", "theater-icon", "thermometer", "thermometer-icon", "thermometer-snowflake", "thermometer-snowflake-icon", "thermometer-sun", "thermometer-sun-icon", "thumbs-down", "thumbs-down-icon", "thumbs-up", "thumbs-up-icon", "ticket", "ticket-check", "ticket-check-icon", "ticket-icon", "ticket-minus", "ticket-minus-icon", "ticket-percent", "ticket-percent-icon", "ticket-plus", "ticket-plus-icon", "ticket-slash", "ticket-slash-icon", "ticket-x", "ticket-x-icon", "tickets", "tickets-icon", "tickets-plane", "tickets-plane-icon", "timer", "timer-icon", "timer-off", "timer-off-icon", "timer-reset", "timer-reset-icon", "toggle-left", "toggle-left-icon", "toggle-right", "toggle-right-icon", "toilet", "toilet-icon", "tornado", "tornado-icon", "torus", "torus-icon", "touchpad", "touchpad-icon", "touchpad-off", "touchpad-off-icon", "tower-control", "tower-control-icon", "toy-brick", "toy-brick-icon", "tractor", "tractor-icon", "traffic-cone", "traffic-cone-icon", "train", "train-front", "train-front-icon", "train-front-tunnel", "train-front-tunnel-icon", "train-icon", "train-track", "train-track-icon", "tram-front", "tram-front-icon", "transgender", "transgender-icon", "trash", "trash2", "trash2-icon", "trash-icon", "tree-deciduous", "tree-deciduous-icon", "tree-palm", "tree-palm-icon", "tree-pine", "tree-pine-icon", "trees", "trees-icon", "trello", "trello-icon", "trending-down", "trending-down-icon", "trending-up", "trending-up-down", "trending-up-down-icon", "trending-up-icon", "triangle", "triangle-alert", "triangle-alert-icon", "triangle-dashed", "triangle-dashed-icon", "triangle-icon", "triangle-right", "triangle-right-icon", "trophy", "trophy-icon", "truck", "truck-electric", "truck-electric-icon", "truck-icon", "turtle", "turtle-icon", "tv", "tv2", "tv2-icon", "tv-icon", "tv-minimal", "tv-minimal-icon", "tv-minimal-play", "tv-minimal-play-icon", "twitch", "twitch-icon", "twitter", "twitter-icon", "type", "type-icon", "type-outline", "type-outline-icon", "umbrella", "umbrella-icon", "umbrella-off", "umbrella-off-icon", "underline", "underline-icon", "undo", "undo2", "undo2-icon", "undo-dot", "undo-dot-icon", "undo-icon", "unfold-horizontal", "unfold-horizontal-icon", "unfold-vertical", "unfold-vertical-icon", "ungroup", "ungroup-icon", "university", "university-icon", "unlink", "unlink2", "unlink2-icon", "unlink-icon", "unlock", "unlock-icon", "unlock-keyhole", "unlock-keyhole-icon", "unplug", "unplug-icon", "upload", "upload-cloud", "upload-cloud-icon", "upload-icon", "usb", "usb-icon", "user", "user2", "user2-icon", "user-check", "user-check2", "user-check2-icon", "user-check-icon", "user-circle", "user-circle2", "user-circle2-icon", "user-circle-icon", "user-cog", "user-cog2", "user-cog2-icon", "user-cog-icon", "user-icon", "user-lock", "user-lock-icon", "user-minus", "user-minus2", "user-minus2-icon", "user-minus-icon", "user-pen", "user-pen-icon", "user-plus", "user-plus2", "user-plus2-icon", "user-plus-icon", "user-round", "user-round-check", "user-round-check-icon", "user-round-cog", "user-round-cog-icon", "user-round-icon", "user-round-minus", "user-round-minus-icon", "user-round-pen", "user-round-pen-icon", "user-round-plus", "user-round-plus-icon", "user-round-search", "user-round-search-icon", "user-round-x", "user-round-x-icon", "user-search", "user-search-icon", "user-square", "user-square2", "user-square2-icon", "user-square-icon", "user-x", "user-x2", "user-x2-icon", "user-x-icon", "users", "users2", "users2-icon", "users-icon", "users-round", "users-round-icon", "utensils", "utensils-crossed", "utensils-crossed-icon", "utensils-icon", "utility-pole", "utility-pole-icon", "variable", "variable-icon", "vault", "vault-icon", "vegan", "vegan-icon", "venetian-mask", "venetian-mask-icon", "venus", "venus-and-mars", "venus-and-mars-icon", "venus-icon", "verified", "verified-icon", "vibrate", "vibrate-icon", "vibrate-off", "vibrate-off-icon", "video", "video-icon", "video-off", "video-off-icon", "videotape", "videotape-icon", "view", "view-icon", "voicemail", "voicemail-icon", "volleyball", "volleyball-icon", "volume", "volume1", "volume1-icon", "volume2", "volume2-icon", "volume-icon", "volume-off", "volume-off-icon", "volume-x", "volume-x-icon", "vote", "vote-icon", "wallet", "wallet2", "wallet2-icon", "wallet-cards", "wallet-cards-icon", "wallet-icon", "wallet-minimal", "wallet-minimal-icon", "wallpaper", "wallpaper-icon", "wand", "wand2", "wand2-icon", "wand-icon", "wand-sparkles", "wand-sparkles-icon", "warehouse", "warehouse-icon", "washing-machine", "washing-machine-icon", "watch", "watch-icon", "waves", "waves-icon", "waves-ladder", "waves-ladder-icon", "waypoints", "waypoints-icon", "webcam", "webcam-icon", "webhook", "webhook-icon", "webhook-off", "webhook-off-icon", "weight", "weight-icon", "wheat", "wheat-icon", "wheat-off", "wheat-off-icon", "whole-word", "whole-word-icon", "wifi", "wifi-high", "wifi-high-icon", "wifi-icon", "wifi-low", "wifi-low-icon", "wifi-off", "wifi-off-icon", "wifi-pen", "wifi-pen-icon", "wifi-zero", "wifi-zero-icon", "wind", "wind-arrow-down", "wind-arrow-down-icon", "wind-icon", "wine", "wine-icon", "wine-off", "wine-off-icon", "workflow", "workflow-icon", "worm", "worm-icon", "wrap-text", "wrap-text-icon", "wrench", "wrench-icon", "x", "x-circle", "x-circle-icon", "x-icon", "x-octagon", "x-octagon-icon", "x-square", "x-square-icon", "youtube", "youtube-icon", "zap", "zap-icon", "zap-off", "zap-off-icon", "zoom-in", "zoom-in-icon", "zoom-out", "zoom-out-icon"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}