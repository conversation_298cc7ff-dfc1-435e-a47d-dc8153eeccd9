import { GROUPS } from "@/backend/constants/groups";
import { lexicalEditor } from "@payloadcms/richtext-lexical";
import { GlobalConfig } from "payload";

export const HistoriaPageBanner: GlobalConfig = {
    slug: "about-us-page-historia-page-banner",
    admin: {
        group: GROUPS.ABOUT_US,
    },
    label: "Historia (Banner)",
    fields: [
        {
            name: "title",
            type: "text",
            required: true,
            label: "Título",
            admin: {
                description: "Título que se mostrará en la página de Sobre Nosotros",
            },
        },
        {
            name: 'descripcion',
            type: "richText",
            editor: lexicalEditor(),
            required: true,
            label: "Descripción",
            admin: {
                description: "Historia que se mostrará en la página de Sobre Nosotros",
            },
        },
    ],
}