'use client'
import { AcademicActivity, AcademicActivityCategory } from '@/payload-types'
import { Text } from '@/components/ui/text'
import { StaggerFade } from '@/components/motion'
import ActivityCard from '../grid/activity-card'

interface AcademicActivityRelatedProps {
  activities: AcademicActivity[]
}

export default function AcademicActivityRelated({ activities }: AcademicActivityRelatedProps) {
  if (activities.length === 0) {
    return null
  }
  // const getCategoryInfo = (activity: AcademicActivity) => {
  //   const category = activity.category as AcademicActivityCategory
  //   return category
  // }

  return (
    <section>
      <div className="text-center mb-8">
        <Text variant="h2" className="text-primary mb-4">
          Actividades Relacionadas
        </Text>
        <Text variant="description" className="text-muted-foreground max-w-2xl mx-auto">
          Descubre otras actividades académicas que podrían interesarte
        </Text>
      </div>

      <StaggerFade className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {activities.map((activity) => {
          return (
            <ActivityCard
              key={activity.id}
              activity={activity}
            />
          )
        })}
      </StaggerFade>
    </section>
  )
}
