'use client'
import { Text } from '@/components/ui/text'
import { HomeAboutUs } from '@/payload-types'
import { motion } from 'motion/react'

export default function AboutUsTitle({ data }: { data: HomeAboutUs }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      className="mx-auto text-center flex flex-col items-center"
    >
      <Text variant="h1" className="mb-2 text-5xl font-bold">
        {data.title}
      </Text>
      <div className="w-32 h-[1px] bg-primary mx-auto my-4"></div>
      <Text variant="h3" className="mb-6 font-semibold text-2xl">
        {data.subtitle}
      </Text>
    </motion.div>
  )
}
