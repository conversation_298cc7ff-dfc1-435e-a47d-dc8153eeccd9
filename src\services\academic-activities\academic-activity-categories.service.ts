import { getPayloadClient } from '@/lib/payload'
import type { AcademicActivityCategory } from '@/payload-types'
import { DataFromCollectionSlug, FindArgs, PaginatedDocs } from 'payload'

export interface GetCategoriesParams {
  page?: number
  limit?: number
  search?: string
  active?: boolean
  sort?: 'name-asc' | 'name-desc' | 'newest' | 'oldest'
}

/**
 * Servicio para gestionar categorías de actividades académicas
 */
export const AcademicActivityCategoriesService = {
  /**
   * Obtiene todas las categorías con filtros y paginación
   */
  async getCategories(params: GetCategoriesParams = {}): Promise<PaginatedDocs<DataFromCollectionSlug<"academic-activity-categories">>> {
    const {
      page = 1,
      limit = 50,
      search = '',
      active,
      sort = 'name-asc',
    } = params

    try {
      const payload = await getPayloadClient()

      const query: Omit<FindArgs, "collection"> = {
        where: {},
        page,
        limit,
      }

      // Filtro por búsqueda de texto
      if (search) {
        query.where = {
          or: [
            { name: { like: search } },
            { description: { like: search } },
          ],
        }
      }

      // Filtro por estado activo
      if (active !== undefined) {
        query.where = {
          ...query.where,
          active: { equals: active },
        }
      }

      // Configurar ordenamiento
      switch (sort) {
        case 'name-asc':
          query.sort = 'name'
          break
        case 'name-desc':
          query.sort = '-name'
          break
        case 'newest':
          query.sort = '-createdAt'
          break
        case 'oldest':
          query.sort = 'createdAt'
          break
        default:
          query.sort = 'name'
      }

      const response = await payload.find({
        collection: "academic-activity-categories",
        ...query,
        depth: 1,
        locale: 'all',
      })

      return response
    } catch (error) {
      console.error('Error al obtener categorías:', error)
      return {
        docs: [],
        totalDocs: 0,
        limit,
        totalPages: 0,
        page,
        pagingCounter: 0,
        hasPrevPage: false,
        hasNextPage: false,
        prevPage: null,
        nextPage: null,
      }
    }
  },

  /**
   * Obtiene todas las categorías activas (para selects y filtros)
   */
  async getActiveCategories() {
    try {
      const response = await this.getCategories({
        active: true,
        limit: 100,
        sort: 'name-asc',
      })
      return response.docs
    } catch (error) {
      console.error('Error al obtener categorías activas:', error)
      return []
    }
  },

  /**
   * Obtiene una categoría por su ID
   */
  async getCategoryById(id: string): Promise<AcademicActivityCategory | null> {
    try {
      const payload = await getPayloadClient()

      const response = await payload.find({
        collection: 'academic-activity-categories',
        where: {
          id: { equals: parseInt(id) },
        },
        depth: 1,
        limit: 1,
      })

      return response.docs.length > 0 ? (response.docs[0] as AcademicActivityCategory) : null
    } catch (error) {
      console.error('Error al obtener categoría por ID:', error)
      return null
    }
  },

  /**
   * Obtiene categorías con el conteo de actividades asociadas
   */
  async getCategoriesWithCount() {
    try {
      const payload = await getPayloadClient()

      const categories = await this.getActiveCategories()

      const categoriesWithCount = await Promise.all(
        categories.map(async (category) => {
          const activitiesResponse = await payload.find({
            collection: 'academic-activities',
            where: {
              category: { equals: category.id },
              status: { equals: 'active' },
            },
            limit: 0, // Solo queremos el conteo
          })

          return {
            ...category,
            activityCount: activitiesResponse.totalDocs,
          }
        })
      )

      return categoriesWithCount
    } catch (error) {
      console.error('Error al obtener categorías con conteo:', error)
      return []
    }
  },
}
