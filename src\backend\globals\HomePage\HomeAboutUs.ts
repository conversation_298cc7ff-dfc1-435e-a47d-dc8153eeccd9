import { ButtonFields, ButtonLabels } from '@/backend/constants/fields/buttons.field'
import { MediaFields, MediaLabels } from '@/backend/constants/fields/media.field'
import { GROUPS } from '@/backend/constants/groups'
import { GlobalConfig } from 'payload'

export const HomeAboutUs: GlobalConfig = {
  slug: 'home-about-us',
  label: 'Sobre Nosotros',
  admin: {
    group: GROUPS.HOME,
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      label: 'Título principal',
      admin: {
        description:
          "Título principal que se mostrará en la sección 'Sobre nosotros' de la página principal",
        placeholder: 'Sobre Nosotros',
      },
    },
    {
      name: 'subtitle',
      type: 'text',
      required: true,
      label: 'Subtítulo',
      admin: {
        description:
          "Subtítulo que se mostrará en la sección 'Sobre nosotros' de la página principal",
        placeholder: 'Academia Peruana de Doctores',
      },
    },
    {
      name: 'description',
      type: 'text',
      required: true,
      label: 'Descripción',
      admin: {
        description:
          "Descripción que se mostrará en la sección 'Sobre nosotros' de la página principal",
        placeholder: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed non risus.',
      },
    },
    {
      name: 'buttons',
      type: 'array',
      label: 'Botones',
      admin: {
        description: 'Botones de acción para este slide',
      },
      labels: ButtonLabels,
      fields: ButtonFields,
    },
    {
      name: 'highlights',
      labels: {
        singular: 'Logro',
        plural: 'Logros',
      },
      type: 'array',
      label: 'Logros',
      admin: {
        description: 'Lista de logros o datos destacados en indicadores cuantificables',
      },
      fields: [
        {
          name: 'number',
          type: 'number',
          required: true,
          label: 'Número',
          admin: {
            placeholder: 'Ejemplo: 10',
          },
        },
        {
          name: 'description',
          type: 'textarea',
          required: true,
          label: 'Descripción',
        },
        {
          name: 'plus',
          type: 'checkbox',
          label: "Añadir símbolo '+' después del número",
          defaultValue: false,
          admin: {
            description: "Activa esta opción para mostrar un '+' después del número.",
          },
        },
      ],
    },
    {
      name: 'gallery',
      type: 'array',
      label: 'Galería de Imágenes',
      maxRows: 3,
      admin: {
        description: 'Sube imágenes para la galería de la sección "Sobre Nosotros".',
      },
      labels: MediaLabels,
      fields: MediaFields,
    },
  ],
}
