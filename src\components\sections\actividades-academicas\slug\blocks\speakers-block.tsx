'use client'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Text } from '@/components/ui/text'
import { ExternalLinkIcon, GlobeIcon, MailIcon, Users } from 'lucide-react'
import { Media, Speaker } from '@/payload-types'
import { speakerLinksOptions, speakerNameWithDegree } from '@/backend/collections/Events/Speakers/utils'
import TwitterIcon from '@/components/shared/icons/TwitterIcon'
import Linkedin from '@/components/shared/icons/LinkedinIcon'
interface SpeakersBlockProps {
  block: {
    blockType: 'speakers'
    title?: string
    speakers?: Speaker[]
  }
}

export default function SpeakersBlock({ block }: SpeakersBlockProps) {
  const allSpeakers = [
    ...(block.speakers || []),
  ]

  if (allSpeakers.length === 0) {
    return null
  }

  const getSpeakerSubtitle = (speaker: Speaker) => {
    const parts = []
    if (speaker.specialty) parts.push(speaker.specialty)
    if (speaker.institution) parts.push(speaker.institution)
    if (speaker.position) parts.push(speaker.position)
    return parts.join(' • ')
  }

  const iconByPlatform = {
    linkedin: Linkedin,
    twitter: TwitterIcon,
    website: GlobeIcon,
    email: MailIcon,
    other: ExternalLinkIcon,
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="w-5 h-5 text-primary" />
          {block.title || 'Ponentes'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {allSpeakers.map((speaker, index) => (
            <div
              key={speaker.id || index}
              className="flex gap-4 p-4 border rounded-lg hover:shadow-sm transition-all"
            >
              <Avatar className="h-16 w-16 flex-shrink-0">
                <AvatarImage
                  src={(speaker.photo as Media)?.url || '/placeholder-avatar.jpg'}
                  alt={speaker.fullName}
                />
                <AvatarFallback className="text-sm">
                  {(speaker.fullName)
                    .split(' ')
                    .map((n: string) => n[0])
                    .join('')
                    .toUpperCase()
                    .slice(0, 2)}
                </AvatarFallback>
              </Avatar>

              <div className="flex-1 min-w-0">
                <Text variant="h4" className="text-base font-semibold mb-1">
                  {speakerNameWithDegree(speaker as Speaker)}
                </Text>

                {getSpeakerSubtitle(speaker as Speaker) && (
                  <Text variant="base" className="text-sm text-muted-foreground mb-2">
                    {getSpeakerSubtitle(speaker as Speaker)}
                  </Text>
                )}

                {speaker.bio && (
                  <Text variant="base" className="text-sm leading-relaxed mb-3 line-clamp-3">
                    {/* {speaker.bio} Rich text*/}
                    bio
                  </Text>
                )}

                {/* Contact Links */}
                {
                  speaker.socialLinks?.length && (
                    <div className="flex gap-2 flex-wrap">
                      {
                        speaker.socialLinks?.map((link, index) => {
                          let socialLink = speakerLinksOptions.find(option => option.value === link.platform)
                          if (!socialLink && link.customPlatform) {
                            socialLink = { label: link.customPlatform, value: "other" }
                          }

                          const IconComponent = iconByPlatform[link.platform]

                          return (
                            <Button
                              key={index}
                              variant="outline"
                              size="sm"
                              asChild
                            >
                              <a href={link.url} target="_blank" rel="noopener noreferrer">
                                <IconComponent className="h-3 w-3 mr-1" />
                                {link.platform.charAt(0).toUpperCase() + link.platform.slice(1)}
                              </a>
                            </Button>
                          );
                        })
                      }
                    </div>
                  )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
