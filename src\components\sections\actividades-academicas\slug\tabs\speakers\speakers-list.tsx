"use client";

import React, { useEffect, useId, useRef, useState } from "react";
import { AnimatePresence, motion } from "motion/react";
import { Speaker, Media } from "@/payload-types";
import { Text } from "@/components/ui/text";
import { Button } from "@/components/ui/button";
import {
    GlobeIcon,
    MailIcon,
    ExternalLinkIcon,
    XIcon,
    UserIcon,
    BuildingIcon,
    BriefcaseIcon,
} from "lucide-react";
import LinkedinIcon from "@/components/shared/icons/LinkedinIcon";
import TwitterIcon from "@/components/shared/icons/TwitterIcon";

import { speakerNameWithDegree } from "@/backend/collections/Events/Speakers/utils";
import { useOutsideClick } from "@/hooks/use-click-outside";

interface SpeakersTabProps {
    speakers: Speaker[];
}

export default function SpeakersList({ speakers }: SpeakersTabProps) {
    const [active, setActive] = useState<Speaker | null>(null);
    const ref = useRef<HTMLDivElement>(null);
    const id = useId();

    useEffect(() => {
        function onKeyDown(event: KeyboardEvent) {
            if (event.key === "Escape") {
                setActive(null);
            }
        }

        if (active) {
            document.body.style.overflow = "hidden";
        } else {
            document.body.style.overflow = "auto";
        }

        window.addEventListener("keydown", onKeyDown);
        return () => window.removeEventListener("keydown", onKeyDown);
    }, [active]);

    useOutsideClick(ref, () => setActive(null));

    // Función para obtener la URL de la imagen
    const getImageUrl = (photo: Speaker['photo']): string => {
        if (!photo) return '/placeholders/speaker.jpg';
        const media = photo as Media;
        return media.url || '/placeholders/speaker.jpg';
    };

    // Función para obtener el ícono de la plataforma social
    const getSocialIcon = (platform: string) => {
        const iconMap = {
            linkedin: LinkedinIcon,
            twitter: TwitterIcon,
            website: GlobeIcon,
            email: MailIcon,
            other: ExternalLinkIcon
        };

        return iconMap[platform as keyof typeof iconMap] || ExternalLinkIcon;
    };

    // Función para formatear la URL del enlace social
    const formatSocialUrl = (platform: string, url: string): string => {
        if (platform === 'email' && !url.startsWith('mailto:')) {
            return `mailto:${url}`;
        }
        return url;
    };

    if (!speakers || speakers.length === 0) {
        return (
            <div className="text-center py-8">
                <UserIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <Text variant="h3" className="text-gray-500 mb-2">
                    No hay ponentes disponibles
                </Text>
                <Text className="text-gray-400">
                    Los ponentes se mostrarán aquí cuando estén disponibles.
                </Text>
            </div>
        );
    }

    return (
        <>
            {/* Modal expandido */}
            <AnimatePresence>
                {active && (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="fixed inset-0 bg-black/20 h-full w-full z-10"
                    />
                )}
            </AnimatePresence>

            <AnimatePresence>
                {active && (
                    <div className="fixed inset-0 grid place-items-center z-[100]">
                        <motion.button
                            key={`button-${active.fullName}-${id}`}
                            layout
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0, transition: { duration: 0.05 } }}
                            className="flex absolute top-2 right-2 lg:hidden items-center justify-center bg-white rounded-full h-6 w-6"
                            onClick={() => setActive(null)}
                        >
                            <XIcon className="h-4 w-4 text-black" />
                        </motion.button>

                        <motion.div
                            layoutId={`card-${active.fullName}-${id}`}
                            ref={ref}
                            className="w-full max-w-[500px] h-full md:h-fit md:max-h-[90%] flex flex-col bg-white dark:bg-neutral-900 sm:rounded-3xl overflow-hidden"
                        >
                            {/* Imagen del ponente */}
                            <motion.div layoutId={`image-${active.fullName}-${id}`}>
                                <img
                                    width={200}
                                    height={200}
                                    src={getImageUrl(active.photo)}
                                    alt={active.fullName}
                                    className="w-full h-80 lg:h-80 sm:rounded-tr-lg sm:rounded-tl-lg object-contain"
                                />
                            </motion.div>

                            <div>
                                {/* Header con información básica */}
                                <div className="flex justify-between items-start p-4">
                                    <div className="flex-1">
                                        <motion.h3
                                            layoutId={`title-${active.fullName}-${id}`}
                                            className="font-bold text-neutral-700 dark:text-neutral-200 text-lg"
                                        >
                                            {active.fullName}
                                        </motion.h3>

                                        {active.specialty && (
                                            <motion.p
                                                layoutId={`specialty-${active.specialty}-${id}`}
                                                className="text-neutral-600 dark:text-neutral-400 font-medium"
                                            >
                                                {active.specialty}
                                            </motion.p>
                                        )}
                                    </div>
                                </div>

                                {/* Contenido detallado */}
                                <div className="pt-4 relative px-4">
                                    <motion.div
                                        layout
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        exit={{ opacity: 0 }}
                                        className="text-neutral-600 text-xs md:text-sm lg:text-base h-40 md:h-fit pb-10 flex flex-col items-start gap-4 overflow-auto dark:text-neutral-400 [mask:linear-gradient(to_bottom,white,white,transparent)] [scrollbar-width:none] [-ms-overflow-style:none] [-webkit-overflow-scrolling:touch]"
                                    >
                                        {/* Información institucional */}
                                        {(active.institution || active.position) && (
                                            <div className="space-y-2">
                                                {active.institution && (
                                                    <div className="flex items-center gap-2">
                                                        <BuildingIcon className="w-4 h-4 text-gray-500" />
                                                        <span className="text-sm">{active.institution}</span>
                                                    </div>
                                                )}
                                                {active.position && (
                                                    <div className="flex items-center gap-2">
                                                        <BriefcaseIcon className="w-4 h-4 text-gray-500" />
                                                        <span className="text-sm">{active.position}</span>
                                                    </div>
                                                )}
                                            </div>
                                        )}

                                        {/* Biografía */}
                                        {active.bio && (
                                            <div className="space-y-2">
                                                <h4 className="font-semibold text-neutral-800 dark:text-neutral-200">
                                                    Biografía
                                                </h4>
                                                <div
                                                    className="prose prose-sm max-w-none text-neutral-600 dark:text-neutral-400"
                                                    dangerouslySetInnerHTML={{ __html: active.bio }}
                                                />
                                            </div>
                                        )}

                                        {/* Enlaces sociales */}
                                        {active.socialLinks && active.socialLinks.length > 0 && (
                                            <div className="space-y-2">
                                                <h4 className="font-semibold text-neutral-800 dark:text-neutral-200">
                                                    Enlaces
                                                </h4>
                                                <div className="flex flex-wrap gap-2">
                                                    {active.socialLinks.map((link, index) => {
                                                        const IconComponent = getSocialIcon(link.platform);
                                                        return (
                                                            <Button
                                                                key={index}
                                                                variant="outline"
                                                                size="sm"
                                                                asChild
                                                            >
                                                                <a
                                                                    href={formatSocialUrl(link.platform, link.url)}
                                                                    target="_blank"
                                                                    rel="noopener noreferrer"
                                                                    className="flex items-center gap-2"
                                                                >
                                                                    <IconComponent className="w-4 h-4" />
                                                                    {link.platform.charAt(0).toUpperCase() + link.platform.slice(1)}
                                                                </a>
                                                            </Button>
                                                        );
                                                    })}
                                                </div>
                                            </div>
                                        )}
                                    </motion.div>
                                </div>
                            </div>
                        </motion.div>
                    </div>
                )}
            </AnimatePresence>

            {/* Lista de tarjetas de speakers */}
            <div className="max-w-4xl w-full space-y-4">
                {speakers.map((speaker, index) => (
                    <motion.div
                        layoutId={`card-${speaker.fullName}-${id}`}
                        key={`card-${speaker.fullName}-${id}`}
                        onClick={() => setActive(speaker)}
                        className="py-4 flex flex-col md:flex-row justify-between items-center hover:bg-neutral-50 dark:hover:bg-neutral-800 rounded-xl cursor-pointer border border-gray-200 hover:border-gray-300 transition-all duration-200"
                        whileHover={{ y: -2 }}
                        transition={{ duration: 0.2 }}
                    >
                        <div className="flex gap-4 flex-col md:flex-row items-center md:items-start">
                            {/* Imagen del ponente */}
                            <motion.div layoutId={`image-${speaker.fullName}-${id}`}>
                                <img
                                    width={100}
                                    height={100}
                                    src={getImageUrl(speaker.photo)}
                                    alt={speaker.fullName}
                                    className="h-20 w-20 md:h-16 md:w-16 rounded-full object-cover border-2 border-gray-200"
                                />
                            </motion.div>

                            {/* Información del ponente */}
                            <div className="text-center md:text-left">
                                <motion.h3
                                    layoutId={`title-${speaker.fullName}-${id}`}
                                    className="font-semibold text-neutral-800 dark:text-neutral-200 text-lg"
                                >
                                    {speakerNameWithDegree(speaker)}
                                </motion.h3>

                                {speaker.specialty && (
                                    <motion.p
                                        layoutId={`specialty-${speaker.specialty}-${id}`}
                                        className="text-neutral-600 dark:text-neutral-400 font-medium"
                                    >
                                        {speaker.specialty}
                                    </motion.p>
                                )}

                                {speaker.institution && (
                                    <p className="text-sm text-neutral-500 dark:text-neutral-500 flex items-center justify-center md:justify-start gap-1 mt-1">
                                        <BuildingIcon className="w-3 h-3" />
                                        {speaker.institution}
                                    </p>
                                )}
                            </div>
                        </div>

                        {/* Botón de ver más */}
                        <motion.button
                            layoutId={`button-${speaker.fullName}-${id}`}
                            className="px-4 py-2 text-sm rounded-full font-medium bg-gray-100 hover:bg-primary hover:text-white text-black mt-4 md:mt-0 transition-colors duration-200 cursor-pointer"
                        >
                            Ver detalles
                        </motion.button>
                    </motion.div>
                ))}
            </div>
        </>
    );
}
