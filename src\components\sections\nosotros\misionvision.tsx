import { AboutUsPageService } from "@/services/about-us.service";
import { Text } from "@/components/ui/text";
import { RichText } from "@payloadcms/richtext-lexical/react";

export default async function NosotrosMisionVision() {
    const misionVision = await AboutUsPageService.getMisionVision();

    if (!misionVision) {
        return (
            <section>
                <div className="container mx-auto py-20 flex">
                    <div className="flex-1">
                        <h2 className="text-4xl font-bold">Misión y Visión</h2>
                    </div>
                    <div className="flex-1">
                        <p>No se encontró la misión y visión.</p>
                    </div>
                </div>
            </section>
        )
    }

    return (
        <section className="bg-gray-100">
            <div className="container mx-auto py-20">
                <Text variant="h2" className="text-center mb-10">
                    Nuestro Propósito
                </Text>

                <div className="flex gap-10 text-center">
                    <div className="flex-1">
                        <Text variant="h3" className="mb-4">
                            Misión
                        </Text>
                        <RichText
                            data={misionVision.mision}
                        />
                    </div>
                    <div className="flex-1">
                        <Text variant="h3" className="mb-4">
                            Visión
                        </Text>
                        <RichText
                            data={misionVision.vision}
                        />
                    </div>
                </div>
            </div>
        </section>
    )
}