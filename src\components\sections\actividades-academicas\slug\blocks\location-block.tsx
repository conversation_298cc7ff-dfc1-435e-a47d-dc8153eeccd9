'use client'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Text } from '@/components/ui/text'
import { MapPin, Monitor, Users, ExternalLink, Navigation } from 'lucide-react'

interface LocationBlockProps {
  block: {
    blockType: 'location'
    title?: string
    modality: 'online' | 'presencial' | 'hybrid'
    venue?: string
    address?: string
    city?: string
    country?: string
    meetingLink?: string
    meetingId?: string
    meetingPassword?: string
    instructions?: string
    mapUrl?: string
  }
}

export default function LocationBlock({ block }: LocationBlockProps) {
  const getModalityInfo = () => {
    switch (block.modality) {
      case 'online':
        return {
          icon: Monitor,
          title: 'Evento Virtual',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50 border-blue-200',
        }
      case 'presencial':
        return {
          icon: MapPin,
          title: 'Evento Presencial',
          color: 'text-green-600',
          bgColor: 'bg-green-50 border-green-200',
        }
      case 'hybrid':
        return {
          icon: Users,
          title: 'Evento Híbrido',
          color: 'text-purple-600',
          bgColor: 'bg-purple-50 border-purple-200',
        }
      default:
        return {
          icon: MapPin,
          title: 'Ubicación',
          color: 'text-gray-600',
          bgColor: 'bg-gray-50 border-gray-200',
        }
    }
  }

  const modalityInfo = getModalityInfo()
  const Icon = modalityInfo.icon

  const getFullAddress = () => {
    const parts = []
    if (block.venue) parts.push(block.venue)
    if (block.address) parts.push(block.address)
    if (block.city) parts.push(block.city)
    if (block.country) parts.push(block.country)
    return parts.join(', ')
  }

  return (
    <Card className={modalityInfo.bgColor}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Icon className={`w-5 h-5 ${modalityInfo.color}`} />
          {block.title || modalityInfo.title}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Información de ubicación presencial */}
        {(block.modality === 'presencial' || block.modality === 'hybrid') && (
          <div className="space-y-3">
            {block.venue && (
              <div>
                <Text variant="base" className="font-medium text-gray-900">
                  {block.venue}
                </Text>
              </div>
            )}
            
            {getFullAddress() && (
              <div className="flex items-start gap-2">
                <MapPin className="w-4 h-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                <Text variant="base" className="text-sm text-muted-foreground">
                  {getFullAddress()}
                </Text>
              </div>
            )}

            {/* Botones de acción para ubicación */}
            <div className="flex gap-2 flex-wrap">
              {block.mapUrl && (
                <Button variant="outline" size="sm" asChild>
                  <a href={block.mapUrl} target="_blank" rel="noopener noreferrer">
                    <Navigation className="w-4 h-4 mr-2" />
                    Ver en Mapa
                  </a>
                </Button>
              )}
              
              {getFullAddress() && (
                <Button variant="outline" size="sm" asChild>
                  <a 
                    href={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(getFullAddress())}`}
                    target="_blank" 
                    rel="noopener noreferrer"
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Google Maps
                  </a>
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Información de acceso virtual */}
        {(block.modality === 'online' || block.modality === 'hybrid') && (
          <div className="space-y-3">
            <div className="p-3 bg-white rounded-lg border">
              <Text variant="base" className="font-medium text-gray-900 mb-2">
                Acceso Virtual
              </Text>
              
              {block.meetingLink && (
                <div className="mb-3">
                  <Button asChild className="w-full">
                    <a href={block.meetingLink} target="_blank" rel="noopener noreferrer">
                      <Monitor className="w-4 h-4 mr-2" />
                      Unirse a la Reunión
                    </a>
                  </Button>
                </div>
              )}

              {block.meetingId && (
                <div className="text-sm space-y-1">
                  <div>
                    <span className="font-medium">ID de reunión:</span> {block.meetingId}
                  </div>
                  {block.meetingPassword && (
                    <div>
                      <span className="font-medium">Contraseña:</span> {block.meetingPassword}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Instrucciones adicionales */}
        {block.instructions && (
          <div className="p-3 bg-white rounded-lg border">
            <Text variant="base" className="font-medium text-gray-900 mb-2">
              Instrucciones
            </Text>
            <Text variant="base" className="text-sm text-muted-foreground leading-relaxed">
              {block.instructions}
            </Text>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
