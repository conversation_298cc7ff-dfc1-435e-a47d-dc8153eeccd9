import { CACHE_TAGS } from '@/services/constants'
import { revalidateTag } from 'next/cache'
import { NextRequest } from 'next/server'

export async function POST(req: NextRequest) {
    try {
        // Verifica el token de seguridad
        const secret = req.headers.get('revalidate-secret')
        if (secret !== process.env.REVALIDATE_SECRET) {
            return new Response(JSON.stringify({ message: 'Invalid token' }), { status: 401 })
        }

        const body = await req.json()
        const { tag } = body

        if (!tag) {
            return new Response(JSON.stringify({ message: 'Tag is required' }), { status: 400 })
        }

        // verificar que el tag sea válido
        const validTags = Object.values(CACHE_TAGS).flat()
        if (!validTags.includes(tag)) {
            return new Response(JSON.stringify({ message: 'Invalid tag' }), { status: 400 })
        }

        // Revalida el tag especificado
        revalidateTag(tag)
        return new Response(JSON.stringify({ revalidated: true }), { status: 200 })
    } catch (err) {
        console.error('Error al revalidar:', err)
        return new Response(JSON.stringify({ message: 'Error revalidating' }), { status: 500 })
    }
}