import { getPayloadClient } from '@/lib/payload'
import { HomeWorkArea } from '@/payload-types'

export const WorkAreasService = {
  async getWorkAreasData(): Promise<HomeWorkArea | null> {
    try {
      const payload = await getPayloadClient()

      // Obtener los datos de las áreas de trabajo desde el global
      const workAreasData = await payload.findGlobal({
        slug: 'home-work-areas',
        depth: 2, // Para cargar las relaciones con las imágenes
      })

      return workAreasData
    } catch (error) {
      console.error('Error al obtener los datos de Áreas de Trabajo:', error)
      return null
    }
  },
}
