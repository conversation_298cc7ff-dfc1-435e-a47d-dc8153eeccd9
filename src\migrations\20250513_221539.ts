import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   CREATE TYPE "public"."enum_home_about_us_buttons_variant" AS ENUM('white', 'secondary', 'default', 'link', 'destructive');
  CREATE TYPE "public"."enum_home_about_us_buttons_size" AS ENUM('default', 'sm', 'lg');
  CREATE TABLE IF NOT EXISTS "home_about_us_buttons" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"text" varchar NOT NULL,
  	"href" varchar NOT NULL,
  	"icon" varchar,
  	"variant" "enum_home_about_us_buttons_variant" DEFAULT 'default' NOT NULL,
  	"size" "enum_home_about_us_buttons_size" DEFAULT 'default' NOT NULL
  );
  
  CREATE TABLE IF NOT EXISTS "home_about_us_highlights" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"number" numeric NOT NULL,
  	"description" varchar NOT NULL,
  	"plus" boolean DEFAULT false
  );
  
  CREATE TABLE IF NOT EXISTS "home_about_us_gallery" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"image_id" integer NOT NULL
  );
  
  CREATE TABLE IF NOT EXISTS "home_about_us" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"title" varchar NOT NULL,
  	"subtitle" varchar NOT NULL,
  	"description" varchar NOT NULL,
  	"updated_at" timestamp(3) with time zone,
  	"created_at" timestamp(3) with time zone
  );
  
  DO $$ BEGIN
   ALTER TABLE "home_about_us_buttons" ADD CONSTRAINT "home_about_us_buttons_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."home_about_us"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "home_about_us_highlights" ADD CONSTRAINT "home_about_us_highlights_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."home_about_us"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "home_about_us_gallery" ADD CONSTRAINT "home_about_us_gallery_image_id_media_id_fk" FOREIGN KEY ("image_id") REFERENCES "public"."media"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "home_about_us_gallery" ADD CONSTRAINT "home_about_us_gallery_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."home_about_us"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "home_about_us_buttons_order_idx" ON "home_about_us_buttons" USING btree ("_order");
  CREATE INDEX IF NOT EXISTS "home_about_us_buttons_parent_id_idx" ON "home_about_us_buttons" USING btree ("_parent_id");
  CREATE INDEX IF NOT EXISTS "home_about_us_highlights_order_idx" ON "home_about_us_highlights" USING btree ("_order");
  CREATE INDEX IF NOT EXISTS "home_about_us_highlights_parent_id_idx" ON "home_about_us_highlights" USING btree ("_parent_id");
  CREATE INDEX IF NOT EXISTS "home_about_us_gallery_order_idx" ON "home_about_us_gallery" USING btree ("_order");
  CREATE INDEX IF NOT EXISTS "home_about_us_gallery_parent_id_idx" ON "home_about_us_gallery" USING btree ("_parent_id");
  CREATE INDEX IF NOT EXISTS "home_about_us_gallery_image_idx" ON "home_about_us_gallery" USING btree ("image_id");`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   DROP TABLE "home_about_us_buttons" CASCADE;
  DROP TABLE "home_about_us_highlights" CASCADE;
  DROP TABLE "home_about_us_gallery" CASCADE;
  DROP TABLE "home_about_us" CASCADE;
  DROP TYPE "public"."enum_home_about_us_buttons_variant";
  DROP TYPE "public"."enum_home_about_us_buttons_size";`)
}
