'use client'
import { AcademicActivity, AcademicActivityCategory, AcademicActivityTag, AcademicActivityMedia } from '@/payload-types'
import { Text } from '@/components/ui/text'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { motion } from 'motion/react'
import { Calendar, Clock, MapPin, Monitor, Users } from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'
import { format } from 'date-fns'
import { es } from 'date-fns/locale'

interface FeaturedActivitySectionProps {
    initialActivity?: AcademicActivity | null
}

export default function FeaturedActivitySection({ initialActivity }: FeaturedActivitySectionProps) {
    const activity = initialActivity

    if (!activity) {
        return null
    }

    const getImageUrl = () => {
        const image = activity.mainImage as AcademicActivityMedia
        return image?.url || '/placeholder-activity.jpg'
    }

    const getCategoryName = () => {
        const category = activity.category as AcademicActivityCategory
        return category?.name || ''
    }

    const getCategoryColor = () => {
        const category = activity.category as AcademicActivityCategory
        return category?.color || '#010012'
    }

    const getTags = () => {
        if (!activity.tags) return []
        return (activity.tags as AcademicActivityTag[]).slice(0, 3)
    }

    const getLocationInfo = () => {
        const locationBlock = activity.eventBlocks?.find(block => block.blockType === 'location')
        if (!locationBlock || locationBlock.blockType !== 'location') return null

        const modality = locationBlock.modality
        let locationText = ''
        let icon = MapPin

        switch (modality) {
            case 'online':
                locationText = 'Virtual'
                icon = Monitor
                break
            case 'presencial':
                locationText = locationBlock.venue || locationBlock.address || 'Presencial'
                icon = MapPin
                break
            case 'hybrid':
                locationText = 'Híbrido'
                icon = Users
                break
            default:
                locationText = 'Por definir'
        }

        return { text: locationText, icon, modality }
    }

    const formatDate = (dateString: string) => {
        const date = new Date(dateString)
        return format(date, "d 'de' MMMM, yyyy", { locale: es })
    }

    const formatTime = (dateString: string) => {
        const date = new Date(dateString)
        return format(date, 'HH:mm', { locale: es })
    }

    const locationInfo = getLocationInfo()

    return (
        <section className="py-16 bg-gray-50">
            <div className="container mx-auto px-4">
                <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                    className="text-center mb-12"
                >
                    <Text variant="h2" className="text-primary mb-4">
                        Actividad Destacada
                    </Text>
                    <Text variant="description" className="text-gray-600 max-w-2xl mx-auto">
                        No te pierdas nuestra próxima actividad académica destacada
                    </Text>
                </motion.div>

                <motion.div
                    initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.2 }}
                    viewport={{ once: true }}
                    className="max-w-4xl mx-auto"
                >
                    <Link href={`/actividades-academicas/${activity.slug}`}>
                        <div className="group bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 cursor-pointer">
                            {/* Imagen y categoría */}
                            <div className="relative h-64 md:h-80 overflow-hidden">
                                <img
                                    src={getImageUrl()}
                                    alt={activity.title}
                                    className="object-cover group-hover:scale-105 transition-transform duration-300 h-full w-full"
                                />

                                {/* Overlay gradiente */}
                                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />

                                {/* Badge de categoría */}
                                {getCategoryName() && (
                                    <motion.div
                                        className="absolute top-4 right-4 z-10"
                                        initial={{ opacity: 0, scale: 0.8 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        transition={{ delay: 0.3 }}
                                    >
                                        <span
                                            className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium text-white"
                                            style={{ backgroundColor: getCategoryColor() }}
                                        >
                                            {getCategoryName()}
                                        </span>
                                    </motion.div>
                                )}
                            </div>

                            {/* Contenido */}
                            <div className="p-6 md:p-8">
                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.4 }}
                                >
                                    <Text variant="h3" className="text-primary mb-4 group-hover:text-primary/90 transition-colors">
                                        {activity.title}
                                    </Text>

                                    <Text variant="base" className="text-gray-600 mb-6 line-clamp-3">
                                        {activity.excerpt}
                                    </Text>

                                    {/* Etiquetas */}
                                    {getTags().length > 0 && (
                                        <div className="flex flex-wrap gap-2 mb-6">
                                            {getTags().map((tag) => (
                                                <Badge key={tag.id} variant="secondary" className="text-xs">
                                                    {tag.name}
                                                </Badge>
                                            ))}
                                        </div>
                                    )}
                                </motion.div>
                            </div>

                            {/* Barra inferior con información del evento */}
                            <motion.div
                                className="bg-primary text-white p-4 md:p-6"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.5 }}
                            >
                                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                                    <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                                        {/* Fecha */}
                                        <div className="flex items-center gap-2">
                                            <Calendar className="w-4 h-4" />
                                            <span className="text-sm font-medium">
                                                {formatDate(activity.startDate)}
                                            </span>
                                        </div>

                                        {/* Hora */}
                                        <div className="flex items-center gap-2">
                                            <Clock className="w-4 h-4" />
                                            <span className="text-sm">
                                                {formatTime(activity.startDate)}
                                                {activity.endDate && ` - ${formatTime(activity.endDate)}`}
                                            </span>
                                        </div>

                                        {/* Ubicación/Modalidad */}
                                        {locationInfo && (
                                            <div className="flex items-center gap-2">
                                                <locationInfo.icon className="w-4 h-4" />
                                                <span className="text-sm">
                                                    {locationInfo.text}
                                                </span>
                                            </div>
                                        )}
                                    </div>

                                    <div className="flex gap-3">
                                        <Button variant="white" size="sm" className="group-hover:bg-white/90 transition-colors">
                                            Ver detalles
                                        </Button>
                                    </div>
                                </div>
                            </motion.div>
                        </div>
                    </Link>
                </motion.div>
            </div>
        </section>
    )
}