'use client'
import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { motion, AnimatePresence } from 'motion/react'
import { ListFilter, Search, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import ComboBox from '@/components/ui/combo-box'
import { DateRangePicker } from '@/components/ui/date-range-picker'

import { AcademicActivityCategory } from '@/payload-types'
import { DateRange } from 'react-day-picker'
import { Checkbox } from '@/components/ui/checkbox'

interface FilterState {
    search: string
    category: string
    dateRange: DateRange | undefined
    modality: string
    upcoming: boolean
}

interface AcademicActivitiesFiltersProps {
    initialData?: {
        categories: AcademicActivityCategory[]
    }
}

const modalityOptions = [
    { value: '', label: 'Todas las modalidades' },
    { value: 'online', label: 'Virtual' },
    { value: 'presencial', label: 'Presencial' },
    { value: 'hybrid', label: 'Híbrido' },
]

export default function AcademicActivitiesFilters({ initialData }: AcademicActivitiesFiltersProps) {
    const router = useRouter()
    const searchParams = useSearchParams()
    const [isOpen, setIsOpen] = useState(false)
    const categories = initialData?.categories || []
    const [filters, setFilters] = useState<FilterState>({
        search: '',
        category: '',
        dateRange: undefined,
        modality: '',
        upcoming: false,
    })

    // Sincronizar con search params
    useEffect(() => {
        const search = searchParams.get('search') || ''
        const category = searchParams.get('category') || ''
        const modality = searchParams.get('modality') || ''
        const startDate = searchParams.get('startDate')
        const endDate = searchParams.get('endDate')
        const upcoming = searchParams.get('upcoming') === 'true'

        let dateRange: DateRange | undefined = undefined
        if (startDate && endDate) {
            dateRange = {
                from: new Date(startDate),
                to: new Date(endDate),
            }
        }

        setFilters({
            search,
            category,
            dateRange,
            modality,
            upcoming,
        })
    }, [searchParams])

    const categoryOptions = [
        { value: '', label: 'Todas las categorías' },
        ...categories.map(cat => ({
            value: cat.id.toString(),
            label: cat.name,
        })),
    ]

    const updateSearchParams = (newFilters: Partial<FilterState>) => {
        const params = new URLSearchParams(searchParams.toString())

        // Actualizar parámetros
        Object.entries(newFilters).forEach(([key, value]) => {
            if (key === 'dateRange') {
                const dateRange = value as DateRange | undefined
                if (dateRange?.from) {
                    params.set('startDate', dateRange.from.toISOString().split('T')[0])
                } else {
                    params.delete('startDate')
                }
                if (dateRange?.to) {
                    params.set('endDate', dateRange.to.toISOString().split('T')[0])
                } else {
                    params.delete('endDate')
                }
            } else {
                if (value && value !== '') {
                    params.set(key, value as string)
                } else {
                    params.delete(key)
                }
            }
        })

        // Resetear página al filtrar
        params.delete('page')

        router.push(`?${params.toString()}`, {
            scroll: false,
        })
    }

    const handleApplyFilters = () => {
        updateSearchParams(filters)
        setIsOpen(false)
    }

    const handleClearFilters = () => {
        const clearedFilters: FilterState = {
            search: '',
            category: '',
            dateRange: undefined,
            modality: '',
            upcoming: false,
        }
        setFilters(clearedFilters)
        updateSearchParams(clearedFilters)
        setIsOpen(false)
    }

    const hasActiveFilters = filters.search || filters.category || filters.dateRange || filters.modality

    return (
        <section className="py-8 bg-white border-b">
            <div className="container mx-auto px-4">
                {/* Botón de filtros */}
                <div className="flex items-center justify-between mb-4">
                    <Button
                        variant="outline"
                        onClick={() => setIsOpen(!isOpen)}
                        className="flex items-center gap-2"
                    >
                        <ListFilter className="w-4 h-4" />
                        Filtros
                        {hasActiveFilters && (
                            <span className="ml-2 bg-primary text-primary-foreground text-xs px-2 py-0.5 rounded-full">
                                {[filters.search, filters.category, filters.dateRange, filters.modality].filter(Boolean).length}
                            </span>
                        )}
                    </Button>

                    {hasActiveFilters && (
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleClearFilters}
                            className="flex items-center gap-2"
                        >
                            <X className="w-4 h-4" />
                            Limpiar filtros
                        </Button>
                    )}
                </div>

                {/* Panel de filtros colapsible */}
                <AnimatePresence>
                    {isOpen && (
                        <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3, ease: 'easeInOut' }}
                            className="overflow-hidden"
                        >
                            <div className="bg-gray-50 rounded-lg p-6 space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 items-end">
                                    {/* Búsqueda de texto */}
                                    <div className="space-y-2">
                                        <Label htmlFor="search">Buscar</Label>
                                        <div className="relative">
                                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                            <Input
                                                id="search"
                                                type="text"
                                                placeholder="Buscar actividades..."
                                                value={filters.search}
                                                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                                                className="pl-10"
                                            />
                                        </div>
                                    </div>

                                    {/* Categoría */}
                                    <div className="space-y-2">
                                        <ComboBox
                                            label="Categoría"
                                            placeholder="Seleccionar categoría"
                                            items={categoryOptions}
                                            defaultValue={filters.category}
                                            handleSelect={(value: string) => setFilters(prev => ({ ...prev, category: value }))}
                                        />
                                    </div>

                                    {/* Rango de fechas */}
                                    <div className="space-y-2">
                                        <DateRangePicker
                                            label="Rango de fechas"
                                            placeholder="Seleccionar fechas"
                                            value={filters.dateRange}
                                            onChange={(dateRange) => setFilters(prev => ({ ...prev, dateRange }))}
                                            allowPastDates
                                        />
                                    </div>

                                    {/* Modalidad */}
                                    <div className="space-y-2">
                                        <ComboBox
                                            label="Modalidad"
                                            placeholder="Seleccionar modalidad"
                                            items={modalityOptions}
                                            defaultValue={filters.modality}
                                            handleSelect={(value: string) => setFilters(prev => ({ ...prev, modality: value }))}
                                        // handleClear={() => setFilters(prev => ({ ...prev, modality: '' }))}
                                        />
                                    </div>

                                    <div>
                                        <div>
                                            <Checkbox
                                                checked={filters.upcoming}
                                                onCheckedChange={(value) => setFilters(prev => ({ ...prev, upcoming: value === true, dateRange: undefined }))}
                                            />
                                            <span className="ml-2 text-sm text-gray-500">
                                                Solo próximas actividades
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {/* Botones de acción */}
                                <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
                                    <Button onClick={handleApplyFilters} className="flex-1 sm:flex-none">
                                        Aplicar filtros
                                    </Button>
                                    <Button variant="ghost" onClick={handleClearFilters} className="flex-1 sm:flex-none">
                                        Limpiar filtros
                                    </Button>
                                </div>
                            </div>
                        </motion.div>
                    )}
                </AnimatePresence>
            </div>
        </section>
    )
}