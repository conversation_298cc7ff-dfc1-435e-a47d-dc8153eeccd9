import { getPayloadClient } from '@/lib/payload'
import { HomeJoinUs } from '@/payload-types'

export const HomeJoinUsService = {
  async getHomeJoinUsData(): Promise<HomeJoinUs | null> {
    try {
      const payload = await getPayloadClient()

      // Obtener los datos de la sección "Únete a Nosotros" desde el global
      const joinUsData = await payload.findGlobal({
        slug: 'home-join-us',
        depth: 2, // Para cargar las relaciones con las imágenes
      })

      return joinUsData as HomeJoinUs
    } catch (error) {
      console.error('Error al obtener los datos de Únete a Nosotros:', error)
      return null
    }
  },
}
