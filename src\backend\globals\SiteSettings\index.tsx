import { GROUPS } from "@/backend/constants/groups";
import type { GlobalConfig } from "payload";

export const SiteSettings: GlobalConfig = {
    slug: 'general-settings',
    label: 'Información General',
    admin: {
        group: GROUPS.SETTINGS,
        description: 'Configuración general del sitio web (Logo, Contacto, Pie de Página, etc.)',
    },
    fields: [
        // Información General de la página ej: logo, nombre de entidad, contacto (email, redes sociales)
        {
            type: "group",
            label: "Información General",
            fields: [
                // Logo
                {
                    name: "logo",
                    type: "upload",
                    relationTo: "media",
                    label: "Logo",
                    admin: {
                        description: "Logo principal del sitio (dimensiones recomendadas: 80x80px)",
                    },
                    required: true,
                },
                {
                    name: "name",
                    type: "text",
                    label: "Nombre de la Entidad",
                    required: true,
                    admin: {
                        description: "Nombre de la entidad que se mostrará en la parte superior y pie de página del sitio",
                        placeholder: "Academia Peruana de Doctores"
                    }
                },
                // Información de contacto
                {
                    name: 'contactInfo',
                    type: 'group',
                    label: 'Información de Contacto',
                    fields: [
                        {
                            name: 'email',
                            type: 'email',
                            label: 'Email',
                        },
                        {
                            name: 'phone',
                            type: 'text',
                            label: 'Teléfono',
                        },
                        {
                            name: 'address',
                            type: 'textarea',
                            label: 'Dirección',
                        },
                    ],
                },
            ]
        },

        // Redes sociales
        {
            name: 'socialMedia',
            type: 'array',
            label: 'Redes Sociales',
            labels: {
                singular: 'Red Social',
                plural: 'Redes Sociales',
            },
            admin: {
                position: "sidebar",
            },
            fields: [
                {
                    name: 'platform',
                    type: 'select',
                    required: true,
                    label: 'Plataforma',
                    options: [
                        { label: 'Facebook', value: 'facebook' },
                        { label: 'Instagram', value: 'instagram' },
                        { label: 'LinkedIn', value: 'linkedin' },
                        { label: 'Twitter/X', value: 'twitter' },
                        { label: 'YouTube', value: 'youtube' },
                    ],
                },
                {
                    name: 'url',
                    type: 'text',
                    required: true,
                    label: 'URL',
                },
            ],
        },

        // Pie de Página Section
        {
            type: 'collapsible',
            label: 'Configuración del Pie de Página',
            admin: {
                initCollapsed: false,
            },
            fields: [
                {
                    name: 'footerDescription',
                    type: 'textarea',
                    label: 'Descripción',
                    admin: {
                        description: 'Descripción que aparece bajo el logo en el pie de página',
                        placeholder: "Academia Peruana de Doctores - Contribuyendo al progreso científico y académico del Perú y el mundo."
                    },
                },

                // Columnas del footer
                {
                    name: 'footerColumns',
                    type: 'array',
                    label: 'Columnas del Pie de Página',
                    labels: {
                        singular: 'Columna',
                        plural: 'Columnas',
                    },
                    admin: {
                        description: 'Columnas que aparecen en la parte inferior del pie de página',
                    },
                    maxRows: 4,
                    fields: [
                        {
                            name: 'title',
                            type: 'text',
                            required: true,
                            label: 'Título de la Columna',
                        },
                        {
                            name: 'links',
                            type: 'array',
                            label: 'Enlaces',
                            fields: [
                                {
                                    name: 'label',
                                    type: 'text',
                                    required: true,
                                    label: 'Etiqueta',
                                },
                                {
                                    name: 'href',
                                    type: 'text',
                                    required: true,
                                    label: 'Enlace',
                                },
                            ],
                        },
                    ],
                },

                // texto de copyright
                {
                    name: 'copyright',
                    type: 'text',
                    label: 'Texto de Copyright',
                    defaultValue: '© 2025 Academia Peruana de Doctores.',
                    admin: {
                        description: 'Texto que aparece en la parte inferior del pie de página',
                        placeholder: `© ${new Date().getFullYear()} Academia Peruana de Doctores.`
                    }
                },

            ],
        },
    ],
}