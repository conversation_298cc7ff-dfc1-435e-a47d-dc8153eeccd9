import { checkRole } from '@/backend/access/utils'
import { auditFields } from '@/backend/constants/fields/audit.field'
import { GROUPS } from '@/backend/constants/groups'
import type { CollectionConfig } from 'payload'

export const academicActivityMediaSizes = {
  thumbnail: {
    width: 400,
    height: 300,
    position: 'centre',
  },
  card: {
    width: 768,
    height: 432,
    position: 'centre',
  },
  hero: {
    width: 1200,
    height: 630,
    position: 'centre',
  },
} as const

export const AcademicActivityMedia: CollectionConfig = {
  slug: 'academic-activity-media',
  dbName: 'actMedia',
  labels: {
    singular: 'Imagen',
    plural: 'Imágenes',
  },
  admin: {
    group: GROUPS.EVENTS,
    description: 'Imágenes utilizadas en las actividades académicas',
    hidden: true,
  },
  access: {
    read: () => true,
    create: ({ req: { user } }) => checkRole(['admin', 'activities-coordinator', 'media-manager'], user),
    update: ({ req: { user } }) => checkRole(['admin', 'activities-coordinator', 'media-manager'], user),
    delete: ({ req: { user } }) => checkRole(['admin', 'activities-coordinator', 'media-manager'], user),
  },
  upload: {
    staticDir: 'media/academic-activities',
    imageSizes: [
      {
        name: 'thumbnail',
        ...academicActivityMediaSizes.thumbnail,
      },
      {
        name: 'card',
        ...academicActivityMediaSizes.card,
      },
      {
        name: 'hero',
        ...academicActivityMediaSizes.hero,
      },
    ],
    adminThumbnail: 'thumbnail',
    mimeTypes: ['image/*'],
  },
  fields: [
    {
      name: 'alt',
      type: 'text',
      label: 'Texto Alternativo',
      admin: {
        description: 'Descripción de la imagen para accesibilidad',
      },
    },
    ...auditFields()
  ],
}
