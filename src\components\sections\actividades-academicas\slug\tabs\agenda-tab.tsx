'use client'
import { <PERSON><PERSON>Up, StaggerFade } from "@/components/motion";
import { Text } from "@/components/ui/text";
import { Badge } from "@/components/ui/badge";
import { Avatar } from "@/components/ui/avatar";
import {
  ClockIcon,
  UserIcon,
  MicIcon,
  UsersIcon,
  CoffeeIcon,
  UtensilsIcon,
  ClipboardIcon,
  MoreHorizontalIcon,
  LogOut
} from "lucide-react";
import { Speaker, Media } from "@/payload-types";
import { speakerNameWithDegree } from "@/backend/collections/Events/Speakers/utils";

interface AgendaItem {
  time: string;
  title: string;
  description?: string | null;
  speaker?: Speaker | number | null;
  type?: 'conference' | 'workshop' | 'panel' | 'networking' | 'break' | 'lunch' | 'registration' | 'closing' | 'other' | null;
  customType?: string | null;
}

interface AgendaBlockData {
  title?: string | null;
  items?: AgendaItem[] | null;
}

interface AgendaTabProps {
  data: AgendaBlockData;
}

export default function AgendaTab({ data }: AgendaTabProps) {
  const getActivityTypeLabel = (type: string, customType?: string) => {
    if (type === 'other' && customType) {
      return customType;
    }

    const labels = {
      conference: 'Conferencia',
      workshop: 'Taller',
      panel: 'Panel',
      networking: 'Networking',
      break: 'Descanso',
      lunch: 'Almuerzo',
      registration: 'Registro',
      closing: 'Clausura',
      other: 'Otra'
    };
    return labels[type as keyof typeof labels] || type;
  };

  const getActivityTypeIcon = (type: string) => {
    const icons = {
      conference: MicIcon,
      workshop: UsersIcon,
      panel: UsersIcon,
      networking: UsersIcon,
      break: CoffeeIcon,
      lunch: UtensilsIcon,
      registration: ClipboardIcon,
      closing: LogOut,
      other: MoreHorizontalIcon
    };
    return icons[type as keyof typeof icons] || MicIcon;
  };

  const getActivityTypeColor = (type: string) => {
    const colors = {
      conference: 'bg-blue-100 text-blue-800 border-blue-200',
      workshop: 'bg-green-100 text-green-800 border-green-200',
      panel: 'bg-purple-100 text-purple-800 border-purple-200',
      networking: 'bg-orange-100 text-orange-800 border-orange-200',
      break: 'bg-gray-100 text-gray-800 border-gray-200',
      lunch: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      registration: 'bg-indigo-100 text-indigo-800 border-indigo-200',
      closing: 'bg-red-100 text-red-800 border-red-200',
      other: 'bg-primary text-white'
    };
    return colors[type as keyof typeof colors] || colors.other;
  };

  const getSpeakerImage = (photo: Speaker['photo']): string => {
    if (!photo) return '/placeholders/speaker.jpg';
    const media = photo as Media;
    return media.url || '/placeholders/speaker.jpg';
  };

  return (
    <section className="p-8 space-y-6">
      <FadeUp delay={0.1}>
        <div className="text-center mb-12">
          <Text variant="h3" className="text-primary mb-3 font-light">
            {data.title || "Agenda"}
          </Text>
          <div className="w-16 h-0.5 bg-gradient-to-r from-primary to-secondary mx-auto"></div>
        </div>
      </FadeUp>

      <StaggerFade className="space-y-4">
        {(data.items || []).map((item, index) => {
          const ActivityIcon = getActivityTypeIcon(item.type || 'conference');

          return (
            <div
              key={index}
              className="group relative bg-white overflow-hidden hover:shadow-lg hover:border-primary/20 transition-all duration-300"
            >
              {/* Línea lateral */}
              <div className={"absolute left-0 top-0 bottom-0 w-1 bg-primary"}></div>

              <div className="p-6 pl-8">
                <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
                  {/* Sección de hora - Diseño mejorado */}
                  <div className="lg:col-span-3">
                    <div className="bg-gradient-to-br from-primary/5 to-secondary/5 rounded-xl p-4 text-center border border-primary/10">
                      <div className="flex items-center justify-center gap-2 mb-2">
                        <ClockIcon className="w-4 h-4 text-primary" />
                        <Text className="text-xs font-medium text-primary uppercase tracking-wide">
                          Horario
                        </Text>
                      </div>
                      <Text className="text-lg font-bold text-gray-900">
                        {item.time}
                      </Text>
                    </div>
                  </div>

                  {/* Contenido principal */}
                  <div className="lg:col-span-9 space-y-4">
                    {/* Header con título, ícono y badge */}
                    <div className="flex items-start justify-between gap-4">
                      <div className="flex items-start gap-3 flex-1">
                        <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0 group-hover:bg-primary/20 transition-colors">
                          <ActivityIcon className="w-5 h-5 text-primary" />
                        </div>
                        <div className="flex-1">
                          <Text variant="h4" className="text-gray-900 font-semibold leading-tight mb-1">
                            {item.title}
                          </Text>
                          {item.description && (
                            <Text className="text-gray-600 text-sm leading-relaxed">
                              {item.description}
                            </Text>
                          )}
                        </div>
                      </div>

                      {item.type && (
                        <Badge
                          variant="outline"
                          className={`${getActivityTypeColor(item.type)} text-xs font-medium flex-shrink-0`}
                        >
                          {getActivityTypeLabel(item.type, item.customType || undefined)}
                        </Badge>
                      )}
                    </div>

                    {/* Ponente */}
                    {item.speaker && typeof item.speaker === 'object' && (
                      <div className="flex items-center gap-3 pt-3 border-t border-gray-50">
                        <Avatar className="w-10 h-10 ring-2 ring-primary/10">
                          <img
                            src={getSpeakerImage(item.speaker.photo)}
                            alt={item.speaker.fullName}
                            className="w-full h-full object-cover"
                          />
                        </Avatar>
                        <div className="flex items-center gap-2">
                          <UserIcon className="w-4 h-4 text-gray-400" />
                          <Text className="text-sm text-gray-700 font-medium">
                            {speakerNameWithDegree(item.speaker)}
                          </Text>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </StaggerFade>

      {/* Información adicional */}
      {(!data.items || data.items.length === 0) && (
        <FadeUp delay={0.2}>
          <div className="text-center py-8">
            <ClockIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <Text variant="h3" className="text-gray-500 mb-2">
              Agenda en preparación
            </Text>
            <Text className="text-gray-400">
              La agenda detallada se publicará próximamente.
            </Text>
          </div>
        </FadeUp>
      )}
    </section>
  );
}
