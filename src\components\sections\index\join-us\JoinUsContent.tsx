
"use client";
import { motion } from "motion/react";
import type { HomeJoinUs } from "@/payload-types";
import JoinUsItem from "./JoinUsItem";
import { containerVariants, imageVariants, itemVariants, lineVariants, listVariants } from "./animations.constants";
import { Text } from "@/components/ui/text";

export default function JoinUsContent({ data }: { data: HomeJoinUs }) {
    return (
        <motion.div
            className="grid lg:grid-cols-2 gap-8 lg:gap-12 xl:gap-16 items-center"
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
        >
            {/* Columna izquierda - Contenido */}
            <motion.div
                className="space-y-8"
                variants={itemVariants}
            >
                {/* Línea decorativa amarilla */}
                <motion.div
                    className="h-1 bg-secondary"
                    variants={lineVariants}
                />

                {/* Título */}
                <motion.div
                    variants={itemVariants}
                >
                    <Text variant='h2'
                        className="font-bold text-white leading-tight"
                    >{data.title}</Text>
                </motion.div>

                {/* Descripción */}
                <motion.div
                    variants={itemVariants}
                >
                    <Text variant='description'
                        className="text-gray-300 text-sm lg:text-base leading-relaxed max-w-lg"
                    >{data.description}</Text>
                </motion.div>

                {/* Lista de razones */}
                <motion.div
                    className="space-y-6 mt-8"
                    variants={listVariants}
                >
                    {data.items?.map((item, index) => (
                        <motion.div
                            key={index}
                            variants={itemVariants}
                            whileHover={{
                                x: 8,
                                transition: { duration: 0.2 }
                            }}
                        >
                            <JoinUsItem item={item} />
                        </motion.div>
                    ))}
                </motion.div>
            </motion.div>

            {/* Columna derecha - Imagen */}
            <motion.div
                variants={imageVariants}
                whileHover={{
                    scale: 1.02,
                    transition: { duration: 0.3 }
                }}
            >
                <div className="relative rounded-xl overflow-hidden shadow-2xl">
                    {typeof data.image === 'object' && data.image?.url && (
                        <motion.img
                            src={data.image.url}
                            alt={data.image.alt || data.title}
                            width={600}
                            height={400}
                            className="w-full h-auto object-cover"
                            loading='lazy'
                            initial={{ scale: 1.1 }}
                            animate={{ scale: 1 }}
                            transition={{ duration: 0.8, ease: "easeOut" }}
                        />
                    )}
                </div>
            </motion.div>
        </motion.div>
    )
}