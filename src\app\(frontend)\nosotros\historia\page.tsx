import { Timeline } from "@/components/ui/timeline"
import { AboutUsPageService } from "@/services/about-us.service"
import { RichText } from "@payloadcms/richtext-lexical/react"

export default async function HistoriaPage() {
    const banner = await AboutUsPageService.getHistoriaPageBanner()

    if (!banner) {
        return (
            <div className="flex flex-col items-center justify-center h-screen bg-gray-100">
                <h1 className="text-4xl font-bold mb-4">Historia</h1>
                <p className="text-lg text-gray-700">No se pudo cargar la información de la historia.</p>
            </div>
        )
    }

    const historailList = await AboutUsPageService.getHistoriaList();
    if (!historailList || historailList.length === 0) {
        return (
            <div className="flex flex-col items-center justify-center h-screen bg-gray-100">
                <h1 className="text-4xl font-bold mb-4">Historia</h1>
                <p className="text-lg text-gray-700">No se encontraron eventos históricos.</p>
            </div>
        );
    }

    const data = historailList.map((item) => {
        return {
            title: `${item.year}`,
            content: (
                <div>
                    <RichText data={item.description} />
                    {item.images && item.images.length > 0 && (
                        <div className="flex gap-4">
                            {
                                item.images.map((image, index) => {
                                    const imageURL =
                                        typeof image.image === "object" &&
                                            image.image !== null &&
                                            "url" in image.image
                                            ? (image.image as { url: string }).url
                                            : undefined;
                                    return (
                                        <div key={index} className="w-1/3">
                                            {imageURL && (
                                                <img
                                                    src={imageURL}
                                                    alt={`Historia ${item.year}`}
                                                    className="w-full h-auto rounded-lg"
                                                />
                                            )}
                                        </div>
                                    )
                                })
                            }

                        </div>
                    )}
                </div>
            )
        }
    })

    return <Timeline data={data} header={banner} />
}