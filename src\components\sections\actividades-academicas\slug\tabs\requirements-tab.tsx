'use client'
import { FadeUp, StaggerFade } from "@/components/motion";
import { Text } from "@/components/ui/text";
import { AcademicActivity } from "@/payload-types";
import { RichText } from "@payloadcms/richtext-lexical/react";
import { ClipboardListIcon } from "lucide-react";
import "@/styles/rich-text.css";

interface RequirementsTabProps {
  data: Extract<
    NonNullable<NonNullable<AcademicActivity["eventBlocks"]>[number]>,
    { blockType: "requirements" }
  >;
}

export default function RequirementsTab({ data }: RequirementsTabProps) {
  return (
    <section className="p-8 space-y-6">
      <FadeUp delay={0.1}>
        <Text variant="h3" className="text-primary mb-6">
          {data.title || "Información Adicional"}
        </Text>
      </FadeUp>

      <StaggerFade className="space-y-4">
        {
          data.content ? (
            <div className="rich-text-content">
              <RichText data={data.content} />
            </div>
          ) : (
            // Aún no hay información disponible
            <div className="text-center py-8">
              <ClipboardListIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <Text className="text-gray-400">
                Aún no hay información disponible
              </Text>
            </div>
          )
        }
      </StaggerFade>
    </section>
  );
}
