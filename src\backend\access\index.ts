import { Access } from 'payload'
import { checkRole } from './utils'

/** Allow access only for admins */
export const admins: Access = ({ req: { user } }) => checkRole(['admin'], user)

/**Allow to access all admins and the owner of the document */
export const adminsAndUser: Access = ({ req: { user } }) => {
    if (user) {
        if (checkRole(['admin'], user)) {
            return true
        }

        // Return a proper query constraint
        return {
            id: { equals: user.id },
        }
    }
    return false
}