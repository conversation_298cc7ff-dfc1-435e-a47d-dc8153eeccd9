'use client'
import { AcademicActivity, AcademicActivityCategory, AcademicActivityMedia } from '@/payload-types'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Text } from '@/components/ui/text'
import { StaggerFade } from '@/components/motion'
import { Calendar, Clock, ArrowRight } from 'lucide-react'
import Link from 'next/link'

import { format } from 'date-fns'
import { es } from 'date-fns/locale'

interface AcademicActivityRelatedProps {
  activities: AcademicActivity[]
}

export default function AcademicActivityRelated({ activities }: AcademicActivityRelatedProps) {
  if (activities.length === 0) {
    return null
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return format(date, "d MMM", { locale: es })
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return format(date, 'HH:mm', { locale: es })
  }

  const getImageUrl = (activity: AcademicActivity) => {
    const image = activity.mainImage as AcademicActivityMedia
    return image?.url || '/placeholder-activity.jpg'
  }

  const getCategoryInfo = (activity: AcademicActivity) => {
    const category = activity.category as AcademicActivityCategory
    return category
  }

  return (
    <section>
      <div className="text-center mb-8">
        <Text variant="h2" className="text-primary mb-4">
          Actividades Relacionadas
        </Text>
        <Text variant="description" className="text-muted-foreground max-w-2xl mx-auto">
          Descubre otras actividades académicas que podrían interesarte
        </Text>
      </div>

      <StaggerFade className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {activities.map((activity) => {
          const category = getCategoryInfo(activity)

          return (
            <Link key={activity.id} href={`/actividades-academicas/${activity.slug}`}>
              <Card className="group hover:shadow-lg transition-all duration-300 overflow-hidden h-full">
                {/* Imagen */}
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={getImageUrl(activity)}
                    alt={activity.title}
                    className="object-cover group-hover:scale-105 transition-transform duration-300 h-full w-full"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />

                  {/* Badge de categoría */}
                  {category && (
                    <div className="absolute top-3 right-3">
                      <Badge
                        variant="secondary"
                        className="text-white border-white/30"
                        style={{ backgroundColor: `${category.color}90` }}
                      >
                        {category.name}
                      </Badge>
                    </div>
                  )}

                  {/* Fecha en la esquina inferior izquierda */}
                  <div className="absolute bottom-3 left-3">
                    <div className="bg-white/90 backdrop-blur-sm rounded-lg px-2 py-1">
                      <div className="flex items-center gap-1 text-xs">
                        <Calendar className="w-3 h-3 text-primary" />
                        <span className="font-medium text-gray-900">
                          {formatDate(activity.startDate)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <CardContent className="p-4 flex-1 flex flex-col">
                  {/* Título */}
                  <Text variant="h4" className="text-base font-semibold mb-2 line-clamp-2 group-hover:text-primary transition-colors">
                    {activity.title}
                  </Text>

                  {/* Excerpt */}
                  {activity.excerpt && (
                    <Text variant="base" className="text-sm text-muted-foreground mb-4 line-clamp-2 flex-1">
                      {activity.excerpt}
                    </Text>
                  )}

                  {/* Footer con hora y enlace */}
                  <div className="flex items-center justify-between pt-2 border-t">
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Clock className="w-3 h-3" />
                      <span>
                        {formatTime(activity.startDate)}
                        {activity.endDate && ` - ${formatTime(activity.endDate)}`}
                      </span>
                    </div>

                    <div className="flex items-center gap-1 text-xs text-primary group-hover:gap-2 transition-all">
                      <span>Ver más</span>
                      <ArrowRight className="w-3 h-3" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          )
        })}
      </StaggerFade>
    </section>
  )
}
