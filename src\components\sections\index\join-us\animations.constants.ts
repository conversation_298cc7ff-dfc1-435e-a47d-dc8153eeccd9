// Variantes de animación para el contenedor principal
export const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            duration: 0.6,
            staggerChildren: 0.2,
            delayChildren: 0.1
        }
    }
} as const

// Variantes para elementos individuales
export const itemVariants = {
    hidden: {
        opacity: 0,
        y: 30,
        scale: 0.95
    },
    visible: {
        opacity: 1,
        y: 0,
        scale: 1,
        transition: {
            duration: 0.6,
            ease: [0.25, 0.46, 0.45, 0.94] // Easing suave
        }
    }
} as const

// Variantes para la línea decorativa
export const lineVariants = {
    hidden: { width: 0, opacity: 0 },
    visible: {
        width: 80,
        opacity: 1,
        transition: {
            duration: 0.8,
            ease: "easeOut"
        }
    }
} as const

// Variantes para la imagen
export const imageVariants = {
    hidden: {
        opacity: 0,
        scale: 0.8,
        rotateY: -15
    },
    visible: {
        opacity: 1,
        scale: 1,
        rotateY: 0,
        transition: {
            duration: 0.8,
            ease: [0.25, 0.46, 0.45, 0.94]
        }
    }
} as const

// Variantes para los items de la lista con stagger
export const listVariants = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.15,
            delayChildren: 0.3
        }
    }
} as const