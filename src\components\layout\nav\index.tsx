import Logo from '@/components/shared/logo'
import NavContainer from './NavContainer'
import Link from 'next/link'
import NavLink from '@/components/shared/nav-link'

export default function Nav() {
  return (
    <NavContainer>
      <div className="container mx-auto py-3">
        <div className='flex items-center justify-between'>
          <Link href="/">
            <Logo variant="dark" />
          </Link>


          {/* Desktop */}
          <div className='hidden md:flex gap-2'>
            {/* <NavLink active={path === href}>
              Actividades Académicas
            </NavLink> */}

            {/* Iterar ROUTES aquí */}
          </div>

          {/* Mobile */}
          <div className='block md:hidden'>
            {/* sheet de shacdnui que habre de la derecha, tiene como trigger un ha */}
          </div>
        </div>
      </div>
    </NavContainer>
  )
}
