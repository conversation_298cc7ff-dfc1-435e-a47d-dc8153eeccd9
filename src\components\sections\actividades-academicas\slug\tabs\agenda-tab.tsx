'use client'
import { <PERSON>adeUp, StaggerFade } from "@/components/motion";
import { Text } from "@/components/ui/text";
import { Badge } from "@/components/ui/badge";
import { Avatar } from "@/components/ui/avatar";
import {
  ClockIcon,
  UserIcon,
  MicIcon,
  UsersIcon,
  CoffeeIcon,
  UtensilsIcon,
  ClipboardIcon,
  AwardIcon,
  MoreHorizontalIcon
} from "lucide-react";
import { Speaker, Media } from "@/payload-types";
import { speakerNameWithDegree } from "@/backend/collections/Events/Speakers/utils";

interface AgendaItem {
  time: string;
  title: string;
  description?: string | null;
  speaker?: Speaker | number | null;
  type?: 'conference' | 'workshop' | 'panel' | 'networking' | 'break' | 'lunch' | 'registration' | 'closing' | 'other' | null;
  customType?: string | null;
}

interface AgendaBlockData {
  title?: string | null;
  items?: AgendaItem[] | null;
}

interface AgendaTabProps {
  data: AgendaBlockData;
}

export default function AgendaTab({ data }: AgendaTabProps) {
  const getActivityTypeIcon = (type: string) => {
    const icons = {
      conference: MicIcon,
      workshop: UsersIcon,
      panel: UsersIcon,
      networking: UsersIcon,
      break: CoffeeIcon,
      lunch: UtensilsIcon,
      registration: ClipboardIcon,
      closing: AwardIcon,
      other: MoreHorizontalIcon
    };
    return icons[type as keyof typeof icons] || MicIcon;
  };

  const getActivityTypeLabel = (type: string, customType?: string) => {
    if (type === 'other' && customType) {
      return customType;
    }

    const labels = {
      conference: 'Conferencia',
      workshop: 'Taller',
      panel: 'Panel',
      networking: 'Networking',
      break: 'Descanso',
      lunch: 'Almuerzo',
      registration: 'Registro',
      closing: 'Clausura',
      other: 'Otra'
    };
    return labels[type as keyof typeof labels] || type;
  };

  const getActivityTypeColor = (type: string) => {
    const colors = {
      conference: 'bg-blue-100 text-blue-800 border-blue-200',
      workshop: 'bg-green-100 text-green-800 border-green-200',
      panel: 'bg-purple-100 text-purple-800 border-purple-200',
      networking: 'bg-orange-100 text-orange-800 border-orange-200',
      break: 'bg-gray-100 text-gray-800 border-gray-200',
      lunch: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      registration: 'bg-indigo-100 text-indigo-800 border-indigo-200',
      closing: 'bg-red-100 text-red-800 border-red-200',
      other: 'bg-gray-100 text-gray-800 border-gray-200'
    };
    return colors[type as keyof typeof colors] || colors.other;
  };

  const getSpeakerImage = (photo: Speaker['photo']): string => {
    if (!photo) return '/placeholders/speaker.jpg';
    const media = photo as Media;
    return media.url || '/placeholders/speaker.jpg';
  };

  return (
    <section className="p-8 space-y-6">
      <FadeUp delay={0.1}>
        <Text variant="h3" className="text-primary mb-6">
          {data.title || "Agenda de la actividad"}
        </Text>
      </FadeUp>

      <StaggerFade className="space-y-4">
        {(data.items || []).map((item, index) => {
          const ActivityIcon = getActivityTypeIcon(item.type || 'conference');

          return (
            <div
              key={index}
              className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow duration-200"
            >
              <div className="flex flex-col md:flex-row gap-4">
                {/* Hora */}
                <div className="flex items-center gap-2 md:w-32 flex-shrink-0">
                  <ClockIcon className="w-4 h-4 text-gray-500" />
                  <Text className="font-medium text-gray-900">
                    {item.time}
                  </Text>
                </div>

                {/* Contenido principal */}
                <div className="flex-1 space-y-3">
                  {/* Título y tipo */}
                  <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2">
                    <div className="flex items-start gap-3">
                      <ActivityIcon className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                      <div>
                        <Text variant="h4" className="text-gray-900 leading-tight">
                          {item.title}
                        </Text>
                      </div>
                    </div>

                    {item.type && (
                      <Badge
                        variant="outline"
                        className={`${getActivityTypeColor(item.type)} text-xs`}
                      >
                        {getActivityTypeLabel(item.type, item.customType || undefined)}
                      </Badge>
                    )}
                  </div>

                  {/* Descripción */}
                  {item.description && (
                    <Text className="text-gray-600 text-sm leading-relaxed ml-8">
                      {item.description}
                    </Text>
                  )}

                  {/* Ponente */}
                  {item.speaker && typeof item.speaker === 'object' && (
                    <div className="flex items-center gap-3 ml-8 pt-2">
                      <Avatar className="w-8 h-8">
                        <img
                          src={getSpeakerImage(item.speaker.photo)}
                          alt={item.speaker.fullName}
                          className="w-full h-full object-cover"
                        />
                      </Avatar>
                      <div className="flex items-center gap-2">
                        <UserIcon className="w-4 h-4 text-gray-400" />
                        <Text className="text-sm text-gray-700 font-medium">
                          {speakerNameWithDegree(item.speaker)}
                        </Text>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </StaggerFade>

      {/* Información adicional */}
      {(!data.items || data.items.length === 0) && (
        <FadeUp delay={0.2}>
          <div className="text-center py-8">
            <ClockIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <Text variant="h3" className="text-gray-500 mb-2">
              Agenda en preparación
            </Text>
            <Text className="text-gray-400">
              La agenda detallada se publicará próximamente.
            </Text>
          </div>
        </FadeUp>
      )}
    </section>
  );
}
