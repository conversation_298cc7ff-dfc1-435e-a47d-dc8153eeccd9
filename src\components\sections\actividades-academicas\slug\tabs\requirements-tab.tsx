'use client'
import { FadeUp, StaggerFade } from "@/components/motion";
import { Text } from "@/components/ui/text";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircleIcon,
  AlertCircleIcon,
  ClipboardListIcon
} from "lucide-react";
import DynamicIcon from "@/backend/components/lucide/dynamic-icon";

interface RequirementItem {
  icon?: string | null;
  title: string;
  description?: string | null;
  required?: boolean | null;
}

interface RequirementsBlockData {
  title?: string | null;
  items?: RequirementItem[] | null;
}

interface RequirementsTabProps {
  data: RequirementsBlockData;
}

export default function RequirementsTab({ data }: RequirementsTabProps) {
  return (
    <section className="p-8 space-y-6">
      <FadeUp delay={0.1}>
        <Text variant="h3" className="text-primary mb-6">
          {data.title || "Requisitos"}
        </Text>
      </FadeUp>

      <StaggerFade className="space-y-4">
        {(data.items || []).map((item, index) => (
          <div
            key={index}
            className={`border rounded-lg p-4 transition-all duration-200 ${item.required
              ? 'bg-red-50 border-red-200 hover:bg-red-100'
              : 'bg-blue-50 border-blue-200 hover:bg-blue-100'
              }`}
          >
            <div className="flex items-start gap-4">
              {/* Ícono del requisito */}
              <div className="flex-shrink-0 mt-1">
                {item.icon ? (
                  <DynamicIcon
                    name={item.icon as any}
                    className={`w-5 h-5 ${item.required ? 'text-red-600' : 'text-blue-600'
                      }`}
                  />
                ) : (
                  <ClipboardListIcon
                    className={`w-5 h-5 ${item.required ? 'text-red-600' : 'text-blue-600'
                      }`}
                  />
                )}
              </div>

              {/* Contenido */}
              <div className="flex-1">
                <div className="flex items-start justify-between gap-3 mb-2">
                  <Text variant="h4" className={`${item.required ? 'text-red-900' : 'text-blue-900'
                    }`}>
                    {item.title}
                  </Text>

                  <div className="flex items-center gap-2">
                    {item.required ? (
                      <>
                        <AlertCircleIcon className="w-4 h-4 text-red-600" />
                        <Badge variant="destructive" className="text-xs">
                          Obligatorio
                        </Badge>
                      </>
                    ) : (
                      <>
                        <CheckCircleIcon className="w-4 h-4 text-blue-600" />
                        <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-800">
                          Recomendado
                        </Badge>
                      </>
                    )}
                  </div>
                </div>

                {item.description && (
                  <Text className={`text-sm leading-relaxed ${item.required ? 'text-red-800' : 'text-blue-800'
                    }`}>
                    {item.description}
                  </Text>
                )}
              </div>
            </div>
          </div>
        ))}
      </StaggerFade>

      {/* Información adicional */}
      {(!data.items || data.items.length === 0) && (
        <FadeUp delay={0.2}>
          <div className="text-center py-8">
            <ClipboardListIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <Text variant="h3" className="text-gray-500 mb-2">
              Sin requisitos específicos
            </Text>
            <Text className="text-gray-400">
              No hay requisitos especiales para participar en esta actividad.
            </Text>
          </div>
        </FadeUp>
      )}

      {/* Resumen de requisitos */}
      {(data.items && data.items.length > 0) && (
        <FadeUp delay={0.3}>
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mt-6">
            <div className="flex items-center gap-2 mb-3">
              <ClipboardListIcon className="w-5 h-5 text-gray-600" />
              <Text variant="h4" className="text-gray-900">
                Resumen
              </Text>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <AlertCircleIcon className="w-4 h-4 text-red-600" />
                <Text className="text-gray-700">
                  <span className="font-medium text-red-700">
                    {data.items.filter(item => item.required).length}
                  </span> requisitos obligatorios
                </Text>
              </div>

              <div className="flex items-center gap-2">
                <CheckCircleIcon className="w-4 h-4 text-blue-600" />
                <Text className="text-gray-700">
                  <span className="font-medium text-blue-700">
                    {data.items.filter(item => !item.required).length}
                  </span> recomendaciones
                </Text>
              </div>
            </div>
          </div>
        </FadeUp>
      )}
    </section>
  );
}
