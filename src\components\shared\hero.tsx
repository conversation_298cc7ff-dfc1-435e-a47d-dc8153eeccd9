import { FadeUp } from "@/components/motion";
import { Text } from "@/components/ui/text";

interface HeroSectionProps {
    title: string;
    description: string;
    backgroundImageUrl?: string;
}

export default function HeroSection(
    { title, description, backgroundImageUrl }: HeroSectionProps
) {
    return (
        <section className="bg-primary h-[250px] md:h-[380px] flex items-end"
            style={{
                backgroundImage: backgroundImageUrl
                    ? `linear-gradient(to top, rgba(0, 0, 18, 0.8), rgba(0, 0, 18, 0.2)), url(${encodeURI(backgroundImageUrl)})`
                    : undefined,
                backgroundSize: "cover",
                backgroundPosition: "bottom",
                backgroundRepeat: "no-repeat",
            }}
        >
            <article className="container mx-auto py-6 px-4 lg:px-16 space-y-4 text-center md:text-start">
                <FadeUp duration={0.8} delay={0.2} distance={40}>
                    <Text variant="h1" className="text-white text-2xl md:text-5xl">
                        {title}
                    </Text>
                </FadeUp>

                <FadeUp duration={0.8} delay={0.2} distance={40}>
                    <Text variant="description" className="text-base md:text-lg max-w-lg text-white">
                        {description}
                    </Text>
                </FadeUp>
            </article>
        </section>
    );
}