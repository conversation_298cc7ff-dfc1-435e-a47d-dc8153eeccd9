# Biblioteca de Componentes Motion

Esta carpeta contiene componentes reutilizables de animación usando Motion de motion/react.
Todos los componentes están diseñados para ser altamente flexibles y reutilizables con props super configurables.

## 🎯 Componentes Disponibles

### 1. **Fade** - Animaciones de desvanecimiento
- `Fade` - Componente base con dirección configurable
- `FadeUp`, `FadeDown`, `FadeLeft`, `FadeRight`, `FadeIn` - Variantes predefinidas
- `FadeUpSlow`, `FadeUpFast`, `FadeUpBounce` - Variantes con configuraciones específicas

### 2. **Stagger** - Animaciones escalonadas
- `Stagger` - Componente base para animar elementos hijos secuencialmente
- `StaggerFade`, `StaggerSlideUp`, `StaggerScale`, `StaggerRotate` - Variantes por tipo
- `StaggerFast`, `StaggerSlow`, `StaggerBounce` - Variantes por velocidad

### 3. **Scale** - Animaciones de escala
- `Scale` - Componente base con tipos configurables
- `ScaleIn`, `ScaleOut`, `ScaleBounce`, `ScalePulse`, `ScaleGrow`, `ScaleShrink` - Variantes
- `ScaleInSlow`, `ScaleInFast`, `ScaleInBig` - Configuraciones específicas

### 4. **Slide** - Animaciones de deslizamiento
- `Slide` - Componente base con direcciones configurables
- `SlideUp`, `SlideDown`, `SlideLeft`, `SlideRight` - Direcciones básicas
- `SlideUpLeft`, `SlideUpRight`, `SlideDownLeft`, `SlideDownRight` - Direcciones diagonales
- `SlideInFromBottom`, `SlideInFromTop`, `SlideInFromLeft`, `SlideInFromRight` - Desde viewport

### 5. **Rotate** - Animaciones de rotación
- `Rotate` - Componente base con direcciones configurables
- `RotateIn`, `RotateInCounterclockwise`, `FlipX`, `FlipY`, `FlipDiagonal` - Variantes
- `RotateContinuous`, `RotatePulse` - Animaciones continuas

### 6. **Reveal** - Animaciones de revelado
- `Reveal` - Componente base con tipos y direcciones configurables
- `RevealClip`, `RevealMask`, `RevealCurtain`, `RevealTypewriter`, `RevealWave` - Por tipo
- `RevealFromLeft`, `RevealFromRight`, `RevealFromTop`, `RevealFromBottom` - Por dirección

## 🚀 Uso Básico

```tsx
import { FadeUp, StaggerFade, ScaleIn, SlideLeft } from '@/components/motion'

// Animación simple
<FadeUp>
  <div>Este contenido aparece con fade up</div>
</FadeUp>

// Animación con props personalizadas
<FadeUp duration={1.2} delay={0.3} distance={50}>
  <div>Fade up personalizado</div>
</FadeUp>

// Animación stagger para listas
<StaggerFade staggerDelay={0.1}>
  {items.map(item => (
    <div key={item.id}>{item.content}</div>
  ))}
</StaggerFade>
```

## ⚙️ Props Comunes

Todos los componentes comparten estas props básicas:

- `duration?: number` - Duración en segundos (default: 0.6)
- `delay?: number` - Retraso antes de iniciar (default: 0)
- `ease?: string | number[]` - Función de easing (default: 'easeOut')
- `once?: boolean` - Si debe ejecutarse solo una vez (default: true)
- `threshold?: number` - Umbral de visibilidad (default: 0.1)
- `className?: string` - Clase CSS adicional

## 🎨 Props Específicas por Componente

### Fade
```tsx
interface FadeProps {
  direction?: 'up' | 'down' | 'left' | 'right' | 'none'
  distance?: number // Distancia del movimiento
  initialOpacity?: number
  finalOpacity?: number
}
```

### Stagger
```tsx
interface StaggerProps {
  staggerDelay?: number // Retraso entre elementos
  childAnimation?: 'fade' | 'slide' | 'scale' | 'rotate' | 'custom'
  direction?: 'normal' | 'reverse'
  slideDirection?: 'up' | 'down' | 'left' | 'right'
}
```

### Scale
```tsx
interface ScaleProps {
  type?: 'in' | 'out' | 'bounce' | 'pulse' | 'grow' | 'shrink'
  initialScale?: number
  finalScale?: number
  transformOrigin?: string
  repeat?: number | boolean
}
```

## 🎯 Ejemplos Avanzados

### Combinando Animaciones
```tsx
<Stagger staggerDelay={0.2} childAnimation="slide">
  {cards.map((card, index) => (
    <Scale
      key={index}
      type="in"
      delay={index * 0.1}
      whileHover={{ scale: 1.05 }}
    >
      <div className="card">
        <FadeUp delay={0.3}>
          <h3>{card.title}</h3>
        </FadeUp>
        <FadeUp delay={0.5}>
          <p>{card.content}</p>
        </FadeUp>
      </div>
    </Scale>
  ))}
</Stagger>
```

### Usando Constantes Predefinidas
```tsx
import { ANIMATION_DURATIONS, ANIMATION_DELAYS, ANIMATION_DISTANCES } from '@/components/motion'

<FadeUp
  duration={ANIMATION_DURATIONS.slow}
  delay={ANIMATION_DELAYS.medium}
  distance={ANIMATION_DISTANCES.large}
>
  <div>Contenido con configuración predefinida</div>
</FadeUp>
```

## 📱 Responsive y Performance

- Todos los componentes usan `whileInView` para optimizar performance
- Las animaciones se activan solo cuando el elemento es visible
- Soporte completo para diferentes tamaños de pantalla
- Configuración de `threshold` y `margin` para control fino

## 🎭 Casos de Uso Comunes

### Para Páginas de Actividades Académicas
```tsx
// Hero section
<FadeUp duration={1}>
  <h1>Actividades Académicas</h1>
</FadeUp>

// Grid de actividades
<StaggerSlideUp staggerDelay={0.15}>
  {activities.map(activity => (
    <ScaleIn whileHover={{ scale: 1.02 }}>
      <ActivityCard activity={activity} />
    </ScaleIn>
  ))}
</StaggerSlideUp>

// Filtros
<SlideLeft>
  <FilterPanel />
</SlideLeft>
```

### Para Formularios
```tsx
<Stagger staggerDelay={0.1} childAnimation="fade">
  <FadeUp><Input label="Título" /></FadeUp>
  <FadeUp><DateRangePicker label="Fechas" /></FadeUp>
  <FadeUp><Textarea label="Descripción" /></FadeUp>
  <FadeUp><Button>Guardar</Button></FadeUp>
</Stagger>
```

## 🔧 Personalización

Todos los componentes aceptan props de motion adicionales:
```tsx
<FadeUp
  whileHover={{ scale: 1.05 }}
  whileTap={{ scale: 0.95 }}
  onAnimationComplete={() => console.log('Animación completada')}
>
  <div>Contenido interactivo</div>
</FadeUp>
```
