// storage-adapter-import-placeholder
import { postgresAdapter } from '@payloadcms/db-postgres'
import { payloadCloudPlugin } from '@payloadcms/payload-cloud'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import path from 'path'
import { buildConfig } from 'payload'
import { fileURLToPath } from 'url'
import sharp from 'sharp'
import { Users } from './backend/collections/Users'
import { es } from '@payloadcms/translations/languages/es'
import { COLLECTIONS } from './backend/collections'
import { GLOBALS } from './backend/globals'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

const SERVER_URL = process.env.NEXT_PUBLIC_SERVER_URL || ''

export default buildConfig({
  serverURL: SERVER_URL,
  csrf: [
    SERVER_URL,
  ],
  cors: {
    origins: [SERVER_URL],
  },
  admin: {
    user: Users.slug,
    importMap: {
      baseDir: path.resolve(dirname),
    },
    meta: {
      titleSuffix: '| APD CMS',
      icons: {
        icon: '/favicon-light.webp',
      },
    },
    components: {
      graphics: {
        Logo: {
          path: '/backend/components/shared/logo.tsx#Logo',
          exportName: 'Logo',
        },
        Icon: {
          path: '/backend/components/shared/icon',
          exportName: 'Icon',
        },
      },
    },
    theme: 'light',
  },
  collections: COLLECTIONS,
  globals: GLOBALS,
  editor: lexicalEditor({}),
  secret: process.env.PAYLOAD_SECRET || '',
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  db: postgresAdapter({
    pool: {
      connectionString: process.env.DATABASE_URI || '',
    },
  }),
  sharp,
  plugins: [
    payloadCloudPlugin(),
    // storage-adapter-placeholder
  ],
  i18n: {
    supportedLanguages: { es },
    fallbackLanguage: 'es',
  },
})
