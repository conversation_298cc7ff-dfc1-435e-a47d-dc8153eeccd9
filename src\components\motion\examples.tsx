/**
 * Ejemplos de uso de los componentes de animación Motion
 *
 * Este archivo muestra diferentes formas de usar los componentes de animación
 * para crear experiencias visuales elegantes y fluidas.
 */

'use client'

import React from 'react'
import {
  Fade, FadeUp, FadeDown, FadeLeft, FadeRight, FadeIn,
  Stagger, StaggerFade, StaggerSlideUp, StaggerScale,
  Scale, ScaleIn, ScaleBounce, ScalePulse,
  Slide, SlideUp, SlideLeft, SlideInFromBottom,
  Rotate, RotateIn, FlipX, RotateContinuous,
  RevealClip, RevealFromLeft, RevealTypewriter,
  ANIMATION_DURATIONS, ANIMATION_DISTANCES
} from './index'

// Ejemplo 1: Animaciones básicas de Fade
export function FadeExamples() {
  return (
    <div className="space-y-8 p-6">
      <h2 className="text-2xl font-bold">Animaciones Fade</h2>

      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        <FadeUp>
          <div className="bg-blue-500 text-white p-4 rounded-lg text-center">
            Fade Up
          </div>
        </FadeUp>

        <FadeDown>
          <div className="bg-green-500 text-white p-4 rounded-lg text-center">
            Fade Down
          </div>
        </FadeDown>

        <FadeLeft>
          <div className="bg-purple-500 text-white p-4 rounded-lg text-center">
            Fade Left
          </div>
        </FadeLeft>

        <FadeRight>
          <div className="bg-red-500 text-white p-4 rounded-lg text-center">
            Fade Right
          </div>
        </FadeRight>

        <FadeIn>
          <div className="bg-yellow-500 text-white p-4 rounded-lg text-center">
            Fade In
          </div>
        </FadeIn>

        <Fade direction="up" duration={ANIMATION_DURATIONS.slow} ease="bounce">
          <div className="bg-indigo-500 text-white p-4 rounded-lg text-center">
            Custom Fade
          </div>
        </Fade>
      </div>
    </div>
  )
}

// Ejemplo 2: Animaciones Stagger
export function StaggerExamples() {
  const items = ['Item 1', 'Item 2', 'Item 3', 'Item 4', 'Item 5']

  return (
    <div className="space-y-8 p-6">
      <h2 className="text-2xl font-bold">Animaciones Stagger</h2>

      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-4">Stagger Fade</h3>
          <StaggerFade staggerDelay={0.1}>
            {items.map((item, index) => (
              <div key={index} className="bg-blue-100 p-3 mb-2 rounded">
                {item}
              </div>
            ))}
          </StaggerFade>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Stagger Slide Up</h3>
          <StaggerSlideUp staggerDelay={0.15}>
            {items.map((item, index) => (
              <div key={index} className="bg-green-100 p-3 mb-2 rounded">
                {item}
              </div>
            ))}
          </StaggerSlideUp>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Stagger Scale</h3>
          <StaggerScale staggerDelay={0.2} className="flex gap-4">
            {items.map((item, index) => (
              <div key={index} className="bg-purple-100 p-3 rounded flex-1 text-center">
                {item}
              </div>
            ))}
          </StaggerScale>
        </div>
      </div>
    </div>
  )
}

// Ejemplo 3: Animaciones Scale
export function ScaleExamples() {
  return (
    <div className="space-y-8 p-6">
      <h2 className="text-2xl font-bold">Animaciones Scale</h2>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <ScaleIn>
          <div className="bg-orange-500 text-white p-4 rounded-lg text-center">
            Scale In
          </div>
        </ScaleIn>

        <ScaleBounce>
          <div className="bg-pink-500 text-white p-4 rounded-lg text-center">
            Scale Bounce
          </div>
        </ScaleBounce>

        <ScalePulse>
          <div className="bg-teal-500 text-white p-4 rounded-lg text-center">
            Scale Pulse
          </div>
        </ScalePulse>

        <Scale type="grow" transformOrigin="bottom">
          <div className="bg-cyan-500 text-white p-4 rounded-lg text-center">
            Grow from Bottom
          </div>
        </Scale>
      </div>
    </div>
  )
}

// Ejemplo 4: Animaciones Slide
export function SlideExamples() {
  return (
    <div className="space-y-8 p-6">
      <h2 className="text-2xl font-bold">Animaciones Slide</h2>

      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        <SlideUp>
          <div className="bg-emerald-500 text-white p-4 rounded-lg text-center">
            Slide Up
          </div>
        </SlideUp>

        <SlideLeft>
          <div className="bg-violet-500 text-white p-4 rounded-lg text-center">
            Slide Left
          </div>
        </SlideLeft>

        <SlideInFromBottom>
          <div className="bg-rose-500 text-white p-4 rounded-lg text-center">
            From Viewport
          </div>
        </SlideInFromBottom>

        <Slide direction="up" distance={ANIMATION_DISTANCES.large} ease="bounce">
          <div className="bg-amber-500 text-white p-4 rounded-lg text-center">
            Custom Slide
          </div>
        </Slide>
      </div>
    </div>
  )
}

// Ejemplo 5: Animaciones Rotate
export function RotateExamples() {
  return (
    <div className="space-y-8 p-6">
      <h2 className="text-2xl font-bold">Animaciones Rotate</h2>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <RotateIn>
          <div className="bg-lime-500 text-white p-4 rounded-lg text-center">
            Rotate In
          </div>
        </RotateIn>

        <FlipX>
          <div className="bg-sky-500 text-white p-4 rounded-lg text-center">
            Flip X
          </div>
        </FlipX>

        <RotateContinuous duration={2}>
          <div className="bg-fuchsia-500 text-white p-4 rounded-lg text-center">
            Continuous
          </div>
        </RotateContinuous>

        <Rotate direction="clockwise" initialRotation={-360} duration={1.5}>
          <div className="bg-slate-500 text-white p-4 rounded-lg text-center">
            Full Rotation
          </div>
        </Rotate>
      </div>
    </div>
  )
}

// Ejemplo 6: Animaciones Reveal
export function RevealExamples() {
  return (
    <div className="space-y-8 p-6">
      <h2 className="text-2xl font-bold">Animaciones Reveal</h2>

      <div className="space-y-6">
        <RevealFromLeft>
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6 rounded-lg">
            <h3 className="text-xl font-bold">Reveal from Left</h3>
            <p>Este contenido se revela desde la izquierda usando clip-path</p>
          </div>
        </RevealFromLeft>

        <RevealClip direction="center">
          <div className="bg-gradient-to-r from-green-500 to-blue-500 text-white p-6 rounded-lg">
            <h3 className="text-xl font-bold">Reveal from Center</h3>
            <p>Este contenido se expande desde el centro</p>
          </div>
        </RevealClip>

        <RevealTypewriter>
          <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white p-6 rounded-lg">
            <h3 className="text-xl font-bold">Typewriter Effect</h3>
            <p>Este texto aparece como si fuera escrito en una máquina de escribir</p>
          </div>
        </RevealTypewriter>
      </div>
    </div>
  )
}

// Ejemplo 7: Combinaciones complejas
export function ComplexAnimationExamples() {
  const cardData = [
    { title: 'Tarjeta 1', content: 'Contenido de la primera tarjeta', color: 'bg-blue-500' },
    { title: 'Tarjeta 2', content: 'Contenido de la segunda tarjeta', color: 'bg-green-500' },
    { title: 'Tarjeta 3', content: 'Contenido de la tercera tarjeta', color: 'bg-purple-500' },
    { title: 'Tarjeta 4', content: 'Contenido de la cuarta tarjeta', color: 'bg-red-500' },
  ]

  return (
    <div className="space-y-8 p-6">
      <h2 className="text-2xl font-bold">Animaciones Complejas</h2>

      <div className="space-y-8">
        {/* Título con reveal */}
        <RevealFromLeft>
          <h3 className="text-3xl font-bold text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Galería de Tarjetas Animadas
          </h3>
        </RevealFromLeft>

        {/* Grid de tarjetas con stagger */}
        <Stagger
          staggerDelay={0.2}
          childAnimation="slide"
          slideDirection="up"
          className="grid grid-cols-1 md:grid-cols-2 gap-6"
        >
          {cardData.map((card, index) => (
            <Scale
              key={index}
              type="in"
              delay={index * 0.1}
              whileHover={{ scale: 1.05 }}
              className="cursor-pointer"
            >
              <div className={`${card.color} text-white p-6 rounded-xl shadow-lg`}>
                <Fade direction="down" delay={0.3 + index * 0.1}>
                  <h4 className="text-xl font-bold mb-2">{card.title}</h4>
                </Fade>
                <Fade direction="up" delay={0.5 + index * 0.1}>
                  <p>{card.content}</p>
                </Fade>
              </div>
            </Scale>
          ))}
        </Stagger>
      </div>
    </div>
  )
}

// Demostración completa
export function MotionComponentsDemo() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto py-12">
        <FadeUp>
          <h1 className="text-4xl font-bold text-center mb-12 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Biblioteca de Componentes Motion
          </h1>
        </FadeUp>

        <div className="space-y-16">
          <FadeExamples />
          <StaggerExamples />
          <ScaleExamples />
          <SlideExamples />
          <RotateExamples />
          <RevealExamples />
          <ComplexAnimationExamples />
        </div>
      </div>
    </div>
  )
}
