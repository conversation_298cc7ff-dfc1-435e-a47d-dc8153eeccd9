import { Metadata } from 'next'
import FeaturedActivitySection from "@/components/sections/actividades-academicas/featured-activity";
import AcademicActivitiesFilters from "@/components/sections/actividades-academicas/filters";
import AcademicAtivitiesGrid from "@/components/sections/actividades-academicas/grid";
import { AcademicActivitiesService, GetAcademicActivitiesParams } from '@/services/academic-activities/academic-activities.service'
import { AcademicActivityCategoriesService } from '@/services/academic-activities/academic-activity-categories.service'
import { AcademicActivityTagsService } from '@/services/academic-activities/academic-activity-tags.service'
import { AcademicActivityCategory } from '@/payload-types'
import HeroSection from '@/components/shared/hero';
import { ACTIVITIES_GRID_PAGE_SIZE } from '@/components/sections/actividades-academicas/constants';
import { LIST_ACADEMIC_ACTIVITIES_SELECT } from '@/services/academic-activities/constants';

export const metadata: Metadata = {
    title: 'Actividades Académicas',
    description: 'Descubre nuestras conferencias, seminarios, talleres y eventos académicos. Mantente actualizado con las últimas actividades de la comunidad académica.',
    keywords: ['actividades académicas', 'conferencias', 'seminarios', 'talleres', 'eventos académicos', 'APD'],
    openGraph: {
        title: 'Actividades Académicas | Academia Peruana de Doctores',
        description: 'Descubre nuestras conferencias, seminarios, talleres y eventos académicos.',
        type: 'website',
    },
}

interface SearchParams {
    page?: string
    search?: string
    category?: string
    startDate?: string
    endDate?: string
    modality?: string
    upcoming?: string
}

interface AcademicActivitiesPageProps {
    searchParams: Promise<SearchParams>
}

export default async function AcademicActivitiesPage({ searchParams }: AcademicActivitiesPageProps) {
    const params = await searchParams

    // Obtener actividad destacada
    const featuredActivities = await AcademicActivitiesService.getUpcomingAcademicActivities({
        limit: 1,
    })
    const featuredActivity = featuredActivities[0] || null

    // Construir parámetros de consulta para el grid
    const page = parseInt(params.page || '1')
    const search = params.search || ''
    const category = params.category || ''
    const startDate = params.startDate
    const endDate = params.endDate
    const upcoming = params.upcoming === 'true'

    const queryParams: GetAcademicActivitiesParams = {
        page,
        limit: ACTIVITIES_GRID_PAGE_SIZE,
        status: 'active',
        sort: 'start-date-desc',
        startDateFrom: upcoming ? new Date() : undefined,
        findArgs: {
            select: LIST_ACADEMIC_ACTIVITIES_SELECT
        }
    }

    if (search) queryParams.search = search
    if (category) queryParams.category = category
    if (startDate) queryParams.startDateFrom = new Date(startDate)
    if (endDate) queryParams.startDateTo = new Date(endDate)

    // Obtener todos los datos necesarios en paralelo
    const [activitiesResponse, categoriesWithCount, upcomingActivities, popularTags, activeCategories] = await Promise.all([
        AcademicActivitiesService.getAcademicActivities(queryParams),
        AcademicActivityCategoriesService.getCategoriesWithCount(),
        AcademicActivitiesService.getUpcomingAcademicActivities({
            limit: 4,
            findArgs: {
                select: {
                    "slug": true,
                    "title": true,
                    "startDate": true,
                }
            }
        }),
        AcademicActivityTagsService.getPopularTags(8),
        AcademicActivityCategoriesService.getActiveCategories(),
    ])

    const initialGridData = {
        activities: activitiesResponse.docs,
        totalPages: activitiesResponse.totalPages,
        currentPage: activitiesResponse.page || 1,
        totalItems: activitiesResponse.totalDocs || 0,
    }

    const asideData = {
        categories: categoriesWithCount,
        upcomingActivities,
        popularTags,
    }

    const filtersData = {
        categories: activeCategories as AcademicActivityCategory[],
    }

    return (
        <main>
            {/* <AcademicAtivityHeroSection /> */}
            <HeroSection
                title='Nuestras Actividades'
                description='Descubre nuestras conferencias, seminarios, talleres y eventos académicos. Mantente actualizado con las últimas actividades de la comunidad académica.'
                backgroundImageUrl='/img/hero/nuestras-actividades.webp'
            />
            <FeaturedActivitySection initialActivity={featuredActivity} />
            <AcademicActivitiesFilters initialData={filtersData} />
            <AcademicAtivitiesGrid initialData={initialGridData} asideData={asideData} />
        </main>
    )
}