'use client'
import { AcademicActivity } from '@/payload-types'
import { FadeUp, StaggerFade } from '@/components/motion'
import AcademicActivityHero from './hero'
import AcademicActivityContent from './content'
import AcademicActivitySidebar from './sidebar'
import AcademicActivityBlocks from './blocks/index'
import AcademicActivityRelated from './related'

interface AcademicActivityDetailPageProps {
  activity: AcademicActivity
  relatedActivities: AcademicActivity[]
}

export default function AcademicActivityDetailPage({
  activity,
  relatedActivities
}: AcademicActivityDetailPageProps) {
  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <AcademicActivityHero activity={activity} />

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Columna Izquierda - Contenido Principal */}
          <div className="lg:col-span-2 space-y-8">
            {/* Excerpt/Resumen */}
            {activity.excerpt && (
              <FadeUp>
                <div className="italic text-lg text-muted-foreground leading-relaxed border-y py-4 md:py-8">
                  {activity.excerpt}
                </div>
              </FadeUp>
            )}

            <StaggerFade>
              {/* Información Principal */}
              <AcademicActivityContent activity={activity} />

              {/* Bloques de Contenido Flexible */}
              {/* <AcademicActivityBlocks activity={activity} /> */}
            </StaggerFade>
          </div>

          {/* Columna Derecha - Sidebar */}
          <div className="space-y-6">
            <FadeUp delay={0.3}>
              <AcademicActivitySidebar activity={activity} />
            </FadeUp>
          </div>
        </div>

        {/* Actividades Relacionadas */}
        {relatedActivities.length > 0 && (
          <FadeUp delay={0.5} className="mt-16">
            <AcademicActivityRelated activities={relatedActivities} />
          </FadeUp>
        )}
      </div>
    </div>
  )
}
