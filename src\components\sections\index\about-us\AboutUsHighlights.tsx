'use client'

import { HomeAboutUs } from '@/payload-types'
import { Text } from '@/components/ui/text'
import { motion } from 'motion/react'

interface AboutUsHighlightsProps {
  highlights: NonNullable<HomeAboutUs['highlights']>
}

export default function AboutUsHighlights({ highlights }: AboutUsHighlightsProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.4 }}
      viewport={{ once: true }}
      className="flex flex-wrap gap-8 md:divide-x md:divide-primary mt-8 border-t border-gray-200 pt-8"
    >
      {highlights.map((highlight, index) => (
        <div key={highlight.id || index} className="mb-6 md:mb-0 w-full md:w-auto pr-8">
          <div className="flex items-center gap-2">
            <Text variant="h4" className="text-5xl font-bold text-primary">
              {highlight.number}
            </Text>
            {highlight.plus && (
              <Text variant="h4" className="text-5xl mt-2 font-bold text-primary">
                +
              </Text>
            )}
          </div>

          <Text className="text-gray-600 max-w-[150px]">{highlight.description}</Text>
        </div>
      ))}
    </motion.div>
  )
}
