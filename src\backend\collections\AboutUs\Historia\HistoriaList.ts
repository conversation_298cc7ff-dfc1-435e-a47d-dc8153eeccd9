import { GROUPS } from "@/backend/constants/groups";
import { lexicalEditor } from "@payloadcms/richtext-lexical";
import { CollectionConfig } from "payload";

export const HistoriaList: CollectionConfig = {
    slug: "historia-list",
    labels: {
        singular: "Historia",
        plural: "Historias",
    },
    admin: {
        useAsTitle: "year",
        group: GROUPS.ABOUT_US,
        description: "Lista de historias para la sección de Historia",
    },
    fields: [
        {
            name: "year",
            type: "text",
            required: true,
            label: "Año",
            admin: {
                description: "Año de la historia",
            },
        },
        {
            name: "resume",
            type: "textarea",
            required: true,
            label: "Resumen",
            admin: {
                description: "Resumen de la historia",
            },
        },
        {
            name: "description",
            type: "richText",
            required: true,
            label: "Cuerpo",
            editor: lexicalEditor(),
            admin: {
                description: "Descripción de la historia",
            },
        },
        {
            name: "images",
            type: "array",
            label: "Imágene<PERSON>",
            admin: {
                description: "Imágenes de la historia",
            },
            labels: {
                singular: "Imagen",
                plural: "Imá<PERSON><PERSON>",
            },
            minRows: 1,
            maxRows: 5,
            fields: [
                {
                    name: "image",
                    type: "upload",
                    relationTo: "media",
                    required: true,
                    label: "Imagen",
                    admin: {
                        description: "Imagen de la historia",
                    },
                }
            ]
        }
    ]
}