'use client'

import { motion, MotionProps, Variants } from 'motion/react'
import { ReactNode } from 'react'

export type RotateDirection = 'clockwise' | 'counterclockwise' | 'flip-x' | 'flip-y' | 'flip-diagonal'

export interface RotateProps extends Omit<MotionProps, 'variants' | 'initial' | 'animate' | 'exit'> {
  children: ReactNode
  /** Tipo de rotación */
  direction?: RotateDirection
  /** Duración de la animación en segundos */
  duration?: number
  /** Retraso antes de iniciar la animación */
  delay?: number
  /** Ángulo de rotación inicial en grados */
  initialRotation?: number
  /** Ángulo de rotación final en grados */
  finalRotation?: number
  /** Opacidad inicial */
  initialOpacity?: number
  /** Opacidad final */
  finalOpacity?: number
  /** Función de easing personalizada */
  ease?: string | number[]
  /** Si la animación debe ejecutarse una sola vez */
  once?: boolean
  /** Umbral de visibilidad para activar la animación */
  threshold?: number
  /** Margen para el viewport */
  margin?: string
  /** Si debe animar al salir */
  animateExit?: boolean
  /** Punto de origen de la transformación */
  transformOrigin?: string
  /** Si debe repetirse la animación */
  repeat?: number | boolean
  /** Dirección de la repetición */
  repeatType?: 'loop' | 'reverse' | 'mirror'
  /** Clase CSS adicional */
  className?: string
}

const createRotateVariants = (
  direction: RotateDirection,
  initialRotation: number,
  finalRotation: number,
  initialOpacity: number,
  finalOpacity: number,
  duration: number,
  delay: number,
  ease: string | number[],
  repeat?: number | boolean,
  repeatType?: 'loop' | 'reverse' | 'mirror'
): Variants => {
  const baseTransition = {
    duration,
    delay,
    ease,
    repeat: repeat === true ? Infinity : (repeat || undefined),
    repeatType,
  }

  const getRotationValues = () => {
    switch (direction) {
      case 'clockwise':
        return {
          initial: { rotate: initialRotation, opacity: initialOpacity },
          final: { rotate: finalRotation, opacity: finalOpacity }
        }
      case 'counterclockwise':
        return {
          initial: { rotate: -initialRotation, opacity: initialOpacity },
          final: { rotate: -finalRotation, opacity: finalOpacity }
        }
      case 'flip-x':
        return {
          initial: { rotateX: 90, opacity: initialOpacity },
          final: { rotateX: 0, opacity: finalOpacity }
        }
      case 'flip-y':
        return {
          initial: { rotateY: 90, opacity: initialOpacity },
          final: { rotateY: 0, opacity: finalOpacity }
        }
      case 'flip-diagonal':
        return {
          initial: { rotateX: 45, rotateY: 45, opacity: initialOpacity },
          final: { rotateX: 0, rotateY: 0, opacity: finalOpacity }
        }
      default:
        return {
          initial: { rotate: initialRotation, opacity: initialOpacity },
          final: { rotate: finalRotation, opacity: finalOpacity }
        }
    }
  }

  const { initial, final } = getRotationValues()

  return {
    hidden: initial,
    visible: {
      ...final,
      transition: baseTransition
    },
    exit: initial,
  }
}

export const Rotate: React.FC<RotateProps> = ({
  children,
  direction = 'clockwise',
  duration = 0.6,
  delay = 0,
  initialRotation = -180,
  finalRotation = 0,
  initialOpacity = 0,
  finalOpacity = 1,
  ease = 'easeOut',
  once = true,
  threshold = 0.1,
  margin = '0px',
  animateExit = false,
  transformOrigin = 'center',
  repeat,
  repeatType = 'loop',
  className,
  ...motionProps
}) => {
  const variants = createRotateVariants(
    direction,
    initialRotation,
    finalRotation,
    initialOpacity,
    finalOpacity,
    duration,
    delay,
    ease,
    repeat,
    repeatType
  )

  return (
    <motion.div
      className={className}
      style={{ transformOrigin }}
      variants={variants}
      initial="hidden"
      whileInView="visible"
      exit={animateExit ? "exit" : undefined}
      viewport={{
        once,
        amount: threshold,
        margin
      }}
      {...motionProps}
    >
      {children}
    </motion.div>
  )
}

// Componentes predefinidos para casos comunes
export const RotateIn: React.FC<Omit<RotateProps, 'direction'>> = (props) => (
  <Rotate direction="clockwise" {...props} />
)

export const RotateInCounterclockwise: React.FC<Omit<RotateProps, 'direction'>> = (props) => (
  <Rotate direction="counterclockwise" {...props} />
)

export const FlipX: React.FC<Omit<RotateProps, 'direction'>> = (props) => (
  <Rotate direction="flip-x" {...props} />
)

export const FlipY: React.FC<Omit<RotateProps, 'direction'>> = (props) => (
  <Rotate direction="flip-y" {...props} />
)

export const FlipDiagonal: React.FC<Omit<RotateProps, 'direction'>> = (props) => (
  <Rotate direction="flip-diagonal" {...props} />
)

// Variantes con configuraciones específicas
export const RotateInSlow: React.FC<Omit<RotateProps, 'direction' | 'duration'>> = (props) => (
  <Rotate direction="clockwise" duration={1.2} {...props} />
)

export const RotateInFast: React.FC<Omit<RotateProps, 'direction' | 'duration'>> = (props) => (
  <Rotate direction="clockwise" duration={0.3} {...props} />
)

export const RotateInBounce: React.FC<Omit<RotateProps, 'direction' | 'ease'>> = (props) => (
  <Rotate direction="clockwise" ease={[0.68, -0.55, 0.265, 1.55]} {...props} />
)

export const RotateInFull: React.FC<Omit<RotateProps, 'direction' | 'initialRotation'>> = (props) => (
  <Rotate direction="clockwise" initialRotation={-360} {...props} />
)

// Animaciones continuas
export const RotateContinuous: React.FC<Omit<RotateProps, 'repeat' | 'finalRotation'>> = (props) => (
  <Rotate
    finalRotation={360}
    repeat={true}
    ease="linear"
    {...props}
  />
)

export const RotatePulse: React.FC<Omit<RotateProps, 'repeat' | 'repeatType' | 'initialRotation' | 'finalRotation'>> = (props) => (
  <Rotate
    initialRotation={0}
    finalRotation={10}
    repeat={true}
    repeatType="reverse"
    duration={1}
    ease="easeInOut"
    {...props}
  />
)

export default Rotate
