import { stringify } from 'qs-esm'
import { Where } from 'payload'

export function fetcher(
    uri: string,
    options?: RequestInit & {
        query?: Where
    },
) {
    const stringifiedQuery = stringify(
        {
            where: options?.query,
        },
        { addQueryPrefix: true },
    )

    return fetch(`${process.env.NEXT_PUBLIC_API_URL}${uri}${stringifiedQuery}`, {
        ...options,
        headers: {
            ...options?.headers,
            'Content-Type': 'application/json',
        },
        credentials: 'include',
    })
}