'use client'

import { motion, MotionProps, Variants } from 'motion/react'
import { ReactNode } from 'react'

export type FadeDirection = 'up' | 'down' | 'left' | 'right' | 'none'

export interface FadeProps extends Omit<MotionProps, 'variants' | 'initial' | 'animate' | 'exit'> {
  children: ReactNode
  /** Dirección de la animación de fade */
  direction?: FadeDirection
  /** Duración de la animación en segundos */
  duration?: number
  /** Retraso antes de iniciar la animación */
  delay?: number
  /** Distancia del movimiento en píxeles */
  distance?: number
  /** Opacidad inicial */
  initialOpacity?: number
  /** Opacidad final */
  finalOpacity?: number
  /** Función de easing personalizada */
  ease?: string | number[]
  /** Si la animación debe ejecutarse una sola vez */
  once?: boolean
  /** Umbral de visibilidad para activar la animación */
  threshold?: number
  /** Margen para el viewport */
  margin?: string
  /** Si debe animar al salir */
  animateExit?: boolean
  /** Clase CSS adicional */
  className?: string
}

const createFadeVariants = (
  direction: FadeDirection,
  distance: number,
  initialOpacity: number,
  finalOpacity: number,
  duration: number,
  delay: number,
  ease: string | number[]
): Variants => {
  const getInitialTransform = () => {
    switch (direction) {
      case 'up':
        return { y: distance, opacity: initialOpacity }
      case 'down':
        return { y: -distance, opacity: initialOpacity }
      case 'left':
        return { x: distance, opacity: initialOpacity }
      case 'right':
        return { x: -distance, opacity: initialOpacity }
      case 'none':
      default:
        return { opacity: initialOpacity }
    }
  }

  const getFinalTransform = () => {
    switch (direction) {
      case 'up':
      case 'down':
        return { y: 0, opacity: finalOpacity }
      case 'left':
      case 'right':
        return { x: 0, opacity: finalOpacity }
      case 'none':
      default:
        return { opacity: finalOpacity }
    }
  }

  return {
    hidden: getInitialTransform(),
    visible: {
      ...getFinalTransform(),
      transition: {
        duration,
        delay,
        ease,
      },
    },
    exit: getInitialTransform(),
  }
}

export const Fade: React.FC<FadeProps> = ({
  children,
  direction = 'up',
  duration = 0.6,
  delay = 0,
  distance = 30,
  initialOpacity = 0,
  finalOpacity = 1,
  ease = 'easeOut',
  once = true,
  threshold = 0.1,
  margin = '0px',
  animateExit = false,
  className,
  ...motionProps
}) => {
  const variants = createFadeVariants(
    direction,
    distance,
    initialOpacity,
    finalOpacity,
    duration,
    delay,
    ease
  )

  return (
    <motion.div
      className={className}
      variants={variants}
      initial="hidden"
      whileInView="visible"
      exit={animateExit ? "exit" : undefined}
      viewport={{ 
        once, 
        amount: threshold,
        margin 
      }}
      {...motionProps}
    >
      {children}
    </motion.div>
  )
}

// Componentes predefinidos para casos comunes
export const FadeUp: React.FC<Omit<FadeProps, 'direction'>> = (props) => (
  <Fade direction="up" {...props} />
)

export const FadeDown: React.FC<Omit<FadeProps, 'direction'>> = (props) => (
  <Fade direction="down" {...props} />
)

export const FadeLeft: React.FC<Omit<FadeProps, 'direction'>> = (props) => (
  <Fade direction="left" {...props} />
)

export const FadeRight: React.FC<Omit<FadeProps, 'direction'>> = (props) => (
  <Fade direction="right" {...props} />
)

export const FadeIn: React.FC<Omit<FadeProps, 'direction'>> = (props) => (
  <Fade direction="none" {...props} />
)

// Variantes con configuraciones predefinidas
export const FadeUpSlow: React.FC<Omit<FadeProps, 'direction' | 'duration'>> = (props) => (
  <Fade direction="up" duration={1.2} {...props} />
)

export const FadeUpFast: React.FC<Omit<FadeProps, 'direction' | 'duration'>> = (props) => (
  <Fade direction="up" duration={0.3} {...props} />
)

export const FadeUpBounce: React.FC<Omit<FadeProps, 'direction' | 'ease'>> = (props) => (
  <Fade direction="up" ease={[0.68, -0.55, 0.265, 1.55]} {...props} />
)

export default Fade
