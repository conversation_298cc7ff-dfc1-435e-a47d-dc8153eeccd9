import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'

export default function NotFound() {
    return (
        <div className="container py-20 md:py-32 flex flex-col items-center justify-center text-center">
            <div className="max-w-md mx-auto">
                <h1 className="text-6xl font-bold text-primary mb-4">404</h1>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                    Actividad no encontrada
                </h2>
                <p className="text-muted-foreground mb-8">
                    Lo sentimos, no hemos podido encontrar la actividad que estás buscando.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button asChild>
                        <Link href="/actividades-academicas" className="flex items-center gap-2">
                            <ArrowLeft className="h-4 w-4" />
                            Volver a la página de actividades
                        </Link>
                    </Button>
                    <Button asChild variant="outline">
                        <Link href="/">Ir al inicio</Link>
                    </Button>
                </div>
            </div>
        </div>
    )
}