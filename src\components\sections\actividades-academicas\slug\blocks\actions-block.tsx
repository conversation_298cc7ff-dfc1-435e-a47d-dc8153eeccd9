'use client'
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Text } from '@/components/ui/text'
import { ExternalLink, Download, Calendar, Share2 } from 'lucide-react'

interface ActionsBlockProps {
  block: {
    blockType: 'actions'
    title?: string
    actions?: Array<{
      label: string
      url: string
      type: 'primary' | 'secondary' | 'outline'
      icon?: 'external' | 'download' | 'calendar' | 'share'
      description?: string
    }>
  }
}

const iconMap = {
  external: ExternalLink,
  download: Download,
  calendar: Calendar,
  share: Share2,
}

export default function ActionsBlock({ block }: ActionsBlockProps) {
  if (!block.actions || block.actions.length === 0) {
    return null
  }

  const getButtonVariant = (type: string) => {
    switch (type) {
      case 'primary':
        return 'default'
      case 'secondary':
        return 'secondary'
      case 'outline':
        return 'outline'
      default:
        return 'outline'
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ExternalLink className="w-5 h-5 text-primary" />
          {block.title || 'Acciones'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {block.actions.map((action, index) => {
            const IconComponent = action.icon ? iconMap[action.icon] : ExternalLink

            return (
              <div key={index} className="space-y-2">
                <Button
                  variant={getButtonVariant(action.type)}
                  className="w-full justify-start"
                  asChild
                >
                  <a href={action.url} target="_blank" rel="noopener noreferrer">
                    <IconComponent className="w-4 h-4 mr-2" />
                    {action.label}
                  </a>
                </Button>

                {action.description && (
                  <Text variant="base" className="text-xs text-muted-foreground">
                    {action.description}
                  </Text>
                )}
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
