'use client'
import { FadeUp } from "@/components/motion";
import { Text } from "@/components/ui/text";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  MapPinIcon,
  GlobeIcon,
  VideoIcon,
  BuildingIcon,
  ClipboardListIcon,
  ExternalLinkIcon
} from "lucide-react";

interface LocationBlockData {
  modality: 'online' | 'presencial' | 'hybrid';
  conferenceLink?: string | null;
  platform?: 'zoom' | 'meet' | 'teams' | 'youtube' | 'other' | null;
  address?: string | null;
  venue?: string | null;
  instructions?: string | null;
}

interface LocationTabProps {
  title?: string;
  data: LocationBlockData;
}

export default function LocationTab({ title = "Ubicación y Acceso", data }: LocationTabProps) {
  const getModalityLabel = (modality: string) => {
    const labels = {
      online: 'En línea',
      presencial: 'Presencial',
      hybrid: 'Híbrido'
    };
    return labels[modality as keyof typeof labels] || modality;
  };

  const getModalityIcon = (modality: string) => {
    const icons = {
      online: VideoIcon,
      presencial: BuildingIcon,
      hybrid: GlobeIcon
    };
    return icons[modality as keyof typeof icons] || GlobeIcon;
  };

  const getPlatformLabel = (platform: string) => {
    const labels = {
      zoom: 'Zoom',
      meet: 'Google Meet',
      teams: 'Microsoft Teams',
      youtube: 'YouTube Live',
      other: 'Otra plataforma'
    };
    return labels[platform as keyof typeof labels] || platform;
  };

  const ModalityIcon = getModalityIcon(data.modality);

  return (
    <section className="p-8 space-y-6">
      <FadeUp delay={0.1}>
        <Text variant="h3" className="text-primary mb-6">
          {title}
        </Text>
      </FadeUp>

      {/* Modalidad */}
      <FadeUp delay={0.2}>
        <div className="flex items-center gap-3 mb-6">
          <ModalityIcon className="w-6 h-6 text-primary" />
          <div>
            <Text variant="h4" className="text-gray-900">
              Modalidad
            </Text>
            <Badge variant="secondary" className="mt-1">
              {getModalityLabel(data.modality)}
            </Badge>
          </div>
        </div>
      </FadeUp>

      {/* Información específica según modalidad */}
      <div className="space-y-6">
        {/* Online/Híbrido - Enlace de conferencia */}
        {(data.modality === 'online' || data.modality === 'hybrid') && data.conferenceLink && (
          <FadeUp delay={0.3}>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <VideoIcon className="w-5 h-5 text-blue-600 mt-0.5" />
                <div className="flex-1">
                  <Text variant="h4" className="text-blue-900 mb-2">
                    Enlace de Acceso
                  </Text>
                  {data.platform && (
                    <Text className="text-blue-700 text-sm mb-3">
                      Plataforma: {getPlatformLabel(data.platform)}
                    </Text>
                  )}
                  <Button asChild variant="default" size="sm">
                    <a
                      href={data.conferenceLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2"
                    >
                      <ExternalLinkIcon className="w-4 h-4" />
                      Unirse a la conferencia
                    </a>
                  </Button>
                </div>
              </div>
            </div>
          </FadeUp>
        )}

        {/* Presencial/Híbrido - Ubicación física */}
        {(data.modality === 'presencial' || data.modality === 'hybrid') && (data.address || data.venue) && (
          <FadeUp delay={0.4}>
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <MapPinIcon className="w-5 h-5 text-green-600 mt-0.5" />
                <div className="flex-1">
                  <Text variant="h4" className="text-green-900 mb-2">
                    Ubicación Física
                  </Text>
                  {data.venue && (
                    <Text className="text-green-800 font-medium mb-2">
                      {data.venue}
                    </Text>
                  )}
                  {data.address && (
                    <Text className="text-green-700 text-sm">
                      {data.address}
                    </Text>
                  )}
                </div>
              </div>
            </div>
          </FadeUp>
        )}

        {/* Instrucciones de acceso */}
        {data.instructions && (
          <FadeUp delay={0.5}>
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <ClipboardListIcon className="w-5 h-5 text-amber-600 mt-0.5" />
                <div className="flex-1">
                  <Text variant="h4" className="text-amber-900 mb-2">
                    Instrucciones de Acceso
                  </Text>
                  <Text className="text-amber-800 text-sm leading-relaxed">
                    {data.instructions}
                  </Text>
                </div>
              </div>
            </div>
          </FadeUp>
        )}
      </div>

      {/* Información adicional para modalidad híbrida */}
      {data.modality === 'hybrid' && (
        <FadeUp delay={0.6}>
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <GlobeIcon className="w-5 h-5 text-purple-600 mt-0.5" />
              <div className="flex-1">
                <Text variant="h4" className="text-purple-900 mb-2">
                  Modalidad Híbrida
                </Text>
                <Text className="text-purple-800 text-sm leading-relaxed">
                  Este evento se realizará de forma híbrida. Puedes participar tanto presencialmente
                  como en línea. Elige la modalidad que mejor se adapte a tus necesidades.
                </Text>
              </div>
            </div>
          </div>
        </FadeUp>
      )}
    </section>
  );
}
