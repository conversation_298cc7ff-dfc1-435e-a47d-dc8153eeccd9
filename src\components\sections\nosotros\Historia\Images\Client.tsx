"use client"

import { HistoriaList } from "@/payload-types"
import { useCallback, useEffect, useState } from "react";
import useEmblaCarousel from 'embla-carousel-react'
import Fade from 'embla-carousel-fade'
import Autoplay from 'embla-carousel-autoplay'
import { EmblaCarouselType } from 'embla-carousel'
import { cn } from "@/lib/utils";
import { useAutoplay } from "@/lib/hooks/useAutoplay";
import { AnimatePresence, motion, Variants } from "motion/react";
import { Text } from "@/components/ui/text";
import { RichText } from "@payloadcms/richtext-lexical/react";

const INTERVAL = 5000;

export default function NosotrosHistoriaImagesClient({
    historiaList
}: {
    historiaList: HistoriaList[]
}) {
    const [showItem, setShowItem] = useState<HistoriaList | null>(null)

    const [emblaRef, emblaApi] = useEmblaCarousel({
        loop: true,
    }, [
        Fade(),
        Autoplay({
            playOnInit: true,
            delay: INTERVAL,
        })
    ])

    const { startAutoplay, stopAutoplay } = useAutoplay(emblaApi)

    useEffect(() => {
        if (showItem) {
            // Stop autoplay when an item is shown
            stopAutoplay();
        } else {
            // Restart autoplay when no item is shown
            startAutoplay();
        }

    }, [showItem, startAutoplay, stopAutoplay])

    return (
        <div>
            <AnimatePresence>
                {
                    showItem && (
                        <ItemShowModal item={showItem} setShowItem={setShowItem} />
                    )
                }

            </AnimatePresence>

            <div ref={emblaRef} className="overflow-hidden h-100">
                <div className="flex">
                    {historiaList.map((item, index) => {
                        const imageURL =
                            item?.images &&
                                item.images.length > 0 &&
                                typeof item.images[0].image === "object" &&
                                item.images[0].image !== null &&
                                "url" in item.images[0].image
                                ? (item.images[0].image as { url: string }).url
                                : undefined;
                        return (
                            <motion.div
                                key={index}
                                className="min-w-0 shrink-0 grow-0 basis-full relative h-100 cursor-pointer"
                                onMouseEnter={showItem == null ? stopAutoplay : undefined}
                                onMouseLeave={showItem == null ? startAutoplay : undefined}
                                layoutId={`historia-image-container-${item.year}`}
                                onClick={() => {
                                    setShowItem(item)
                                }}
                            >
                                <motion.div
                                    layoutId={`historia-image-image-${item.year}`}
                                    className="w-full h-full"
                                >
                                    <img
                                        src={imageURL}
                                        alt={`Historia ${item.year}`}
                                        className="w-full h-full object-cover rounded-lg"
                                    />
                                </motion.div>
                                <div className="rounded-b-lg absolute bottom-0 pointer-events-none text-white px-3 pb-3 pt-6 bg-gradient-to-t from-primary to-transparent w-full">
                                    <span className="text-sm italic">
                                        {item.resume}
                                    </span>
                                </div>
                            </motion.div>
                        )
                    })}
                </div>
            </div>
            <Dots
                emblaApi={emblaApi}
                historiaList={historiaList}
            />

        </div>
    )
}

// TODO: Fix Bug when closing the modal, the image return to a place that is not the original one
function ItemShowModal({
    item,
    setShowItem
}: {
    item: HistoriaList
    setShowItem: (item: HistoriaList | null) => void
}) {
    const imageURL =
        item?.images &&
            item.images.length > 0 &&
            typeof item.images[0].image === "object" &&
            item.images[0].image !== null &&
            "url" in item.images[0].image
            ? (item.images[0].image as { url: string }).url
            : undefined;

    useEffect(() => {
        document.body.style.overflow = "hidden";
        document.addEventListener("keydown", (e) => {
            if (e.key === "Escape") {
                setShowItem(null);
            }
        });

        return () => {
            document.body.style.overflow = "";
            document.removeEventListener("keydown", (e) => {
                if (e.key === "Escape") {
                    setShowItem(null);
                }
            });
        };
    }, [setShowItem]);

    return (
        <motion.div
            className="fixed top-0 left-0 w-full h-full z-9999 bg-black/50 flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowItem(null)}
        >
            <motion.div
                layoutId={`historia-image-container-${item.year}`}
                className="max-w-3xl w-full max-h-[90vh] overflow-hidden relative rounded-lg bg-white"
                onClick={(e) => e.stopPropagation()}
            >
                <motion.div
                    layoutId={`historia-image-image-${item.year}`}
                    className="w-full h-100"
                >
                    <img
                        src={imageURL}
                        alt={`Historia ${item.year}`}
                        className="w-full h-full object-cover rounded-t-lg"
                    />
                </motion.div>
                <div className="p-5">
                    <Text variant="h2" className="mb-4">{item.year}</Text>
                    <RichText
                        data={item.description}
                    />
                </div>
            </motion.div>
        </motion.div>
    )
}



function Dots({
    emblaApi,
    historiaList
}: {
    emblaApi: EmblaCarouselType | undefined
    historiaList: HistoriaList[]
}) {
    const [selectedIndex, setSelectedIndex] = useState(0)


    const onSelect = useCallback((emblaApi: EmblaCarouselType) => {
        setSelectedIndex(emblaApi.selectedScrollSnap())
    }, [])

    useEffect(() => {
        if (!emblaApi) return
        onSelect(emblaApi)
        emblaApi.on("select", onSelect)
    }, [emblaApi, onSelect])

    return (
        <div className="flex mt-4 gap-2">
            {historiaList.map((item, index) => (
                <Dot
                    key={index}
                    index={index}
                    item={item}
                    isSelected={index === selectedIndex}
                    emblaApi={emblaApi}
                />
            ))}
        </div>
    )
}

const dotProgressVariant: Variants = {
    initial: {
        opacity: 0,
        width: "0%"
    },

    visible: {
        opacity: [0, 1, 1],
        width: "100%",
        transition: {
            duration: 5,
            times: [0, 0.01, 1]
        }
    },
    hidden: {
        opacity: [1, 0, 0],
        width: ["100%", "100%", "0%"],
        transition: {
            duration: 0.5,
            times: [0, 0.9, 1]
        }
    }
}

function Dot({
    item,
    isSelected,
    index,
    emblaApi
}: {
    index: number
    item: HistoriaList
    isSelected: boolean
    emblaApi: EmblaCarouselType | undefined
}) {
    const { startAutoplay, stopAutoplay, autoplayIsPlaying } = useAutoplay(emblaApi)

    const onClick = useCallback(
        (index: number) => {
            if (!emblaApi) return
            emblaApi.scrollTo(index)
            stopAutoplay()
        },
        [emblaApi, stopAutoplay]
    )

    return (
        <button
            className="flex flex-col flex-1 max-w-12 items-center gap-1 cursor-pointer"
            onClick={() => onClick(index)}
            onMouseLeave={startAutoplay}
            onMouseEnter={isSelected ? stopAutoplay : undefined}
        >
            <div
                className={cn(
                    "w-full h-[4px] rounded-full transition-colors duration-300 ease-in-out relative overflow-hidden",
                    !isSelected ? "bg-gray-300" : (!autoplayIsPlaying ? "bg-primary" : "bg-gray-400")
                )}
            >
                <motion.div className="absolute h-full bg-primary"
                    variants={dotProgressVariant}
                    initial="initial"
                    animate={isSelected && autoplayIsPlaying ? "visible" : "hidden"}
                />
            </div>
            <span className={cn("text-sm font-[600] transition-colors duration-300 ease-in-out", !isSelected ? "text-gray-300" : "text-primary")}>
                {item.year}
            </span>
        </button>
    )
}