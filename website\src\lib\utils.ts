import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export async function wait(time: number) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true)
    }, time)
  })
}

export function isInternalLink(href: string) {
  const websiteUrl = process.env.NEXT_PUBLIC_SERVER_URL || ""
  return href.startsWith(websiteUrl)
}
