import { ButtonFields, ButtonLabels } from '@/backend/constants/fields/buttons.field'
import type { Block } from 'payload'

export const ActionsBlock: Block = {
  slug: 'actions',
  labels: {
    singular: 'Acciones',
    plural: 'Acciones',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      label: 'Título de la Sección',
      defaultValue: 'Inscríbete al Evento',
      admin: {
        description: 'Título que aparecerá encima de los botones',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Descripción',
      admin: {
        description: 'Texto descriptivo que aparecerá antes de los botones',
      },
    },
    {
      name: 'buttons',
      type: 'array',
      label: 'Botones de Acción',
      admin: {
        description: 'Botones de acción para este evento (registro, más información, etc.)',
      },
      labels: ButtonLabels,
      fields: ButtonFields,
    },
  ],
}
