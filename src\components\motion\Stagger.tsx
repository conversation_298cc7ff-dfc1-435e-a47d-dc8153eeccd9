'use client'

import { motion, MotionProps, Variants } from 'motion/react'
import { ReactNode, Children } from 'react'

export interface StaggerProps extends Omit<MotionProps, 'variants' | 'initial' | 'animate'> {
  children: ReactNode
  /** Retraso entre cada elemento hijo en segundos */
  staggerDelay?: number
  /** Duración de cada animación individual */
  duration?: number
  /** Retraso inicial antes de comenzar */
  initialDelay?: number
  /** Función de easing */
  ease?: string | number[]
  /** Si la animación debe ejecutarse una sola vez */
  once?: boolean
  /** Umbral de visibilidad */
  threshold?: number
  /** Dirección del stagger */
  direction?: 'normal' | 'reverse'
  /** Tipo de animación para los hijos */
  childAnimation?: 'fade' | 'slide' | 'scale' | 'rotate' | 'custom'
  /** Distancia para animaciones de slide */
  slideDistance?: number
  /** Dirección del slide */
  slideDirection?: 'up' | 'down' | 'left' | 'right'
  /** Escala inicial para animaciones de scale */
  initialScale?: number
  /** Rotación inicial para animaciones de rotate */
  initialRotation?: number
  /** Variantes personalizadas para childAnimation='custom' */
  customVariants?: Variants
  /** Clase CSS adicional */
  className?: string
}

const createChildVariants = (
  childAnimation: StaggerProps['childAnimation'],
  slideDistance: number,
  slideDirection: StaggerProps['slideDirection'],
  initialScale: number,
  initialRotation: number,
  duration: number,
  ease: string | number[],
  customVariants?: Variants
): Variants => {
  if (childAnimation === 'custom' && customVariants) {
    return customVariants
  }

  const baseTransition = { duration, ease }

  switch (childAnimation) {
    case 'fade':
      return {
        hidden: { opacity: 0 },
        visible: { opacity: 1, transition: baseTransition },
      }

    case 'slide':
      const getSlideTransform = () => {
        switch (slideDirection) {
          case 'up': return { y: slideDistance, opacity: 0 }
          case 'down': return { y: -slideDistance, opacity: 0 }
          case 'left': return { x: slideDistance, opacity: 0 }
          case 'right': return { x: -slideDistance, opacity: 0 }
          default: return { y: slideDistance, opacity: 0 }
        }
      }

      return {
        hidden: getSlideTransform(),
        visible: {
          x: 0,
          y: 0,
          opacity: 1,
          transition: baseTransition
        },
      }

    case 'scale':
      return {
        hidden: { scale: initialScale, opacity: 0 },
        visible: { scale: 1, opacity: 1, transition: baseTransition },
      }

    case 'rotate':
      return {
        hidden: { rotate: initialRotation, opacity: 0 },
        visible: { rotate: 0, opacity: 1, transition: baseTransition },
      }

    default:
      return {
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0, transition: baseTransition },
      }
  }
}

export const Stagger: React.FC<StaggerProps> = ({
  children,
  staggerDelay = 0.1,
  duration = 0.6,
  initialDelay = 0,
  ease = 'easeOut',
  once = true,
  threshold = 0.1,
  direction = 'normal',
  childAnimation = 'fade',
  slideDistance = 30,
  slideDirection = 'up',
  initialScale = 0.8,
  initialRotation = -10,
  customVariants,
  className,
  ...motionProps
}) => {
  const childVariants = createChildVariants(
    childAnimation,
    slideDistance,
    slideDirection,
    initialScale,
    initialRotation,
    duration,
    ease,
    customVariants
  )

  const containerVariants: Variants = {
    hidden: {},
    visible: {
      transition: {
        delayChildren: initialDelay,
        staggerChildren: direction === 'reverse' ? -staggerDelay : staggerDelay,
        staggerDirection: direction === 'reverse' ? -1 : 1,
      },
    },
  }

  const childrenArray = Children.toArray(children)
  const processedChildren = direction === 'reverse' ? childrenArray.reverse() : childrenArray

  return (
    <motion.div
      className={className}
      variants={containerVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once, amount: threshold }}
      {...motionProps}
    >
      {processedChildren.map((child, index) => (
        <motion.div
          key={index}
          variants={childVariants}
        >
          {child}
        </motion.div>
      ))}
    </motion.div>
  )
}

// Componentes predefinidos para casos comunes
export const StaggerFade: React.FC<Omit<StaggerProps, 'childAnimation'>> = (props) => (
  <Stagger childAnimation="fade" {...props} />
)

export const StaggerSlideUp: React.FC<Omit<StaggerProps, 'childAnimation' | 'slideDirection'>> = (props) => (
  <Stagger childAnimation="slide" slideDirection="up" {...props} />
)

export const StaggerSlideLeft: React.FC<Omit<StaggerProps, 'childAnimation' | 'slideDirection'>> = (props) => (
  <Stagger childAnimation="slide" slideDirection="left" {...props} />
)

export const StaggerScale: React.FC<Omit<StaggerProps, 'childAnimation'>> = (props) => (
  <Stagger childAnimation="scale" {...props} />
)

export const StaggerRotate: React.FC<Omit<StaggerProps, 'childAnimation'>> = (props) => (
  <Stagger childAnimation="rotate" {...props} />
)

// Variantes con configuraciones específicas
export const StaggerFast: React.FC<Omit<StaggerProps, 'staggerDelay' | 'duration'>> = (props) => (
  <Stagger staggerDelay={0.05} duration={0.3} {...props} />
)

export const StaggerSlow: React.FC<Omit<StaggerProps, 'staggerDelay' | 'duration'>> = (props) => (
  <Stagger staggerDelay={0.2} duration={1} {...props} />
)

export const StaggerBounce: React.FC<Omit<StaggerProps, 'ease'>> = (props) => (
  <Stagger ease={[0.68, -0.55, 0.265, 1.55]} {...props} />
)

export default Stagger
