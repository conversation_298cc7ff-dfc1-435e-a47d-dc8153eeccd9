import { CarouselItem } from "@/types/carousel.items";

export const CAROUSEL_ITEMS: CarouselItem[] = [
  {
    id: "1",
    title: "Academia Peruana de Doctores",
    description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit.Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
    image: "https://apd.org.pe/wp-content/uploads/2022/08/portada5.jpg",
    buttons: [
      {
        id: "1",
        text: "Ver más",
        href: "https://www.facebook.com/academiaperuanadedoctores/",
        variant: "white"
      },
      {
        id: "2",
        text: "Contacto",
        href: "https://www.facebook.com/academiaperuanadedoctores/",
        variant: "secondary"
      }
    ]
  },
  {
    id: "2",
    title: "Academia Peruana de Doctoresddd",
    description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit.Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
    image: "https://i0.wp.com/apd.org.pe/wp-content/uploads/2022/08/iniciopaginaAPD1.jpg",
    buttons: [
      {
        id: "1",
        text: "Ver más",
        href: "https://www.facebook.com/academiaperuanadedoctores/",
        variant: "white"
      },
      {
        id: "2",
        text: "Contacto",
        href: "https://www.facebook.com/academiaperuanadedoctores/",
        variant: "secondary"
      }
    ]
  },
  {
    id: "3",
    title: "Academia Peruana de Doctoresddd",
    description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit.Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
    image: "https://apd.org.pe/wp-content/uploads/2022/08/portada111.jpg",
    buttons: [
      {
        id: "1",
        text: "Ver más",
        href: "https://www.facebook.com/academiaperuanadedoctores/",
        variant: "white"
      },
      {
        id: "2",
        text: "Contacto",
        href: "https://www.facebook.com/academiaperuanadedoctores/",
        variant: "secondary"
      }
    ]
  }
]
