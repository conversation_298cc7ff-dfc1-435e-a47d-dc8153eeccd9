import { AcademicActivity } from "@/payload-types"

export type LocationBlock = Extract<NonNullable<NonNullable<AcademicActivity["eventBlocks"]>[number]>, { blockType: "location" }>
export type AgendaBlock = Extract<NonNullable<NonNullable<AcademicActivity["eventBlocks"]>[number]>, { blockType: "agenda" }>
export type RequirementsBlock = Extract<NonNullable<NonNullable<AcademicActivity["eventBlocks"]>[number]>, { blockType: "requirements" }>
export type SpeakersBlock = Extract<NonNullable<NonNullable<AcademicActivity["eventBlocks"]>[number]>, { blockType: "speakers" }>

export type TabEventBlock = LocationBlock | AgendaBlock | SpeakersBlock | RequirementsBlock