{"name": "cms", "version": "1.0.0", "description": "A blank template to get started with Payload 3.0", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation next build", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev", "devsafe": "rm -rf .next && cross-env NODE_OPTIONS=--no-deprecation next dev", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "start": "cross-env NODE_OPTIONS=--no-deprecation next start"}, "dependencies": {"@payloadcms/admin-bar": "3.39.0", "@payloadcms/db-postgres": "3.39.0", "@payloadcms/live-preview-react": "3.39.0", "@payloadcms/next": "3.39.0", "@payloadcms/payload-cloud": "3.39.0", "@payloadcms/plugin-form-builder": "3.39.0", "@payloadcms/plugin-nested-docs": "3.39.0", "@payloadcms/plugin-redirects": "3.39.0", "@payloadcms/plugin-search": "3.39.0", "@payloadcms/plugin-seo": "3.39.0", "@payloadcms/richtext-lexical": "3.39.0", "@payloadcms/translations": "3.39.0", "@payloadcms/ui": "3.39.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slot": "^1.2.2", "@tailwindcss/postcss": "^4.1.6", "@tanstack/react-query": "^5.79.0", "add": "^2.0.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "dlx": "^0.2.1", "graphql": "^16.8.1", "lucide-react": "^0.510.0", "motion": "^12.11.0", "next": "15.3.0", "payload": "3.39.0", "pnpm": "^10.11.1", "postcss": "^8.5.3", "qs-esm": "^7.0.2", "react": "19.1.0", "react-day-picker": "^9.7.0", "react-dom": "19.1.0", "react-query-kit": "^3.3.1", "shadcn": "^2.6.0", "sharp": "0.32.6", "tabs": "^0.2.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.6"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@svgr/webpack": "^8.1.0", "@types/node": "^22.5.4", "@types/react": "19.1.0", "@types/react-dom": "19.1.2", "eslint": "^9.16.0", "eslint-config-next": "15.3.0", "prettier": "^3.4.2", "tw-animate-css": "^1.2.9", "typescript": "5.7.3"}, "engines": {"node": "^18.20.2 || >=20.9.0", "pnpm": "^9 || ^10"}, "pnpm": {"onlyBuiltDependencies": ["sharp"]}}