'use client'

import { forwardRef } from 'react'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import { buttonVariants } from '@/components/ui/button'
import DynamicIcon, { IconName } from '@/backend/components/lucide/dynamic-icon'
import { isInternalLink } from 'website/src/lib/utils'
import type { VariantProps } from 'class-variance-authority'

interface LinkButtonProps {
    href: string
    text: string
    icon?: IconName
    variant?: VariantProps<typeof buttonVariants>
    className?: string
}

const LinkButton = forwardRef<HTMLAnchorElement, LinkButtonProps>(
    ({ href, text, icon, variant, className }, ref) => {
        const isInternal = isInternalLink(href)

        const classes = cn(
            buttonVariants(variant),
            "flex items-center justify-center gap-2",
            className
        )

        const iconElement = icon ? (
            <DynamicIcon
                name={icon as IconName}
                className="w-5 h-5"
            />
        ) : null

        // Para enlaces internos, usamos Next.js Link
        if (isInternal) {
            return (
                <Link
                    href={href}
                    className={classes}
                    ref={ref}
                >
                    {iconElement}
                    <span>{text}</span>
                </Link>
            )
        }

        // Para enlaces externos, usamos <a> con target="_blank"
        return (
            <a
                href={href}
                target="_blank"
                rel="noopener noreferrer"
                className={classes}
                ref={ref}
            >
                {iconElement}
                <span>{text}</span>
            </a>
        )
    }
)

LinkButton.displayName = 'LinkButton'

export { LinkButton }