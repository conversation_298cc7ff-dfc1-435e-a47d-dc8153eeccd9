'use client'
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Text } from '@/components/ui/text'
import { Mail, Globe, Linkedin, Users } from 'lucide-react'
import { Speaker } from '@/payload-types'

interface SpeakersBlockProps {
  block: {
    blockType: 'speakers'
    title?: string
    speakers?: Speaker[]
    customSpeakers?: Array<{
      name: string
      title?: string
      organization?: string
      description?: string
      image?: any
    }>
  }
}

export default function SpeakersBlock({ block }: SpeakersBlockProps) {
  const allSpeakers = [
    ...(block.speakers || []),
    ...(block.customSpeakers || []).map(custom => ({
      id: `custom-${custom.name}`,
      fullName: custom.name,
      degree: undefined,
      specialty: custom.title,
      institution: custom.organization,
      bio: custom.description,
      photo: custom.image,
      active: true,
    }))
  ]

  if (allSpeakers.length === 0) {
    return null
  }

  const getSpeakerDisplayName = (speaker: any) => {
    let name = speaker.fullName || speaker.name
    
    if (speaker.degree && speaker.degree !== 'other') {
      const degreeLabels = {
        doctor: 'Dr.',
        doctora: 'Dra.',
        magister: 'Mg.',
        licenciado: 'Lic.',
        ingeniero: 'Ing.',
        profesor: 'Prof.',
        phd: 'PhD',
        msc: 'MSc',
      }
      const degreeLabel = degreeLabels[speaker.degree as keyof typeof degreeLabels]
      if (degreeLabel) {
        name = `${degreeLabel} ${name}`
      }
    }
    
    return name
  }

  const getSpeakerSubtitle = (speaker: any) => {
    const parts = []
    if (speaker.specialty) parts.push(speaker.specialty)
    if (speaker.institution) parts.push(speaker.institution)
    if (speaker.position) parts.push(speaker.position)
    return parts.join(' • ')
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="w-5 h-5 text-primary" />
          {block.title || 'Ponentes'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {allSpeakers.map((speaker, index) => (
            <div 
              key={speaker.id || index} 
              className="flex gap-4 p-4 border rounded-lg hover:shadow-sm transition-all"
            >
              <Avatar className="h-16 w-16 flex-shrink-0">
                <AvatarImage 
                  src={speaker.photo?.url || '/placeholder-avatar.jpg'} 
                  alt={speaker.fullName || speaker.name} 
                />
                <AvatarFallback className="text-sm">
                  {(speaker.fullName || speaker.name)
                    .split(' ')
                    .map((n: string) => n[0])
                    .join('')
                    .toUpperCase()
                    .slice(0, 2)}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1 min-w-0">
                <Text variant="h4" className="text-base font-semibold mb-1">
                  {getSpeakerDisplayName(speaker)}
                </Text>
                
                {getSpeakerSubtitle(speaker) && (
                  <Text variant="base" className="text-sm text-muted-foreground mb-2">
                    {getSpeakerSubtitle(speaker)}
                  </Text>
                )}
                
                {speaker.bio && (
                  <Text variant="base" className="text-sm leading-relaxed mb-3 line-clamp-3">
                    {speaker.bio}
                  </Text>
                )}
                
                {/* Contact Links */}
                <div className="flex gap-2 flex-wrap">
                  {speaker.email && (
                    <Button variant="outline" size="sm" asChild>
                      <a href={`mailto:${speaker.email}`}>
                        <Mail className="h-3 w-3 mr-1" />
                        Email
                      </a>
                    </Button>
                  )}
                  
                  {speaker.linkedin && (
                    <Button variant="outline" size="sm" asChild>
                      <a href={speaker.linkedin} target="_blank" rel="noopener noreferrer">
                        <Linkedin className="h-3 w-3 mr-1" />
                        LinkedIn
                      </a>
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
