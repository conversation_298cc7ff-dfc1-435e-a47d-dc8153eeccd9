'use client'
import { motion, MotionProps } from 'motion/react'
import { cn } from '@/lib/utils'

interface TextProps {
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'description' | 'base'
  className?: string
  children: React.ReactNode
  animationProps?: MotionProps
}

export function Text({ variant = 'base', className, children, ...props }: TextProps) {
  const { animationProps } = props
  const baseClasses = 'text-primary'

  const variantClasses = {
    h1: 'text-5xl font-bold capitalize',
    h2: 'text-3xl font-bold capitalize',
    h3: 'text-2xl font-semibold',
    h4: 'text-xl font-semibold',
    description: 'text-sm text-gray-800 max-w-md',
    base: 'text-base',
  }

  const mergedClasses = cn(baseClasses, variantClasses[variant], className)

  switch (variant) {
    case 'h1':
      return animationProps ? (
        <motion.h1 className={mergedClasses} {...animationProps}>
          {children}
        </motion.h1>
      ) : (
        <h1 className={mergedClasses}>{children}</h1>
      )
    case 'h2':
      return animationProps ? (
        <motion.h2 className={mergedClasses} {...animationProps}>
          {children}
        </motion.h2>
      ) : (
        <h2 className={mergedClasses}>{children}</h2>
      )
    case 'h3':
      return animationProps ? (
        <motion.h3 className={mergedClasses} {...animationProps}>
          {children}
        </motion.h3>
      ) : (
        <h3 className={mergedClasses}>{children}</h3>
      )
    case 'h4':
      return animationProps ? (
        <motion.h4 className={mergedClasses} {...animationProps}>
          {children}
        </motion.h4>
      ) : (
        <h4 className={mergedClasses}>{children}</h4>
      )
    default:
      return animationProps ? (
        <motion.p className={mergedClasses} {...animationProps}>
          {children}
        </motion.p>
      ) : (
        <p className={mergedClasses}>{children}</p>
      )
  }
}
