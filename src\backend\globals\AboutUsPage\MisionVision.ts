import { GROUPS } from "@/backend/constants/groups";
import { lexicalEditor } from "@payloadcms/richtext-lexical";
import { GlobalConfig } from "payload";

export const AboutUsPageMisionVision: GlobalConfig = {
    slug: "about-us-page-mision-vision",
    admin: {
        group: GROUPS.ABOUT_US,
    },
    label: "Misión y Visión",
    fields: [
        {
            name: "mision",
            type: "richText",
            required: true,
            label: "Misión",
            admin: {
                description: "Misión que se mostrará en la página de Sobre Nosotros",
            },
            editor: lexicalEditor(),
        },
        {
            name: "vision",
            type: "richText",
            required: true,
            label: "Visión",
            admin: {
                description: "Visión que se mostrará en la página de Sobre Nosotros",
            },
            editor: lexicalEditor(),
        },
    ],

}