import { useCallback, useEffect, useState } from 'react'
import { EmblaCarouselType } from 'embla-carousel'

type UseAutoplayType = {
    autoplayIsPlaying: boolean
    toggleAutoplay: () => void
    onAutoplayButtonClick: (callback: () => void) => void
    startAutoplay: () => void
    stopAutoplay: () => void

}

export const useAutoplay = (
    emblaApi: EmblaCarouselType | undefined
): UseAutoplayType => {
    const [autoplayIsPlaying, setAutoplayIsPlaying] = useState(false)

    const onAutoplayButtonClick = useCallback(
        (callback: () => void) => {
            const autoplay = emblaApi?.plugins()?.autoplay
            if (!autoplay) return

            const resetOrStop =
                autoplay.options.stopOnInteraction === false
                    ? autoplay.reset
                    : autoplay.stop

            resetOrStop()
            callback()
        },
        [emblaApi]
    )

    const toggleAutoplay = useCallback(() => {
        const autoplay = emblaApi?.plugins()?.autoplay
        if (!autoplay) return

        const playOrStop = autoplay.isPlaying() ? autoplay.stop : autoplay.play
        playOrStop()
    }, [emblaApi])

    useEffect(() => {
        const autoplay = emblaApi?.plugins()?.autoplay
        if (!autoplay) return

        setAutoplayIsPlaying(autoplay.isPlaying())
        emblaApi
            .on('autoplay:play', () => setAutoplayIsPlaying(true))
            .on('autoplay:stop', () => setAutoplayIsPlaying(false))
            .on('reInit', () => setAutoplayIsPlaying(autoplay.isPlaying()))
    }, [emblaApi])

    const startAutoplay = useCallback(() => {
        const autoplay = emblaApi?.plugins()?.autoplay
        if (!autoplay) return

        if (!autoplay.isPlaying()) {
            autoplay.play()
        }
    }, [emblaApi])

    const stopAutoplay = useCallback(() => {
        const autoplay = emblaApi?.plugins()?.autoplay
        if (!autoplay) return

        if (autoplay.isPlaying()) {
            autoplay.stop()
        }
    }, [emblaApi])

    return {
        autoplayIsPlaying,
        toggleAutoplay,
        onAutoplayButtonClick,
        startAutoplay,
        stopAutoplay,
    }
}
