"use client"

import { useId, useState, useEffect } from "react"
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import { DateRange } from "react-day-picker"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Label } from "@/components/ui/label"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

export interface DateRangePickerProps {
  /** Valor por defecto del rango de fechas */
  defaultValue?: DateRange
  /** Valor controlado del rango de fechas */
  value?: DateRange
  /** Callback cuando cambia el valor */
  onChange?: (date: DateRange | undefined) => void
  /** Etiqueta del campo */
  label?: string
  /** Texto placeholder cuando no hay fecha seleccionada */
  placeholder?: string
  /** ID del campo para accesibilidad */
  id?: string
  /** Nombre del campo para formularios */
  name?: string
  /** Si el campo es requerido */
  required?: boolean
  /** Si el campo está deshabilitado */
  disabled?: boolean
  /** Mensaje de error */
  error?: string
  /** Clase CSS adicional para el contenedor */
  className?: string
  /** Clase CSS adicional para el botón */
  buttonClassName?: string
  /** Fecha mínima seleccionable */
  minDate?: Date
  /** Fecha máxima seleccionable */
  maxDate?: Date
  /** Permitir fechas pasadas */
  allowPastDates?: boolean
  /** Formato de fecha personalizado */
  dateFormat?: string
  /** Variante del botón */
  variant?: "default" | "outline" | "ghost" | "secondary"
  /** Tamaño del componente */
  size?: "sm" | "md" | "lg"
  /** Mostrar texto de ayuda */
  showHelperText?: boolean
  /** Texto de ayuda personalizado */
  helperText?: string
  /** Alineación del popover */
  align?: "start" | "center" | "end"
  /** Callback de validación personalizada */
  onValidate?: (date: DateRange | undefined) => string | null
}

export function DateRangePicker({
  defaultValue,
  value,
  onChange,
  label = "Seleccionar rango de fechas",
  placeholder = "Selecciona un rango de fechas",
  id: providedId,
  name,
  required = false,
  disabled = false,
  error,
  className,
  buttonClassName,
  minDate,
  maxDate,
  allowPastDates = false,
  dateFormat = "dd/MM/yyyy",
  variant = "outline",
  size = "md",
  showHelperText = false,
  helperText,
  align = "start",
  onValidate,
}: DateRangePickerProps) {
  const generatedId = useId()
  const id = providedId || generatedId

  // Estado interno para modo no controlado
  const [internalDate, setInternalDate] = useState<DateRange | undefined>(defaultValue)

  // Determinar si es controlado o no controlado
  const isControlled = value !== undefined
  const currentDate = isControlled ? value : internalDate

  // Validación
  const [validationError, setValidationError] = useState<string | null>(null)

  useEffect(() => {
    if (onValidate) {
      const error = onValidate(currentDate)
      setValidationError(error)
    }
  }, [currentDate, onValidate])

  const handleDateChange = (newDate: DateRange | undefined) => {
    if (disabled) return

    // Validaciones básicas
    if (newDate?.from && !allowPastDates && newDate.from < new Date()) {
      setValidationError("No se pueden seleccionar fechas pasadas")
      return
    }

    if (newDate?.from && minDate && newDate.from < minDate) {
      setValidationError(`La fecha debe ser posterior a ${format(minDate, dateFormat)}`)
      return
    }

    if (newDate?.to && maxDate && newDate.to > maxDate) {
      setValidationError(`La fecha debe ser anterior a ${format(maxDate, dateFormat)}`)
      return
    }

    // Limpiar errores de validación
    setValidationError(null)

    // Actualizar estado
    if (!isControlled) {
      setInternalDate(newDate)
    }

    // Llamar callback
    onChange?.(newDate)
  }

  const formatDateRange = (dateRange: DateRange | undefined) => {
    if (!dateRange?.from) return placeholder

    if (dateRange.to) {
      return `${format(dateRange.from, dateFormat)} - ${format(dateRange.to, dateFormat)}`
    }

    return format(dateRange.from, dateFormat)
  }

  const sizeClasses = {
    sm: "h-8 text-xs px-2",
    md: "h-10 text-sm px-3",
    lg: "h-12 text-base px-4"
  }

  const errorMessage = error || validationError

  return (
    <div className={cn("space-y-2", className)}>
      {/* Label */}
      {label && (
        <Label
          htmlFor={id}
          className={cn(
            required && "after:content-['*'] after:text-red-500 after:ml-1"
          )}
        >
          {label}
        </Label>
      )}

      {/* Input oculto para formularios */}
      {name && (
        <input
          className="hidden"
          type="hidden"
          name={name}
          value={currentDate ? JSON.stringify(currentDate) : ""}
        />
      )}

      {/* Date Range Picker */}
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id={id}
            variant={variant}
            disabled={disabled}
            className={cn(
              "group bg-background hover:bg-background border-input w-full justify-between font-normal outline-offset-0 outline-none focus-visible:outline-[3px]",
              sizeClasses[size],
              !currentDate && "text-muted-foreground",
              errorMessage && "border-red-500 focus-visible:outline-red-500",
              disabled && "opacity-50 cursor-not-allowed",
              buttonClassName
            )}
          >
            <span className={cn("truncate", !currentDate && "text-muted-foreground")}>
              {formatDateRange(currentDate)}
            </span>
            <div className="flex items-center gap-2">
              <CalendarIcon
                size={16}
                className="text-muted-foreground/80 group-hover:text-foreground shrink-0 transition-colors"
                aria-hidden="true"
              />
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-2" align={align}>
          <Calendar
            mode="range"
            selected={currentDate}
            onSelect={handleDateChange}
            disabled={(date) => {
              if (!allowPastDates && date < new Date()) return true
              if (minDate && date < minDate) return true
              if (maxDate && date > maxDate) return true
              return false
            }}
          />
        </PopoverContent>
      </Popover>

      {/* Error Message */}
      {errorMessage && (
        <p className="text-sm text-red-500">
          {errorMessage}
        </p>
      )}

      {/* Helper Text */}
      {showHelperText && helperText && !errorMessage && (
        <p className="text-xs text-muted-foreground">
          {helperText}
        </p>
      )}
    </div>
  )
}

// Export por defecto para compatibilidad
export default DateRangePicker
