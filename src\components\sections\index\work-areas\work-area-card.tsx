'use client'
import { Text } from '@/components/ui/text'
import DynamicIcon from '@/backend/components/lucide/dynamic-icon'
import { motion } from 'motion/react'
import dynamicIconImports from 'lucide-react/dynamicIconImports'
import { WorkAreaCardProps } from './work-areas-types'

export default function WorkAreaCard({ item, index }: WorkAreaCardProps) {
  // Validar que el ícono existe en la lista de íconos disponibles
  const iconName = item.icon as keyof typeof dynamicIconImports
  const hasValidIcon = iconName && Object.keys(dynamicIconImports).includes(iconName)

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.6,
        delay: index * 0.1,
        ease: "easeOut"
      }}
      viewport={{ once: true, margin: "-50px" }}
      className="group"
    >
      <div className="p-8 h-full flex flex-col items-center text-center bg-transparent hover:bg-white/15 transition-all duration-300 hover:transform hover:scale-105">
        {/* Ícono */}
        <div className="mb-6 p-4 bg-white/20 rounded-full group-hover:bg-white/30 transition-all duration-300">
          {hasValidIcon ? (
            <DynamicIcon
              name={iconName}
              className="text-white size-8"
            />
          ) : (
            <div className="size-8 bg-white/30 rounded flex items-center justify-center">
              <span className="text-white text-xs">?</span>
            </div>
          )}
        </div>

        {/* Título */}
        <Text
          variant="h4"
          className="text-white font-bold text-xl mb-4 group-hover:text-white/90 transition-colors"
        >
          {item.title}
        </Text>

        {/* Descripción */}
        <Text
          variant="description"
          className="text-white/80 text-sm leading-relaxed mb-6 flex-grow"
        >
          {item.description}
        </Text>

        {/* Botón MÁS DETALLES */}
        {/* <Button
          variant="link"
          size="sm"
          className="border-white/30 text-white hover:bg-white hover:text-primary transition-all duration-300 text-xs font-medium tracking-wider"
        >
          MÁS DETALLES
        </Button> */}
      </div>
    </motion.div>
  )
}
