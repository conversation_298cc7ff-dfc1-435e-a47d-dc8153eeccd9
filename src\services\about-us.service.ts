import { getPayloadClient } from "@/lib/payload";
import { AboutUsPageBanner, AboutUsPageHistoriaPageBanner, AboutUsPageHistoriaResuman, AboutUsPageMisionVision, HistoriaList } from "@/payload-types";

export const AboutUsPageService = {
    getBanner: async (): Promise<AboutUsPageBanner | null> => {
        try {
            const payload = await getPayloadClient();

            return payload.findGlobal({
                slug: "about-us-page-banner",
                depth: 2,
            })

        } catch (error) {
            console.error("Error al obtener los datos de la sección 'Sobre Nosotros':", error);
            return null;
        }
    },

    getHistoriaResume: async (): Promise<AboutUsPageHistoriaResuman | null> => {
        try {
            const payload = await getPayloadClient();

            return payload.findGlobal({
                slug: "about-us-page-historia-resumen",
                depth: 2,
            })

        } catch (error) {
            console.error("Error al obtener Historia:", error);
            return null;
        }
    },

    getMisionVision: async (): Promise<AboutUsPageMisionVision | null> => {
        try {
            const payload = await getPayloadClient();

            return payload.findGlobal({
                slug: "about-us-page-mision-vision",
                depth: 2,
            })

        } catch (error) {
            console.error("Error al obtener Misión y Visión:", error);
            return null;
        }
    },

    getHistoriaList: async (): Promise<HistoriaList[]> => {
        try {
            const payload = await getPayloadClient();

            const response = await payload.find({
                collection: "historia-list",
                depth: 1,
                sort: "year",
            });

            return response.docs as HistoriaList[];

        } catch (error) {
            console.error("Error al obtener la lista de historias:", error);
            return [];
        }
    },

    getHistoriaPageBanner: async (): Promise<AboutUsPageHistoriaPageBanner | null> => {
        try {
            const payload = await getPayloadClient();

            return payload.findGlobal({
                slug: "about-us-page-historia-page-banner",
                depth: 2,
            })
        } catch (error) {
            console.error("Error al obtener el banner de la página de Historia:", error);
            return null;
        }
    }
}