import type { BeforeDeleteHook } from 'node_modules/payload/dist/collections/config/types'

export const validateImageUsage: BeforeDeleteHook = async ({ req, id }) => {
  const { payload } = req

  // Buscar si la imagen está siendo utilizada en algún registro de HomeCarousel
  const carouselItems = await payload.find({
    collection: 'home-carousel',
    where: {
      image: {
        equals: id,
      },
    },
  })

  // Si la imagen está siendo utilizada, lanzar un error con mensaje personalizado
  if (carouselItems.docs.length > 0) {
    throw new Error(
      `No se puede eliminar esta imagen porque está siendo utilizada en ${carouselItems.docs.length} slide(s) del carousel de inicio. Por favor, elimine primero los slides que utilizan esta imagen o cambie la imagen en esos slides.`,
    )
  }

  return
}
