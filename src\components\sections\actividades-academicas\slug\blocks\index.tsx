'use client'
import { AcademicActivity } from '@/payload-types'
import { FadeUp } from '@/components/motion'
import AgendaBlock from './agenda-block'
import SpeakersBlock from './speakers-block'
import LocationBlock from './location-block'
import ActionsBlock from './actions-block'

interface AcademicActivityBlocksProps {
  activity: AcademicActivity
}

export default function AcademicActivityBlocks({ activity }: AcademicActivityBlocksProps) {
  if (!activity.eventBlocks || activity.eventBlocks.length === 0) {
    return null
  }

  return (
    <div className="space-y-8">
      {activity.eventBlocks.map((block, index) => {
        const delay = index * 0.1

        switch (block.blockType) {
          case 'agenda':
            return (
              <FadeUp key={`agenda-${index}`} delay={delay}>
                <AgendaBlock block={block} />
              </FadeUp>
            )

          case 'speakers':
            return (
              <FadeUp key={`speakers-${index}`} delay={delay}>
                <SpeakersBlock block={block} />
              </FadeUp>
            )

          case 'location':
            return (
              <FadeUp key={`location-${index}`} delay={delay}>
                <LocationBlock block={block} />
              </FadeUp>
            )

          case 'actions':
            return (
              <FadeUp key={`actions-${index}`} delay={delay}>
                <ActionsBlock block={block} />
              </FadeUp>
            )

          default:
            return null
        }
      })}
    </div>
  )
}
