import { GROUPS } from "@/backend/constants/groups";
import { GlobalConfig } from "payload";

export const AboutUsPageBanner: GlobalConfig = {
    slug: "about-us-page-banner",
    admin: {
        group: GROUPS.ABOUT_US,
    },
    label: "Banner",

    fields: [
        {
            name: 'banner',
            type: "upload",
            relationTo: "media",
            required: true,
            label: "Banner",
            admin: {
                description: "Banner que se mostrará en la parte superior de la página de Sobre Nosotros",
            },
            filterOptions: {
                mimeType: {
                    contains: "image",
                }
            }
        },
        {
            name: 'title',
            type: "text",
            required: true,
            label: "Título",
            admin: {
                description: "Título que se mostrará en la parte superior de la página de Sobre Nosotros",
                placeholder: "Sobre Nosotros",
            },
        },
        {
            name: 'description',
            type: "textarea",
            required: true,
            label: "Descripción",
            admin: {
                description: "Descripción que se mostrará en la parte superior de la página de Sobre Nosotros",
                placeholder: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed non risus.",
            },
        }

    ],
}