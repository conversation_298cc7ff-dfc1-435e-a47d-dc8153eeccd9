'use client'

import type { HomeAboutUs } from '@/payload-types'
import { motion } from 'motion/react'

interface AboutUsGalleryProps {
  gallery: HomeAboutUs['gallery']
}

export default function AboutUsGallery({ gallery }: AboutUsGalleryProps) {
  if (!gallery || gallery.length === 0) {
    return null
  }

  // Create a gallery grid layout based on the number of images
  const renderGalleryLayout = () => {
    if (gallery.length === 1) {
      return (
        <div className="w-full h-full grid place-content-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="w-full h-full rounded-lg overflow-hidden aspect-video"
          >
            <img
              src={
                typeof gallery[0].image === 'object' && gallery[0].image?.url
                  ? gallery[0].image.url
                  : ''
              }
              alt="Sobre nosotros"
              className="w-full h-full object-cover"
            />
          </motion.div>
        </div>
      )
    }

    if (gallery.length === 2) {
      return (
        <div className="grid sm:grid-cols-2 gap-4">
          {gallery.map((item, index) => (
            <motion.div
              key={item.id || index}
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="rounded-lg overflow-hidden aspect-[3/4]"
            >
              <img
                src={typeof item.image === 'object' && item.image?.url ? item.image.url : ''}
                alt={`Sobre nosotros ${index + 1}`}
                className="w-full h-full object-cover"
              />
            </motion.div>
          ))}
        </div>
      )
    }

    // For 3 images, create a layout with first image taking 2 rows and 1 column
    return (
      <div className="grid sm:grid-cols-2 grid-rows-2 gap-2 md:h-[500px] place-items-center w-full">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="row-span-2 rounded-lg overflow-hidden h-full w-full aspect-square"
        >
          <img
            src={
              typeof gallery[0].image === 'object' && gallery[0].image?.url
                ? gallery[0].image.url
                : ''
            }
            alt="Sobre nosotros 1"
            // vertical
            className="w-full h-full object-cover aspect-[3/4]"
          />
        </motion.div>
        {gallery.slice(1, 3).map((item, index) => (
          <motion.div
            key={item.id || index}
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: (index + 1) * 0.1 }}
            viewport={{ once: true }}
            className="rounded-lg overflow-hidden h-full w-full aspect-video"
          >
            <img
              src={typeof item.image === 'object' && item.image?.url ? item.image.url : ''}
              alt={`Sobre nosotros ${index + 2}`}
              className="w-full h-full object-cover"
            />
          </motion.div>
        ))}
      </div>
    )
  }

  return <div className="w-full h-full">{renderGalleryLayout()}</div>
}
