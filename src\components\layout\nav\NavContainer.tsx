"use client"

import { cn } from "@/lib/utils";
import { useScroll } from "motion/react";
import { usePathname } from "next/navigation"
import { useState } from "react";


export default function NavContainer({
  children,
}: {
  children: React.ReactNode
}) {
  const pathName = usePathname()
  const { scrollY } = useScroll();
  const [isGradient, setIsGradient] = useState(true);

  scrollY.on("change", (y) => {
    if (pathName !== "/") return;

    // 800px (Carousel max height) - 200px (Nav height)
    // TODO: Make this dynamic
    if (y > 600) {
      setIsGradient(false);
    } else {
      setIsGradient(true);
    }
  });

  return (
    <nav
      className={cn("top-0 z-50 w-full", pathName === "/" ? "fixed" : "sticky")}
    >
      <div 
        className={cn("relative w-full h-full nav-container", pathName === "/" && isGradient && "gradient")}
        style={{
          background: "linear-gradient(to bottom, var(--primary) 0%, color-mix(in oklab, var(--primary) 0%, transparent) 100%)"
        }}
      >
        {children}
      </div>
    </nav>
  )
}