import { getPayloadClient } from '@/lib/payload'
import type { AcademicActivityTag } from '@/payload-types'
import type { FindArgs } from 'payload'

export interface GetTagsParams {
  page?: number
  limit?: number
  search?: string
  active?: boolean
  sort?: 'name-asc' | 'name-desc' | 'newest' | 'oldest'
}


/**
 * Servicio para gestionar etiquetas de actividades académicas
 */
export const AcademicActivityTagsService = {
  /**
   * Obtiene todas las etiquetas con filtros y paginación
   */
  async getTags(params: GetTagsParams = {}) {
    const {
      page = 1,
      limit = 50,
      search = '',
      active,
      sort = 'name-asc',
    } = params

    try {
      const payload = await getPayloadClient()

      const query: FindArgs = {
        collection: 'academic-activity-tags',
        where: {},
        page,
        limit,
      }

      // Filtro por búsqueda de texto
      if (search) {
        query.where = {
          or: [
            { name: { like: search } },
            { description: { like: search } },
          ],
        }
      }

      // Filtro por estado activo
      if (active !== undefined) {
        query.where = {
          ...query.where,
          active: { equals: active },
        }
      }

      // Configurar ordenamiento
      switch (sort) {
        case 'name-asc':
          query.sort = 'name'
          break
        case 'name-desc':
          query.sort = '-name'
          break
        case 'newest':
          query.sort = '-createdAt'
          break
        case 'oldest':
          query.sort = 'createdAt'
          break
        default:
          query.sort = 'name'
      }

      const response = await payload.find({
        ...query,
        depth: 1,
        locale: 'all',
      })

      return response
    } catch (error) {
      console.error('Error al obtener etiquetas:', error)
      return {
        docs: [],
        totalDocs: 0,
        limit,
        totalPages: 0,
        page,
        pagingCounter: 0,
        hasPrevPage: false,
        hasNextPage: false,
        prevPage: null,
        nextPage: null,
      }
    }
  },

  /**
   * Obtiene todas las etiquetas activas (para selects y filtros)
   */
  async getActiveTags() {
    try {
      const response = await this.getTags({
        active: true,
        limit: 100,
        sort: 'name-asc',
      })
      return response.docs
    } catch (error) {
      console.error('Error al obtener etiquetas activas:', error)
      return []
    }
  },

  /**
   * Obtiene una etiqueta por su ID
   */
  async getTagById(id: string): Promise<AcademicActivityTag | null> {
    try {
      const payload = await getPayloadClient()

      const response = await payload.find({
        collection: 'academic-activity-tags',
        where: {
          id: { equals: parseInt(id) },
        },
        depth: 1,
        limit: 1,
      })

      return response.docs.length > 0 ? (response.docs[0] as AcademicActivityTag) : null
    } catch (error) {
      console.error('Error al obtener etiqueta por ID:', error)
      return null
    }
  },

  /**
   * Obtiene etiquetas con el conteo de actividades asociadas
   */
  async getTagsWithCount() {
    try {
      const payload = await getPayloadClient()

      const tags = await this.getActiveTags()

      const tagsWithCount = await Promise.all(
        tags.map(async (tag) => {
          const activitiesResponse = await payload.find({
            collection: 'academic-activities',
            where: {
              tags: { in: [tag.id] },
              status: { equals: 'active' },
            },
            limit: 0, // Solo queremos el conteo
          })

          return {
            ...tag,
            activityCount: activitiesResponse.totalDocs,
          }
        })
      )

      return tagsWithCount
    } catch (error) {
      console.error('Error al obtener etiquetas con conteo:', error)
      return []
    }
  },

  /**
   * Obtiene las etiquetas más populares (con más actividades)
   */
  async getPopularTags(limit: number = 10) {
    try {
      const tagsWithCount = await this.getTagsWithCount()

      return tagsWithCount
        .filter(tag => tag.activityCount > 0)
        .sort((a, b) => b.activityCount - a.activityCount)
        .slice(0, limit)
    } catch (error) {
      console.error('Error al obtener etiquetas populares:', error)
      return []
    }
  },

  /**
   * Busca etiquetas por nombre (para autocompletado)
   */
  async searchTags(query: string, limit: number = 10) {
    try {
      const response = await this.getTags({
        search: query,
        active: true,
        limit,
        sort: 'name-asc',
      })
      return response.docs
    } catch (error) {
      console.error('Error al buscar etiquetas:', error)
      return []
    }
  },
}
