import { GROUPS } from "@/backend/constants/groups";
import { lexicalEditor } from "@payloadcms/richtext-lexical";
import { GlobalConfig } from "payload";

export const AboutUsPageHistoriaResumen: GlobalConfig = {
    slug: "about-us-page-historia-resumen",
    admin: {
        group: GROUPS.ABOUT_US,
    },
    label: "Historia (Resumen)",
    fields: [
        {
            name: "pre-title",
            type: "text",
            required: true,
            label: "Pre-título",
            admin: {
                description: "Pre-título que se mostrará en la página de Sobre Nosotros",
            },
        },
        {
            name: "title",
            type: "text",
            required: true,
            label: "Título",
            admin: {
                description: "Título que se mostrará en la página de Sobre Nosotros",
            },
        },
        {
            name: 'historia',
            type: "richText",
            editor: lexicalEditor(),
            required: true,
            label: "Historia",
            admin: {
                description: "Historia que se mostrará en la página de Sobre Nosotros",
            },
        },
    ],
}