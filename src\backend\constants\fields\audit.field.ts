import type { Field } from "payload"

type AuditFieldOptions = {
    visible?: boolean
}

export const auditFields = ({
    visible = false
}: AuditFieldOptions = {}) => {
    return [
        {
            label: '<PERSON><PERSON>o por',
            name: 'createdBy',
            type: 'relationship',
            relationTo: 'users',
            hidden: !visible,
            admin: {
                description: 'Usuario que creó el registro',
                ...(visible && { position: 'sidebar' }),
                readOnly: true,
                placeholder: "",
            },
            hooks: {
                beforeChange: [
                    ({ req, operation }) => {
                        if (operation === 'create' && req.user) {
                            return req.user.id
                        }
                        return undefined
                    },
                ],
            },
        },
    ] as Field[]
}