'use client'
import { AcademicActivity, Speaker } from '@/payload-types'
import { type MotionTab, MotionTabs } from '@/components/ui/motion/motion-tabs'
import {
  SpeakersTabContent,
  LocationTab,
  AgendaTab,
  RequirementsTab,
  ContentTab
} from './tabs'

interface AcademicActivityContentProps {
  activity: AcademicActivity
}

export default function AcademicActivityContent(
  { activity }: AcademicActivityContentProps
) {
  // Tab principal
  const mainTab: MotionTab = {
    title: "Información general",
    value: "informacion-general",
    content: (
      <ContentTab
        title="Información General"
        content={activity.content}
      />
    )
  }

  // Mapear eventBlocks a tabs en orden
  const blockTabMap: Record<string, (block: any) => MotionTab> = {
    location: (block) => ({
      title: block.blockName || "Ubicación",
      value: "ubicacion",
      content: <LocationTab title="Ubicación y Acceso" data={block} />
    }),
    agenda: (block) => ({
      title: block.blockName || "Agenda",
      value: "agenda",
      content: <AgendaTab data={block} />
    }),
    speakers: (block) => ({
      title: block.blockName || "Ponentes",
      value: "ponentes",
      content: <SpeakersTabContent title={block.title || "Ponentes"} speakers={block.speakers as Speaker[] || []} />
    }),
    requirements: (block) => ({
      title: block.blockName || "Requisitos",
      value: "requisitos",
      content: <RequirementsTab data={block} />
    })
  }

  // Generar tabs en orden de eventBlocks
  const blockTabs: MotionTab[] = (activity.eventBlocks || [])
    .map((block) => {
      if (blockTabMap[block.blockType]) {
        return blockTabMap[block.blockType](block)
      }
      return null
    })
    .filter(Boolean) as MotionTab[]

  const tabs: MotionTab[] = [mainTab, ...blockTabs]

  return (
    <div className="flex flex-col max-w-5xl mx-auto w-full items-start justify-start relative">
      <MotionTabs tabs={tabs} />
    </div>
  )
}