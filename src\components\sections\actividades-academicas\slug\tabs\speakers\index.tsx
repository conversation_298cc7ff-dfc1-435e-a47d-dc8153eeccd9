import { FadeUp } from "@/components/motion";
import { Text } from "@/components/ui/text";
import { Speaker } from "@/payload-types";
import SpeakersList from "./speakers-list";

export default function SpeakersTabContent({
    title, speakers
}: { title: string, speakers: Speaker[] }) {
    return (
        <section className="p-8">
            <FadeUp delay={0.1}>
                <div className="text-center mb-12">
                    <Text variant="h3" className="text-primary mb-3 font-light">
                        {title}
                    </Text>
                    <div className="w-16 h-0.5 bg-gradient-to-r from-primary to-secondary mx-auto"></div>
                </div>
            </FadeUp>

            <SpeakersList speakers={speakers} />
        </section>
    );
}
