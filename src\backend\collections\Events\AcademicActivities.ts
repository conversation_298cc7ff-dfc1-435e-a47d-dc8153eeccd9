import { GROUPS } from '@/backend/constants/groups'
import type { CollectionConfig } from 'payload'
import {
  LocationBlock,
  SpeakersBlock,
  AgendaBlock,
  ActionsBlock,
  RequirementsBlock,
} from './blocks'
import { generateUniqueSlugHook } from '../../hooks/generateSlug'
import { checkRole } from '@/backend/access/utils'
import { auditFields } from '@/backend/constants/fields/audit.field'

export const AcademicActivities: CollectionConfig = {
  slug: 'academic-activities',
  labels: {
    singular: 'Actividad Académica',
    plural: 'Actividades Académicas',
  },
  admin: {
    useAsTitle: 'title',
    group: GROUPS.EVENTS,
    description: 'Gestión de Actividades Académicas',
    defaultColumns: ['title', 'eventDate', 'modality', 'status', 'category', 'startDate', 'createdBy'],
    hideAPIURL: process.env.NODE_ENV === 'production',
  },
  access: {
    read: () => true,
    create: ({ req: { user } }) => checkRole(['admin', 'activities-coordinator'], user),
    update: ({ req: { user } }) => checkRole(['admin', 'activities-coordinator'], user),
    delete: ({ req: { user } }) => checkRole(['admin'], user),
  },
  fields: [
    // Sección de imágenes
    {
      type: 'collapsible',
      label: 'Imágenes',
      admin: {
        initCollapsed: false,
      },
      fields: [
        {
          name: 'mainImage',
          type: 'upload',
          relationTo: 'academic-activity-media',
          required: true,
          label: 'Imagen Principal',
          admin: {
            description: 'Imagen principal de la actividad',
          },
        },
      ],
    },
    // Layout con dos columnas
    {
      type: 'row',
      fields: [
        // Columna izquierda - Contenido principal
        {
          type: 'collapsible',
          label: 'Información Principal',
          admin: {
            initCollapsed: false,
            width: '60%',
          },
          fields: [
            {
              name: 'title',
              type: 'text',
              required: true,
              label: 'Título',
              admin: {
                description: 'Título de la actividad',
              },
            },
            {
              name: 'slug',
              type: 'text',
              label: 'Slug',
              unique: true,
              index: true,
              admin: {
                description: 'URL amigable generada automáticamente desde el título',
                hidden: true,
              },
              hooks: {
                beforeChange: [generateUniqueSlugHook],
              },
            },
            {
              name: 'excerpt',
              type: 'textarea',
              required: true,
              label: 'Resumen',
              admin: {
                description: 'Descripción breve de la actividad que aparecerá en las tarjetas',
              },
            },
            {
              name: 'content',
              type: 'richText',
              label: 'Contenido',
              admin: {
                description: 'Contenido detallado de la actividad',
              },
            },
          ],
        },
        // Columna derecha - Metadatos y configuración
        {
          type: 'collapsible',
          label: 'Configuración y Metadatos',
          admin: {
            initCollapsed: false,
            width: '40%',
          },
          fields: [
            {
              name: 'status',
              type: 'select',
              required: true,
              label: 'Estado',
              options: [
                { label: 'Activo', value: 'active' },
                { label: 'Inactivo', value: 'inactive' },
              ],
              defaultValue: 'active',
              admin: {
                description: 'Define si la actividad se muestra en la página',
              },
            },
            {
              type: "collapsible",
              label: "Fecha y Hora",
              admin: {
                initCollapsed: true,
              },
              fields: [
                {
                  type: "row",
                  fields: [
                    {
                      name: 'startDate',
                      type: 'date',
                      required: true,
                      label: 'Fecha y Hora de Inicio',
                      admin: {
                        description: 'Fecha y hora de inicio de la actividad',
                        width: '50%',
                        date: {
                          pickerAppearance: 'dayAndTime',
                          displayFormat: 'dd/MM/yyyy HH:mm',
                          timeFormat: 'HH:mm',
                          timeIntervals: 15,
                          minDate: new Date(),
                        },
                      },
                    },
                    {
                      name: 'endDate',
                      type: 'date',
                      label: 'Fecha y Hora de Fin',
                      admin: {
                        description: 'Fecha y hora de finalización de la actividad (opcional). Si no se especifica, se asume que es el mismo día.',
                        width: '50%',
                        date: {
                          pickerAppearance: 'dayAndTime',
                          displayFormat: 'dd/MM/yyyy HH:mm',
                          timeFormat: 'HH:mm',
                          timeIntervals: 15,
                          minDate: new Date(),
                        },
                      },
                      validate: (value: Date | null | undefined, { data }: { data: Record<string, unknown> }) => {
                        if (!value) return true // Campo opcional

                        const endDate = new Date(value)
                        const startDate = data.startDate ? new Date(data.startDate as Date) : null

                        if (startDate && endDate <= startDate) {
                          return 'La fecha y hora de fin debe ser posterior a la fecha y hora de inicio'
                        }

                        return true
                      },
                    },
                  ]
                }
              ]
            },
            {
              name: 'category',
              type: 'relationship',
              relationTo: 'academic-activity-categories',
              label: 'Categoría',
              admin: {
                description: 'Tipo de actividad',
              },
            },
            {
              name: 'tags',
              type: 'relationship',
              relationTo: 'academic-activity-tags',
              hasMany: true,
              label: 'Etiquetas',
              admin: {
                description: 'Etiquetas asociadas a la actividad',
              },
            },
          ],
        },
      ],
    },

    // Contenido flexible del actividad usando blocks
    {
      labels: {
        singular: 'Bloque de Actividad',
        plural: 'Bloques de Actividad',
      },
      name: 'eventBlocks',
      type: 'blocks',
      label: 'Contenido de la actividad',
      admin: {
        description: 'Agrega y organiza el contenido específico de la actividad usando bloques flexibles',
      },
      blocks: [
        LocationBlock,
        SpeakersBlock,
        AgendaBlock,
        ActionsBlock,
        RequirementsBlock,
      ],
    },
    ...auditFields()
  ],
}