import type { Block } from 'payload'

export const SpeakersBlock: Block = {
  slug: 'speakers',
  dbName: 'spkrsBlck',
  labels: {
    singular: 'Ponentes',
    plural: 'Ponentes',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      label: 'Título de la Sección',
      defaultValue: 'Ponentes',
      admin: {
        description: 'Título que aparecerá encima de la lista de ponentes',
      },
    },
    {
      name: 'speakers',
      type: 'relationship',
      relationTo: 'speakers',
      hasMany: true,
      label: 'Lista de Ponentes',
      admin: {
        description: 'Selecciona los ponentes que participarán en esta actividad',
        allowCreate: true,
        sortOptions: 'fullName',
      },
      filterOptions: () => {
        return {
          active: { equals: true }
        }
      },
    },
  ],
}
