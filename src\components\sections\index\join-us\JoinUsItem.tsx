'use client'

import DynamicIcon, { type IconName } from '@/backend/components/lucide/dynamic-icon'
import { Text } from '@/components/ui/text'
import { HomeJoinUs } from '@/payload-types'
import { motion } from 'motion/react'

interface JoinUsItemProps {
  item: NonNullable<HomeJoinUs['items']>[number]
}

export default function JoinUsItem({ item }: JoinUsItemProps) {
  return (
    <motion.div
      className="flex items-start gap-4 mb-6 group"
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      {/* Icono */}
      <div
        className="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-full bg-gray-50/20 transition-colors duration-300"
      >
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{
            duration: 0.5,
            delay: 0.1,
            type: "spring",
            stiffness: 200
          }}
        >
          <DynamicIcon
            name={(item.icon as IconName) || 'circle'}
            className="w-6 h-6 text-secondary group-hover:text-secondary/90 transition-colors duration-300"
          />
        </motion.div>
      </div>

      {/* Contenido */}
      <motion.div
        className="flex-1"
        initial={{ opacity: 0, x: -10 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Text
          variant='h3'
          className="text-lg font-semibold text-white mb-2 group-hover:text-secondary transition-colors duration-300"
        >
          {item.title}
        </Text>
        {item.description && (
          <Text
            variant='description'
            className="text-gray-300 text-sm leading-relaxed group-hover:text-gray-200 transition-colors duration-300 max-w-[400px]"
          >
            {item.description}
          </Text>
        )}
      </motion.div>
    </motion.div>
  )
}
