'use client'
import { FadeUp, StaggerFade } from "@/components/motion";
import { Text } from "@/components/ui/text";
import { Button } from "@/components/ui/button";
import {
  ExternalLinkIcon,
  ArrowRightIcon,
  UserPlusIcon
} from "lucide-react";
import DynamicIcon from "@/backend/components/lucide/dynamic-icon";

interface ActionButton {
  text: string;
  href: string; // Payload uses 'href' instead of 'url'
  variant: 'default' | 'secondary' | 'outline' | 'ghost' | 'destructive' | 'link' | 'white';
  size?: 'default' | 'sm' | 'lg';
  icon?: string | null;
  openInNewTab?: boolean | null;
}

interface ActionsBlockData {
  title?: string | null;
  description?: string | null;
  buttons?: ActionButton[] | null;
}

interface ActionsTabProps {
  data: ActionsBlockData;
}

export default function ActionsTab({ data }: ActionsTabProps) {
  const getButtonVariant = (variant: string) => {
    const variants = {
      primary: 'default',
      secondary: 'secondary',
      outline: 'outline',
      ghost: 'ghost'
    };
    return variants[variant as keyof typeof variants] || 'default';
  };

  return (
    <section className="p-8 space-y-6">
      <FadeUp delay={0.1}>
        <Text variant="h3" className="text-primary mb-6">
          {data.title || "Inscríbete al Evento"}
        </Text>
      </FadeUp>

      {data.description && (
        <FadeUp delay={0.2}>
          <Text className="text-gray-600 leading-relaxed mb-8">
            {data.description}
          </Text>
        </FadeUp>
      )}

      <StaggerFade className="space-y-4">
        {(data.buttons || []).map((button, index) => (
          <div key={index} className="flex justify-center">
            <Button
              asChild
              variant={getButtonVariant(button.variant) as any}
              size="lg"
              className="w-full sm:w-auto min-w-[200px] h-12"
            >
              <a
                href={button.href}
                target={button.openInNewTab ? "_blank" : "_self"}
                rel={button.openInNewTab ? "noopener noreferrer" : undefined}
                className="flex items-center justify-center gap-3"
              >
                {button.icon ? (
                  <DynamicIcon
                    name={button.icon as any}
                    className="w-5 h-5"
                  />
                ) : (
                  <UserPlusIcon className="w-5 h-5" />
                )}
                <span>{button.text}</span>
                {button.openInNewTab && (
                  <ExternalLinkIcon className="w-4 h-4" />
                )}
                {!button.openInNewTab && (
                  <ArrowRightIcon className="w-4 h-4" />
                )}
              </a>
            </Button>
          </div>
        ))}
      </StaggerFade>

      {/* Información adicional */}
      {(!data.buttons || data.buttons.length === 0) && (
        <FadeUp delay={0.2}>
          <div className="text-center py-8">
            <UserPlusIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <Text variant="h3" className="text-gray-500 mb-2">
              Información de registro próximamente
            </Text>
            <Text className="text-gray-400">
              Los detalles de inscripción se publicarán pronto.
            </Text>
          </div>
        </FadeUp>
      )}

      {/* Nota informativa */}
      {data.buttons?.length && (
        <FadeUp delay={0.4}>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-8">
            <Text className="text-blue-800 text-sm leading-relaxed">
              <strong>Nota:</strong> Al hacer clic en los enlaces de registro,
              serás redirigido a la plataforma correspondiente. Asegúrate de
              completar todos los pasos requeridos para confirmar tu participación.
            </Text>
          </div>
        </FadeUp>
      )}
    </section>
  );
}
