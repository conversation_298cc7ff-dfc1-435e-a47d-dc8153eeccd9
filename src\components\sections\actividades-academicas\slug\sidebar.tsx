import { AcademicActivity, AcademicActivityTag } from '@/payload-types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Text } from '@/components/ui/text'
import {
  Calendar,
  Clock,
  MapPin,
  Monitor,
  Users,
  Tag,
} from 'lucide-react'
import type { IconName } from '@/backend/components/lucide/dynamic-icon'
import { LinkButton } from '@/components/ui/link-button'
import { areSameDates, formatDate, formatTime } from '@/lib/date-utils'

interface AcademicActivitySidebarProps {
  activity: AcademicActivity
}

export default function AcademicActivitySidebar({ activity }: AcademicActivitySidebarProps) {
  const actionsBlock = activity.eventBlocks?.find(block => block.blockType === 'actions')

  const getLocationInfo = () => {
    const locationBlock = activity.eventBlocks?.find(block => block.blockType === 'location')
    if (!locationBlock || locationBlock.blockType !== 'location') {
      return { text: 'Por definir', icon: MapPin, color: 'text-gray-600' }
    }

    switch (locationBlock.modality) {
      case 'online':
        return { text: 'Virtual', icon: Monitor, color: 'text-blue-600' }
      case 'presencial':
        return {
          text: locationBlock.venue || locationBlock.address || 'Presencial',
          icon: MapPin,
          color: 'text-green-600'
        }
      case 'hybrid':
        return { text: 'Híbrido', icon: Users, color: 'text-purple-600' }
      default:
        return { text: 'Por definir', icon: MapPin, color: 'text-gray-600' }
    }
  }

  const getTags = () => {
    if (!activity.tags) return []
    return activity.tags as AcademicActivityTag[]
  }

  const locationInfo = getLocationInfo()
  const LocationIcon = locationInfo.icon
  const tags = getTags()

  const sameDayEvent = areSameDates(activity.startDate, activity.endDate)

  return (
    <div className="space-y-6">
      {/* Acciones */}
      {
        actionsBlock && (
          <Card className='border-none shadow-none'>
            {
              actionsBlock.title && (
                <CardHeader>
                  <CardTitle className="text-lg">{actionsBlock.title}</CardTitle>
                </CardHeader>
              )
            }
            <CardContent className="space-y-3">
              {actionsBlock.description && (
                <Text variant="base" className="text-sm text-gray-600 mb-4">
                  {actionsBlock.description}
                </Text>
              )}

              {
                actionsBlock.buttons?.length && (
                  <div className="flex flex-wrap gap-2">
                    {actionsBlock.buttons.map(({
                      id, href, text, icon, size, variant
                    }) => {
                      return (
                        <LinkButton
                          key={id}
                          href={href}
                          text={text}
                          icon={icon as IconName}
                          variant={{ variant, size }}
                        />
                      );
                    })}
                  </div>
                )
              }
            </CardContent>
          </Card>
        )
      }

      {/* Información Básica */}
      <Card className='border-none bg-gradient-to-r from-primary to-primary/90 p-4 rounded-lg'>
        <CardContent className="space-y-4">
          {sameDayEvent ? (
            <>
              <div className="flex items-start gap-3">
                <Calendar className="w-5 h-5 text-secondary mt-0.5" />
                <div>
                  <Text variant="base" className="font-medium text-secondary">Fecha</Text>
                  <Text variant="base" className="text-sm text-white">
                    {formatDate(activity.startDate)}
                  </Text>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <Clock className="w-5 h-5 text-secondary mt-0.5" />
                <div>
                  <Text variant="base" className="font-medium text-secondary">Horario</Text>
                  <Text variant="base" className="text-sm text-white first-letter:uppercase">
                    {formatTime(activity.startDate)}
                    {activity.endDate && ` - ${formatTime(activity.endDate)}`}
                  </Text>
                </div>
              </div>
            </>
          ) : (
            <>
              <div className="flex items-start gap-3">
                <Calendar className="w-5 h-5 text-secondary mt-0.5" />
                <div>
                  <Text variant="base" className="font-medium text-secondary">Inicio</Text>
                  <Text variant="base" className="text-sm text-white first-letter:uppercase">
                    {formatDate(activity.startDate)}  {formatTime(activity.startDate)}
                  </Text>
                </div>
              </div>
              {
                activity.endDate && (
                  <div className="flex items-start gap-3">
                    <Calendar className="w-5 h-5 text-secondary mt-0.5" />
                    <div>
                      <Text variant="base" className="font-medium text-secondary">Finalización</Text>
                      <Text variant="base" className="text-sm text-white first-letter:uppercase">
                        {formatDate(activity.endDate)}  {formatTime(activity.endDate)}
                      </Text>
                    </div>
                  </div>
                )
              }
            </>
          )}

          {/* Ubicación/Modalidad */}
          <div className="flex items-start gap-3">
            <LocationIcon className={"w-5 h-5 mt-0.5 text-secondary"} />
            <div>
              <Text variant="base" className="font-medium text-secondary">Modalidad</Text>
              <Text variant="base" className={"text-sm text-white"}>
                {locationInfo.text}
              </Text>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Etiquetas */}
      {
        tags.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Tag className="w-5 h-5" />
                Etiquetas
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {tags.map((tag) => (
                  <Badge key={tag.id} variant="outline" className="text-xs">
                    {tag.name}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )
      }
    </div >
  )
}
