import { WorkAreasService } from '@/services/work-areas.service'
import { Text } from '@/components/ui/text'
import WorkAreasGrid from './work-areas-grid'
import { Media } from '@/payload-types'

export default async function HomeWorkAreasSection() {
    const workAreasData = await WorkAreasService.getWorkAreasData()

    if (!workAreasData) {
        return (
            <div className="py-20 flex items-center justify-center bg-background">
                <div className="text-center">
                    <Text variant="h2" className="mb-4 text-5xl">
                        Áreas de Trabajo
                    </Text>
                    <Text variant="description">No hay información configurada para esta sección.</Text>
                </div>
            </div>
        )
    }

    // Obtener la URL de la imagen de fondo
    const backgroundImage = workAreasData.backgroundImage as Media

    return (
        <section className="relative py-20 overflow-hidden">
            {/* Imagen de fondo */}
            {
                backgroundImage && backgroundImage.url && (
                    <div
                        className="absolute inset-0 w-full h-full"
                        style={{
                            backgroundImage: `url(${backgroundImage.url})`,
                            backgroundSize: 'cover',
                            backgroundPosition: 'center',
                            backgroundRepeat: 'no-repeat',
                        }}
                    />
                )
            }

            {/* Overlay */}
            <div className="absolute inset-0 w-full h-full bg-primary mix-blend-color" />

            {/* Contenido */}
            <div className="relative z-10 container mx-auto px-4">
                <div className="text-center mb-16">
                    <Text
                        variant="h2"
                        className="text-white text-4xl lg:text-5xl font-bold mb-6"
                    >
                        {workAreasData.title}
                    </Text>
                    <Text
                        variant="description"
                        className="text-white/90 text-lg max-w-4xl mx-auto leading-relaxed"
                    >
                        {workAreasData.description}
                    </Text>
                </div>

                {/* Grid de áreas de trabajo */}
                <article className='py-8'>
                    {workAreasData.items && workAreasData.items.length > 0 && (
                        <WorkAreasGrid items={workAreasData.items} />
                    )}
                </article>
            </div>
        </section>
    )
}