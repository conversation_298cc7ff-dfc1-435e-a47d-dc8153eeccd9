'use client'
import { FadeUp } from "@/components/motion";
import { Text } from "@/components/ui/text";
import { FileTextIcon } from "lucide-react";
import React from 'react'
import { RichText } from '@payloadcms/richtext-lexical/react'
import { AcademicActivity } from "@/payload-types";
import "@/styles/rich-text.css";

interface ContentTabProps {
  title?: string;
  content?: AcademicActivity["content"];
}

export default function ContentTab({ title = "Información General", content }: ContentTabProps) {
  return (
    <section className="p-8 space-y-6">
      <FadeUp delay={0.1}>
        <Text variant="h3" className="text-primary mb-6">
          {title}
        </Text>
      </FadeUp>

      {content ? (
        <FadeUp delay={0.2}>
          <div className="prose prose-lg max-w-none [&>h1]:text-secondary rich-text-content">
            <RichText data={content} />
          </div>
        </FadeUp>
      ) : (
        <FadeUp delay={0.2}>
          <div className="text-center py-8">
            <FileTextIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <Text variant="h3" className="text-gray-500 mb-2">
              Contenido en preparación
            </Text>
            <Text className="text-gray-400">
              La información detallada se publicará próximamente.
            </Text>
          </div>
        </FadeUp>
      )}
    </section>
  );
}
