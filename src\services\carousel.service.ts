import { getPayloadClient } from '@/lib/payload'
import { HomeCarousel } from '@/payload-types'

export const CarouselService = {
  async getCarouselItems(): Promise<HomeCarousel[]> {
    try {
      const payload = await getPayloadClient()

      // Obtener los elementos del carousel desde la colección
      const carouselItems = await payload.find({
        collection: 'home-carousel',
        where: {
          active: {
            equals: true,
          },
        },
        sort: 'order',
        depth: 1, // Para cargar la relación con las imágenes
      })

      return carouselItems.docs
    } catch (error) {
      console.error('Error al obtener los elementos del carousel:', error)
      return []
    }
  },
}
