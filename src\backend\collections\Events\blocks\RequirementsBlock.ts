import type { Block } from 'payload'

export const RequirementsBlock: Block = {
  slug: 'requirements',
  labels: {
    singular: 'Información Adicional',
    plural: 'Información Adicional',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      label: 'Título de la Sección',
      defaultValue: 'Requisitos',
      admin: {
        description: 'Título que aparecerá encima de los requisitos',
      },
    },
    {
      name: 'content',
      type: 'richText',
      label: 'Contenido de la Sección',
      admin: {
        description: 'Requisitos, materiales u otra información importante para el evento',
      },
    },
  ],
}
