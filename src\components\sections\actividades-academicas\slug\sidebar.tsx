'use client'
import { AcademicActivity, AcademicActivityCategory, AcademicActivityTag } from '@/payload-types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Text } from '@/components/ui/text'
import {
  Calendar,
  Clock,
  MapPin,
  Monitor,
  Users,
  Share2,
  Download,
  Tag,
  Building
} from 'lucide-react'
import { format, isSameDay } from 'date-fns'
import { es } from 'date-fns/locale'

interface AcademicActivitySidebarProps {
  activity: AcademicActivity
}

export default function AcademicActivitySidebar({ activity }: AcademicActivitySidebarProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return format(date, "EEEE, d 'de' MMMM 'de' yyyy", { locale: es })
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return format(date, 'HH:mm', { locale: es })
  }

  const areSameDates = (date1: string, date2: string | null | undefined) => {
    if (!date2) return true
    return isSameDay(new Date(date1), new Date(date2))
  }

  const getLocationInfo = () => {
    const locationBlock = activity.eventBlocks?.find(block => block.blockType === 'location')
    if (!locationBlock || locationBlock.blockType !== 'location') {
      return { text: 'Por definir', icon: MapPin, color: 'text-gray-600' }
    }

    switch (locationBlock.modality) {
      case 'online':
        return { text: 'Virtual', icon: Monitor, color: 'text-blue-600' }
      case 'presencial':
        return {
          text: locationBlock.venue || locationBlock.address || 'Presencial',
          icon: MapPin,
          color: 'text-green-600'
        }
      case 'hybrid':
        return { text: 'Híbrido', icon: Users, color: 'text-purple-600' }
      default:
        return { text: 'Por definir', icon: MapPin, color: 'text-gray-600' }
    }
  }

  const getTags = () => {
    if (!activity.tags) return []
    return activity.tags as AcademicActivityTag[]
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: activity.title,
          text: activity.excerpt || '',
          url: window.location.href,
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      // Fallback: copiar URL al clipboard
      navigator.clipboard.writeText(window.location.href)
    }
  }

  const locationInfo = getLocationInfo()
  const LocationIcon = locationInfo.icon
  const tags = getTags()

  const sameDayEvent = areSameDates(activity.startDate, activity.endDate)

  return (
    <div className="space-y-6">
      {/* Información Básica */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Información del Evento</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {sameDayEvent ? (
            <>
              <div className="flex items-start gap-3">
                <Calendar className="w-5 h-5 text-primary mt-0.5" />
                <div>
                  <Text variant="base" className="font-medium">Fecha</Text>
                  <Text variant="base" className="text-sm text-muted-foreground">
                    {formatDate(activity.startDate)}
                  </Text>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <Clock className="w-5 h-5 text-primary mt-0.5" />
                <div>
                  <Text variant="base" className="font-medium">Horario</Text>
                  <Text variant="base" className="text-sm text-muted-foreground first-letter:uppercase">
                    {formatTime(activity.startDate)}
                    {activity.endDate && ` - ${formatTime(activity.endDate)}`}
                  </Text>
                </div>
              </div>
            </>
          ) : (
            <>
              <div className="flex items-start gap-3">
                <Calendar className="w-5 h-5 text-primary mt-0.5" />
                <div>
                  <Text variant="base" className="font-medium">Inicio</Text>
                  <Text variant="base" className="text-sm text-muted-foreground first-letter:uppercase">
                    {formatDate(activity.startDate)}  {formatTime(activity.startDate)}
                  </Text>
                </div>
              </div>
              {
                activity.endDate && (
                  <div className="flex items-start gap-3">
                    <Calendar className="w-5 h-5 text-primary mt-0.5" />
                    <div>
                      <Text variant="base" className="font-medium">Finalización</Text>
                      <Text variant="base" className="text-sm text-muted-foreground first-letter:uppercase">
                        {formatDate(activity.endDate)}  {formatTime(activity.endDate)}
                      </Text>
                    </div>
                  </div>
                )
              }
            </>
          )}



          {/* Ubicación/Modalidad */}
          <div className="flex items-start gap-3">
            <LocationIcon className={`w-5 h-5 mt-0.5 ${locationInfo.color}`} />
            <div>
              <Text variant="base" className="font-medium">Modalidad</Text>
              <Text variant="base" className={`text-sm ${locationInfo.color}`}>
                {locationInfo.text}
              </Text>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Etiquetas */}
      {
        tags.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Tag className="w-5 h-5" />
                Etiquetas
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {tags.map((tag) => (
                  <Badge key={tag.id} variant="outline" className="text-xs">
                    {tag.name}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )
      }

      {/* Acciones */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Acciones</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <Button onClick={handleShare} variant="outline" className="w-full justify-start">
            <Share2 className="w-4 h-4 mr-2" />
            Compartir Evento
          </Button>
        </CardContent>
      </Card>
    </div >
  )
}
