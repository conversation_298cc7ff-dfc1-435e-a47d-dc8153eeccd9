import type { Speaker } from "@/payload-types";

export const degreeOptions: { label: string, value: string }[] = [
    { label: 'Dr.', value: 'doctor' },
    { label: 'Dra.', value: 'doctora' },
    { label: 'Mg.', value: 'magister' },
    { label: 'Lic.', value: 'licenciado' },
    { label: 'Ing.', value: 'ingeniero' },
    { label: 'Prof.', value: 'profesor' },
    { label: '<PERSON>tro', value: 'other' },
] as const

export function speakerNameWithDegree(speaker: Speaker) {
    let degree = null

    if (speaker.degree === "other") {
        degree = speaker.customDegree
    } else {
        degree = degreeOptions.find(option => option.value === speaker.degree)?.label
    }

    return `${degree} ${speaker.fullName}`
}