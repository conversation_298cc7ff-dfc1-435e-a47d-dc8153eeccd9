'use client'
import { cn } from '@/lib/utils'
import { useField } from '@payloadcms/ui'
import type { SelectFieldClientComponent } from 'payload'
import React, { useState, useMemo, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { ChevronDown, Search } from 'lucide-react'
import DynamicIcon from '../lucide/dynamic-icon'
import dynamicIconImports from 'lucide-react/dynamicIconImports'
import { Input } from '@/components/ui/input'

const ALL_ICON_NAMES = Object.keys(dynamicIconImports) as Array<keyof typeof dynamicIconImports>

const POPULAR_ICONS = [
  'home', 'user', 'settings', 'search', 'heart', 'star', 'mail', 'phone',
  'calendar', 'clock', 'map-pin', 'camera', 'image', 'video', 'music',
  'download', 'upload', 'share', 'edit', 'trash', 'plus', 'minus',
  'check', 'x', 'chevron-left', 'chevron-right', 'chevron-up', 'chevron-down',
  'arrow-left', 'arrow-right', 'arrow-up', 'arrow-down', 'menu', 'grid',
  'list', 'filter', 'sort', 'refresh', 'lock', 'unlock', 'eye', 'eye-off',
  'bell', 'message-circle', 'send', 'bookmark', 'flag', 'tag', 'folder',
  'file', 'file-text', 'save', 'copy', 'cut', 'paste', 'undo', 'redo',
  'bold', 'italic', 'underline', 'link', 'code', 'terminal', 'database',
  'server', 'cloud', 'wifi', 'battery', 'volume', 'play', 'pause', 'stop'
] as Array<keyof typeof dynamicIconImports>

// Configuración de paginación
const INITIAL_LOAD_COUNT = 50
const LOAD_MORE_COUNT = 30

const IconSelectorField: SelectFieldClientComponent = ({ path, field }) => {
  const { value, setValue } = useField({ path })
  const [searchTerm, setSearchTerm] = useState('')
  const [loadedCount, setLoadedCount] = useState(INITIAL_LOAD_COUNT)
  const [isOpen, setIsOpen] = useState(false)

  // Función para filtrar íconos basado en búsqueda
  const getFilteredIcons = useCallback((search: string, limit: number) => {
    if (!search.trim()) {
      const popularFiltered = POPULAR_ICONS.filter(icon =>
        ALL_ICON_NAMES.includes(icon)
      ).slice(0, Math.min(limit, POPULAR_ICONS.length))

      if (limit > popularFiltered.length) {
        const remaining = limit - popularFiltered.length
        const otherIcons = ALL_ICON_NAMES
          .filter(icon => !POPULAR_ICONS.includes(icon))
          .slice(0, remaining)
        return [...popularFiltered, ...otherIcons]
      }

      return popularFiltered
    } else {
      return ALL_ICON_NAMES
        .filter(icon =>
          icon.toLowerCase().includes(search.toLowerCase())
        )
        .slice(0, limit)
    }
  }, [])

  const visibleIcons = useMemo(() => {
    return getFilteredIcons(searchTerm, loadedCount)
  }, [searchTerm, loadedCount, getFilteredIcons])

  const hasMoreIcons = useMemo(() => {
    const totalFiltered = searchTerm.trim()
      ? ALL_ICON_NAMES.filter(icon =>
        icon.toLowerCase().includes(searchTerm.toLowerCase())
      ).length
      : ALL_ICON_NAMES.length
    return loadedCount < totalFiltered
  }, [searchTerm, loadedCount])

  const handleIconSelect = (iconKey: string) => {
    setValue(iconKey)
    setIsOpen(false)
  }

  const handleLoadMore = () => {
    setLoadedCount(prev => prev + LOAD_MORE_COUNT)
  }

  const handleSearchChange = (search: string) => {
    setSearchTerm(search)
    setLoadedCount(INITIAL_LOAD_COUNT) // Reset al buscar
  }

  // Obtener el ícono seleccionado
  const selectedIcon = value ? ALL_ICON_NAMES.find(icon => icon === value) : null

  return (
    <div className="field-type">
      {/* Label */}
      {field?.label && (
        <label className="field-label">
          {typeof field.label === 'string' ? field.label : 'Ícono'}
        </label>
      )}

      {/* Selector Principal */}
      <div className="relative">
        <Button
          type="button"
          variant="outline"
          className={cn(
            "w-full justify-between h-12 px-3 z-0 border-none",
          )}
          onClick={() => setIsOpen(!isOpen)}
        >
          <div className="flex items-center gap-2">
            {selectedIcon ? (
              <>
                <DynamicIcon
                  name={selectedIcon}
                  className="text-primary size-4"
                />
                <span className="text-sm">{selectedIcon}</span>
              </>
            ) : (
              <span className="text-gray-500 text-sm">Seleccionar ícono...</span>
            )}
          </div>
          <div className="flex items-center gap-1">
            <ChevronDown
              size={16}
              className={cn(
                "transition-transform",
                isOpen && "rotate-180"
              )}
            />
          </div>
        </Button>

        {/* Dropdown Optimizado */}
        {isOpen && (
          <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-80 overflow-hidden">
            {/* Barra de búsqueda */}
            <div className="p-3 border-b border-gray-200">
              <div className="relative">
                <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Buscar íconos..."
                  value={searchTerm}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 text-sm rounded-md"
                />
              </div>
            </div>

            {/* Lista de íconos con scroll virtual */}
            <div className="max-h-60 overflow-y-auto">
              {visibleIcons.length > 0 ? (
                <>
                  <div className="grid grid-cols-4 gap-1 p-2">
                    {visibleIcons.map((iconName) => {
                      const isSelected = value === iconName

                      return (
                        <Button
                          key={iconName}
                          variant={"ghost"}
                          type="button"
                          className={cn(
                            'flex flex-col items-center justify-center p-2 rounded-md transition-all',
                            'min-h-[60px] gap-1 text-xs border-none',
                            isSelected
                              ? 'hover:bg-primary hover:text-white bg-primary border border-primary text-white'
                              : 'hover:bg-gray-50 text-gray-600'
                          )}
                          onClick={() => handleIconSelect(iconName)}
                          title={iconName}
                        >
                          <DynamicIcon
                            name={iconName}
                            className={cn(
                              'transition-colors size-5',
                              isSelected ? 'text-white' : 'text-gray-600'
                            )}
                          />
                          <span className={cn(
                            'text-xs text-center leading-tight truncate w-full',
                            isSelected ? 'font-medium' : 'text-gray-500'
                          )}>
                            {iconName}
                          </span>
                        </Button>
                      )
                    })}
                  </div>

                  {/* Botón Cargar Más */}
                  {hasMoreIcons && (
                    <div className="p-2 border-t border-gray-200">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={handleLoadMore}
                      >
                        Cargar más íconos ({loadedCount} de {searchTerm.trim()
                          ? ALL_ICON_NAMES.filter(icon =>
                            icon.toLowerCase().includes(searchTerm.toLowerCase())
                          ).length
                          : ALL_ICON_NAMES.length})
                      </Button>
                    </div>
                  )}
                </>
              ) : (
                <div className="p-4 text-center text-gray-500 text-sm">
                  No se encontraron íconos que coincidan con {`"${searchTerm}"`}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Description */}
      {field?.admin?.description && (
        <div className="field-description mt-2">
          {typeof field.admin.description === 'string' ? field.admin.description : ''}
        </div>
      )}

      {/* Overlay para cerrar el dropdown */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  )
}

export default IconSelectorField

