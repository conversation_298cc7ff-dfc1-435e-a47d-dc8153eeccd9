'use client'
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Text } from '@/components/ui/text'
import { Mic, Users, Coffee, BookOpen, Award, Clock, LucideIcon } from 'lucide-react'
import { AcademicActivity, Media, Speaker } from '@/payload-types'
import { speakerNameWithDegree } from '@/backend/collections/Events/Speakers/utils'


type AgendaBlock = Extract<NonNullable<NonNullable<AcademicActivity["eventBlocks"]>[number]>, { blockType: "agenda" }>

type AgendaTypeKeys = NonNullable<NonNullable<AgendaBlock["items"]>[number]["type"]>


const activityIcons: Record<AgendaTypeKeys, LucideIcon> = {
  conference: Mic,
  workshop: BookOpen,
  panel: Users,
  networking: Users,
  break: Coffee,
  lunch: Coffee,
  registration: Users,
  closing: Award,
  other: Clock,
} as const

const activityColors: Record<AgendaTypeKeys, string> = {
  conference: 'text-blue-500',
  workshop: 'text-purple-500',
  panel: 'text-green-500',
  networking: 'text-pink-500',
  break: 'text-orange-500',
  lunch: 'text-orange-500',
  registration: 'text-gray-500',
  closing: 'text-red-500',
  other: 'text-primary',
} as const

const activityBgColors: Record<AgendaTypeKeys, string> = {
  conference: 'bg-blue-50 border-blue-200',
  workshop: 'bg-purple-50 border-purple-200',
  panel: 'bg-green-50 border-green-200',
  networking: 'bg-pink-50 border-pink-200',
  break: 'bg-orange-50 border-orange-200',
  lunch: 'bg-orange-50 border-orange-200',
  registration: 'bg-gray-50 border-gray-200',
  closing: 'bg-red-50 border-red-200',
  other: 'bg-primary border-primary',
} as const

const activityLabels: Record<AgendaTypeKeys, string> = {
  conference: 'Conferencia',
  workshop: 'Taller',
  panel: 'Panel',
  networking: 'Networking',
  break: 'Descanso',
  lunch: 'Almuerzo',
  registration: 'Registro',
  closing: 'Clausura',
  other: '',
} as const

export default function AgendaBlock({ block }: { block: Extract<NonNullable<NonNullable<AcademicActivity["eventBlocks"]>[number]>, { blockType: "agenda" }>; }) {
  if (!block.items || block.items.length === 0) {
    return null
  }

  const getSpeakerInfo = (item: NonNullable<AgendaBlock["items"]>[number]) => {
    if (!item.speaker || typeof item.speaker !== 'object') return null
    return {
      ...item.speaker,
      photo: (item.speaker.photo as Media),
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="w-5 h-5 text-primary" />
          {block.title || 'Agenda de la Actividad'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {block.items.map((item, index) => {
            const activityType = item.type || "other"
            const Icon = activityIcons[activityType as keyof typeof activityIcons]
            const iconColor = activityColors[activityType as keyof typeof activityColors]
            const bgColor = activityBgColors[activityType as keyof typeof activityBgColors]

            return (
              <div
                key={index}
                className={`flex gap-4 p-4 border rounded-lg transition-all hover:shadow-sm ${bgColor}`}
              >
                {/* Hora */}
                <div className="flex-shrink-0 w-16 text-sm font-medium text-muted-foreground">
                  {item.time}
                </div>

                {/* Icono */}
                <div className="flex items-center gap-2 flex-shrink-0">
                  <div className="p-1.5 rounded-full bg-white">
                    <Icon className={`h-4 w-4 ${iconColor}`} />
                  </div>
                </div>

                {/* Contenido */}
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-2">
                    <Text variant="h4" className="text-base">
                      {item.title}
                    </Text>
                    <Badge variant="outline" className="text-xs capitalize ml-2">
                      {activityType === "other" ? item.customType : activityLabels[activityType]}
                    </Badge>
                  </div>

                  {item.description && (
                    <Text variant="base" className="text-sm text-muted-foreground mb-3">
                      {item.description}
                    </Text>
                  )}

                  {/* Speaker Info */}
                  {getSpeakerInfo(item) && (
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarImage
                          src={getSpeakerInfo(item)?.photo.url || '/placeholder-avatar.jpg'}
                          alt={getSpeakerInfo(item)?.photo.alt || getSpeakerInfo(item)?.fullName || ''}
                          loading='lazy'
                        />
                        <AvatarFallback className="text-xs">
                          {getSpeakerInfo(item)?.fullName
                            .split(' ')
                            .map((n: string) => n[0])
                            .join('')
                            .toUpperCase()
                            .slice(0, 2)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col">
                        <span className="text-sm font-medium">
                          {speakerNameWithDegree(getSpeakerInfo(item) as Speaker)}
                        </span>
                        {getSpeakerInfo(item)?.specialty && (
                          <span className="text-xs text-muted-foreground">
                            {getSpeakerInfo(item)?.specialty}
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
