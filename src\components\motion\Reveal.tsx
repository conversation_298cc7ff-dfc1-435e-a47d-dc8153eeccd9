'use client'

import { motion, MotionProps, Variants } from 'motion/react'
import { ReactNode } from 'react'

export type RevealDirection = 'left' | 'right' | 'top' | 'bottom' | 'center' | 'diagonal'
export type RevealType = 'clip' | 'mask' | 'curtain' | 'typewriter' | 'wave'

export interface RevealProps extends Omit<MotionProps, 'variants' | 'initial' | 'animate' | 'exit'> {
  children: ReactNode
  /** Tipo de revelado */
  type?: RevealType
  /** Dirección del revelado */
  direction?: RevealDirection
  /** Duración de la animación en segundos */
  duration?: number
  /** Retraso antes de iniciar la animación */
  delay?: number
  /** Función de easing personalizada */
  ease?: string | number[]
  /** Si la animación debe ejecutarse una sola vez */
  once?: boolean
  /** Umbral de visibilidad para activar la animación */
  threshold?: number
  /** Margen para el viewport */
  margin?: string
  /** Si debe animar al salir */
  animateExit?: boolean
  /** Color de la máscara/cortina */
  maskColor?: string
  /** Clase CSS adicional */
  className?: string
}

const createRevealVariants = (
  type: RevealType,
  direction: RevealDirection,
  duration: number,
  delay: number,
  ease: string | number[]
): Variants => {
  const baseTransition = { duration, delay, ease }

  switch (type) {
    case 'clip':
      const getClipPath = () => {
        switch (direction) {
          case 'left':
            return {
              hidden: 'inset(0 100% 0 0)',
              visible: 'inset(0 0% 0 0)'
            }
          case 'right':
            return {
              hidden: 'inset(0 0 0 100%)',
              visible: 'inset(0 0 0 0)'
            }
          case 'top':
            return {
              hidden: 'inset(0 0 100% 0)',
              visible: 'inset(0 0 0% 0)'
            }
          case 'bottom':
            return {
              hidden: 'inset(100% 0 0 0)',
              visible: 'inset(0% 0 0 0)'
            }
          case 'center':
            return {
              hidden: 'inset(50% 50% 50% 50%)',
              visible: 'inset(0% 0% 0% 0%)'
            }
          case 'diagonal':
            return {
              hidden: 'polygon(0 0, 0 0, 0 100%)',
              visible: 'polygon(0 0, 100% 0, 100% 100%, 0 100%)'
            }
          default:
            return {
              hidden: 'inset(0 100% 0 0)',
              visible: 'inset(0 0% 0 0)'
            }
        }
      }

      const clipPaths = getClipPath()
      return {
        hidden: { 
          clipPath: clipPaths.hidden,
          opacity: 1
        },
        visible: { 
          clipPath: clipPaths.visible,
          opacity: 1,
          transition: baseTransition
        },
        exit: { 
          clipPath: clipPaths.hidden,
          opacity: 1
        },
      }

    case 'mask':
      return {
        hidden: { 
          opacity: 0,
          scale: 0.8
        },
        visible: { 
          opacity: 1,
          scale: 1,
          transition: baseTransition
        },
        exit: { 
          opacity: 0,
          scale: 0.8
        },
      }

    case 'curtain':
      return {
        hidden: { 
          scaleY: 0,
          transformOrigin: direction === 'top' ? 'top' : 'bottom'
        },
        visible: { 
          scaleY: 1,
          transition: baseTransition
        },
        exit: { 
          scaleY: 0
        },
      }

    case 'typewriter':
      return {
        hidden: { 
          width: 0,
          opacity: 1
        },
        visible: { 
          width: 'auto',
          opacity: 1,
          transition: {
            ...baseTransition,
            ease: 'steps(20, end)'
          }
        },
        exit: { 
          width: 0,
          opacity: 1
        },
      }

    case 'wave':
      return {
        hidden: { 
          opacity: 0,
          y: 20,
          rotateX: -90
        },
        visible: { 
          opacity: 1,
          y: 0,
          rotateX: 0,
          transition: {
            ...baseTransition,
            ease: [0.25, 0.46, 0.45, 0.94]
          }
        },
        exit: { 
          opacity: 0,
          y: 20,
          rotateX: -90
        },
      }

    default:
      return {
        hidden: { opacity: 0 },
        visible: { opacity: 1, transition: baseTransition },
        exit: { opacity: 0 },
      }
  }
}

export const Reveal: React.FC<RevealProps> = ({
  children,
  type = 'clip',
  direction = 'left',
  duration = 0.8,
  delay = 0,
  ease = 'easeOut',
  once = true,
  threshold = 0.1,
  margin = '0px',
  animateExit = false,
  className,
  ...motionProps
}) => {
  const variants = createRevealVariants(
    type,
    direction,
    duration,
    delay,
    ease
  )

  return (
    <motion.div
      className={className}
      variants={variants}
      initial="hidden"
      whileInView="visible"
      exit={animateExit ? "exit" : undefined}
      viewport={{ 
        once, 
        amount: threshold,
        margin 
      }}
      {...motionProps}
    >
      {children}
    </motion.div>
  )
}

// Componentes predefinidos para tipos específicos
export const RevealClip: React.FC<Omit<RevealProps, 'type'>> = (props) => (
  <Reveal type="clip" {...props} />
)

export const RevealMask: React.FC<Omit<RevealProps, 'type'>> = (props) => (
  <Reveal type="mask" {...props} />
)

export const RevealCurtain: React.FC<Omit<RevealProps, 'type'>> = (props) => (
  <Reveal type="curtain" {...props} />
)

export const RevealTypewriter: React.FC<Omit<RevealProps, 'type'>> = (props) => (
  <Reveal type="typewriter" {...props} />
)

export const RevealWave: React.FC<Omit<RevealProps, 'type'>> = (props) => (
  <Reveal type="wave" {...props} />
)

// Componentes predefinidos para direcciones específicas
export const RevealFromLeft: React.FC<Omit<RevealProps, 'direction'>> = (props) => (
  <Reveal direction="left" {...props} />
)

export const RevealFromRight: React.FC<Omit<RevealProps, 'direction'>> = (props) => (
  <Reveal direction="right" {...props} />
)

export const RevealFromTop: React.FC<Omit<RevealProps, 'direction'>> = (props) => (
  <Reveal direction="top" {...props} />
)

export const RevealFromBottom: React.FC<Omit<RevealProps, 'direction'>> = (props) => (
  <Reveal direction="bottom" {...props} />
)

export const RevealFromCenter: React.FC<Omit<RevealProps, 'direction'>> = (props) => (
  <Reveal direction="center" {...props} />
)

export const RevealDiagonal: React.FC<Omit<RevealProps, 'direction'>> = (props) => (
  <Reveal direction="diagonal" {...props} />
)

// Variantes con configuraciones específicas
export const RevealSlow: React.FC<Omit<RevealProps, 'duration'>> = (props) => (
  <Reveal duration={1.5} {...props} />
)

export const RevealFast: React.FC<Omit<RevealProps, 'duration'>> = (props) => (
  <Reveal duration={0.4} {...props} />
)

export default Reveal
