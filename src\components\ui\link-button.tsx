'use client'

import { forwardRef } from 'react'
import { cn } from '@/lib/utils'
import { buttonVariants } from '@/components/ui/button'
import DynamicIcon, { IconName } from '@/backend/components/lucide/dynamic-icon'
import type { VariantProps } from 'class-variance-authority'

interface LinkButtonProps {
    href: string
    text: string
    icon?: IconName
    variant?: VariantProps<typeof buttonVariants>
    className?: string
}

const LinkButton = forwardRef<HTMLAnchorElement, LinkButtonProps>(
    ({ href, text, icon, variant, className }, ref) => {
        const classes = cn(
            buttonVariants(variant),
            "flex items-center justify-center gap-2",
            className
        )

        const iconElement = icon ? (
            <DynamicIcon
                name={icon as IconName}
                className="w-5 h-5"
            />
        ) : null

        return (
            <a
                href={href}
                target="_blank"
                rel="noopener noreferrer"
                className={classes}
                ref={ref}
            >
                {iconElement}
                <span>{text}</span>
            </a>
        )
    }
)

LinkButton.displayName = 'LinkButton'

export { LinkButton }