import { IconFields } from '@/backend/constants/fields/icon.field'
import type { Block } from 'payload'

export const RequirementsBlock: Block = {
  slug: 'requirements',
  labels: {
    singular: 'Requisitos',
    plural: 'Requisitos',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      label: 'Título de la Sección',
      defaultValue: 'Requisitos',
      admin: {
        description: 'Título que aparecerá encima de los requisitos',
      },
    },
    {
      name: 'items',
      type: 'array',
      label: 'Lista de Requisitos',
      admin: {
        description: 'Requisitos, materiales o información importante para el evento',
      },
      labels: {
        singular: 'Requisito',
        plural: 'Requisitos',
      },
      fields: [
        ...IconFields,
        {
          name: 'title',
          type: 'text',
          required: true,
          label: 'Título',
          admin: {
            description: 'Título del requisito',
          },
        },
        {
          name: 'description',
          type: 'textarea',
          label: 'Descripción',
          admin: {
            description: 'Descripción detallada del requisito',
          },
        },
        {
          name: 'required',
          type: 'checkbox',
          label: 'Obligatorio',
          defaultValue: true,
          admin: {
            description: 'Marca si este requisito es obligatorio',
          },
        },
      ],
    },
  ],
}
