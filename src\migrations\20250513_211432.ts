import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   CREATE TYPE "public"."enum_home_carousel_buttons_size" AS ENUM('default', 'sm', 'lg');
  CREATE TABLE IF NOT EXISTS "media" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"alt" varchar NOT NULL,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"url" varchar,
  	"thumbnail_u_r_l" varchar,
  	"filename" varchar,
  	"mime_type" varchar,
  	"filesize" numeric,
  	"width" numeric,
  	"height" numeric,
  	"focal_x" numeric,
  	"focal_y" numeric
  );
  
  ALTER TABLE "home_carousel_buttons" ALTER COLUMN "variant" SET DEFAULT 'default';
  ALTER TABLE "home_carousel_buttons" ADD COLUMN "size" "enum_home_carousel_buttons_size" DEFAULT 'default' NOT NULL;
  ALTER TABLE "payload_locked_documents_rels" ADD COLUMN "media_id" integer;
  CREATE INDEX IF NOT EXISTS "media_updated_at_idx" ON "media" USING btree ("updated_at");
  CREATE INDEX IF NOT EXISTS "media_created_at_idx" ON "media" USING btree ("created_at");
  CREATE UNIQUE INDEX IF NOT EXISTS "media_filename_idx" ON "media" USING btree ("filename");
  DO $$ BEGIN
   ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_media_fk" FOREIGN KEY ("media_id") REFERENCES "public"."media"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "payload_locked_documents_rels_media_id_idx" ON "payload_locked_documents_rels" USING btree ("media_id");
  ALTER TABLE "public"."home_carousel_buttons" ALTER COLUMN "variant" SET DATA TYPE text;
  DROP TYPE "public"."enum_home_carousel_buttons_variant";
  CREATE TYPE "public"."enum_home_carousel_buttons_variant" AS ENUM('white', 'secondary', 'default', 'link', 'destructive');
  ALTER TABLE "public"."home_carousel_buttons" ALTER COLUMN "variant" SET DATA TYPE "public"."enum_home_carousel_buttons_variant" USING "variant"::"public"."enum_home_carousel_buttons_variant";`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "media" DISABLE ROW LEVEL SECURITY;
  DROP TABLE "media" CASCADE;
  ALTER TABLE "payload_locked_documents_rels" DROP CONSTRAINT "payload_locked_documents_rels_media_fk";
  
  DROP INDEX IF EXISTS "payload_locked_documents_rels_media_id_idx";
  ALTER TABLE "home_carousel_buttons" ALTER COLUMN "variant" SET DEFAULT 'white';
  ALTER TABLE "home_carousel_buttons" DROP COLUMN IF EXISTS "size";
  ALTER TABLE "payload_locked_documents_rels" DROP COLUMN IF EXISTS "media_id";
  ALTER TABLE "public"."home_carousel_buttons" ALTER COLUMN "variant" SET DATA TYPE text;
  DROP TYPE "public"."enum_home_carousel_buttons_variant";
  CREATE TYPE "public"."enum_home_carousel_buttons_variant" AS ENUM('white', 'secondary', 'primary', 'link');
  ALTER TABLE "public"."home_carousel_buttons" ALTER COLUMN "variant" SET DATA TYPE "public"."enum_home_carousel_buttons_variant" USING "variant"::"public"."enum_home_carousel_buttons_variant";
  DROP TYPE "public"."enum_home_carousel_buttons_size";`)
}
