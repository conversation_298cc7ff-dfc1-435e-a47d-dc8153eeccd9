'use client'
import { AcademicActivity, Speaker } from '@/payload-types'
import { FadeUp } from '@/components/motion'
import { type MotionTab, MotionTabs } from '@/components/ui/motion/motion-tabs'
import { title } from 'process'
import SpeakersTab from './tabs/speakers/speakers-list'
import SpeakersTabContent from './tabs/speakers'

interface AcademicActivityContentProps {
  activity: AcademicActivity
}

export default function AcademicActivityContent(
  { activity }: AcademicActivityContentProps
) {
  const agendaBlock = activity.eventBlocks?.find(block => block.blockType === 'agenda')
  const speakersBlock = activity.eventBlocks?.find(block => block.blockType === 'speakers')
  const requirementsBlock = activity.eventBlocks?.find(block => block.blockType === 'requirements')
  const locationBlock = activity.eventBlocks?.find(block => block.blockType === 'location')

  const speakers = speakersBlock?.speakers as Speaker[] || []

  const tabs: MotionTab[] = [
    {
      title: "Información general",
      value: "informacion-general",
      content: (
        <section className='p-8'>
          Contenido
        </section>
      )
    },

    ...(locationBlock ? [{
      title: locationBlock.blockName || "Ubicación",
      value: "ubicacion",
      content: (<div>
        {JSON.stringify(locationBlock)}
      </div>)
    }] : []),

    ...(agendaBlock ? [{
      title: agendaBlock.blockName || "Agenda",
      value: "agenda",
      content: (<div>
        {JSON.stringify(agendaBlock.items)}
      </div>)
    }] : []),

    ...(speakersBlock && speakers.length > 0 ? [{
      title: speakersBlock.blockName || "Ponentes",
      value: "ponentes",
      content: <SpeakersTabContent title={speakersBlock.title || "Ponentes"} speakers={speakers} />
    }] : []),

    ...(requirementsBlock ? [{
      title: requirementsBlock.blockName || "Requisitos",
      value: "requisitos",
      content: (<div>
        {JSON.stringify(requirementsBlock.items)}
      </div>)
    }] : [])
  ]

  return (
    <div className="flex flex-col max-w-5xl mx-auto w-full items-start justify-start relative">
      <MotionTabs tabs={tabs} />
    </div>
  )
}