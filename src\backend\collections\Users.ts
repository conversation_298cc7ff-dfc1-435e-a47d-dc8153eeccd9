import type { CollectionConfig } from 'payload'
import { admins } from '../access'
import { GROUPS } from '../constants/groups'
import { auditFields } from '../constants/fields/audit.field'

export const Users: CollectionConfig = {
  slug: 'users',
  labels: {
    singular: 'Usuario',
    plural: 'Usuarios',
  },
  admin: {
    useAsTitle: 'email',
    group: GROUPS.AUTH,
    description: 'Gestiona los usuarios de la aplicación',
    hideAPIURL: true,
  },
  access: {
    read: admins,
    create: admins,
    update: admins,
    delete: admins,
  },
  auth: {
    maxLoginAttempts: 5,
    lockTime: 2 * 60 * 1000, // 2 minutos
    forgotPassword: {
      expiration: 5 * 60 * 1000, //
    },
  },
  fields: [
    {
      label: 'Nombre',
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      label: 'Apellid<PERSON>',
      name: 'lastName',
      type: 'text',
      required: true,
    },
    {
      name: 'roles',
      type: 'select',
      required: true,
      hasMany: true,
      options: [
        { label: 'Administrador', value: 'admin' },
        { label: 'Editor de Contenido', value: 'content-editor' },
        { label: 'Gestor de Media', value: 'media-manager' },
        { label: 'Coordinador de Actividades', value: 'activities-coordinator' }
      ],
    },
    ...auditFields(),
  ],
}
