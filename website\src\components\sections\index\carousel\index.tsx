import { CarouselService } from "@/services/carousel.service";
import CarouselContainer from "./CarouselContainer";
import CarouselItemComponent from "./CarouselItem";
import CarouselBackground from "./CarouselBackground";

export default async function CarouselMain() {
  const items = await CarouselService.getCarouselItems();
  return (
    <CarouselBackground items={items}>
      <section className="h-screen max-h-200 overflow-hidden max-w-400 relative w-screen mx-auto">
        <CarouselContainer length={items.length} items={items}>
          {items.map((item, index) => (
            <CarouselItemComponent key={item.id} item={item} index={index} />
          ))}
        </CarouselContainer>
      </section>
    </CarouselBackground>
  )

}