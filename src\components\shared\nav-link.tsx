'use client'
import { motion } from 'motion/react'
import Link from 'next/link'
import { ReactNode } from 'react'

interface NavLinkProps {
  href: string
  children: ReactNode
  className?: string
  external?: boolean
}

export const NavLink: React.FC<NavLinkProps> = ({
  href,
  children,
  className = '',
  external = false
}) => {
  const linkProps = external
    ? {
      href,
      target: '_blank',
      rel: 'noopener noreferrer'
    }
    : { href }

  const LinkComponent = external ? 'a' : Link

  return (
    <LinkComponent
      {...linkProps}
      className={`group relative inline-block transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2 focus:ring-offset-primary ${className}`}
    >
      <span className="relative z-10 block py-1">
        {children}
      </span>

      {/* Línea animada de hover */}
      <motion.div
        className="absolute bottom-0 left-0 h-0.5 bg-secondary"
        initial={{ width: 0 }}
        whileHover={{ width: '100%' }}
        whileFocus={{ width: '100%' }}
        transition={{
          duration: 0.3,
          ease: 'easeInOut'
        }}
      />
    </LinkComponent>
  )
}

export default NavLink
