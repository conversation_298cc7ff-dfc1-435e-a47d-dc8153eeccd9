import { HomeAboutUsService } from '@/services/home-about-us.service'
import { Text } from '@/components/ui/text'
import AboutUsContent from './AboutUsContent'
import AboutUsGallery from './AboutUsGallery'
import AboutUsTitle from './AboutUsTitle'

export default async function HomeAboutUsSection() {
  const aboutUsData = await HomeAboutUsService.getHomeAboutUsData()

  if (!aboutUsData) {
    return (
      <div className="py-20 flex items-center justify-center bg-background">
        <div className="text-center">
          <Text variant="h2" className="mb-4 text-5xl">
            Sobre Nosotros
          </Text>
          <Text variant="description">No hay información configurada para esta sección.</Text>
        </div>
      </div>
    )
  }

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto flex flex-col">
        <AboutUsTitle data={aboutUsData} />
        <div className="flex flex-col lg:flex-row gap-10 items-start justify-around mt-10">
          <AboutUsContent data={aboutUsData} />
          <aside className='min-h-[500px] md:max-h-[500px] lg:w-1/2 my-8 md:mt-0 overflow-hidden mx-auto'>
            <AboutUsGallery gallery={aboutUsData.gallery} />
          </aside>
        </div>
      </div>
    </section>
  )
}
