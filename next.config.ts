import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
    webpack(config) {
        config.module.rules.push({
            test: /\.svg$/,
            oneOf: [
                // Para importaciones con ?react
                {
                    resourceQuery: /react/, // *.svg?react
                    use: [
                        {
                            loader: '@svgr/webpack',
                            options: {
                                typescript: true,
                                icon: true,
                            },
                        },
                    ],
                },]
        })
    }
}

export default nextConfig