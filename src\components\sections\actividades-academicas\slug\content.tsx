'use client'
import { AcademicActivity, Speaker } from '@/payload-types'
import { type MotionTab, MotionTabs } from '@/components/ui/motion/motion-tabs'
import {
  SpeakersTabContent,
  LocationTab,
  AgendaTab,
  RequirementsTab,
  ActionsTab,
  ContentTab
} from './tabs'

interface AcademicActivityContentProps {
  activity: AcademicActivity
}

export default function AcademicActivityContent(
  { activity }: AcademicActivityContentProps
) {
  const agendaBlock = activity.eventBlocks?.find(block => block.blockType === 'agenda')
  const speakersBlock = activity.eventBlocks?.find(block => block.blockType === 'speakers')
  const requirementsBlock = activity.eventBlocks?.find(block => block.blockType === 'requirements')
  const locationBlock = activity.eventBlocks?.find(block => block.blockType === 'location')
  const actionsBlock = activity.eventBlocks?.find(block => block.blockType === 'actions')

  const speakers = speakersBlock?.speakers as Speaker[] || []

  const tabs: MotionTab[] = [
    {
      title: "Información general",
      value: "informacion-general",
      content: (
        <ContentTab
          title="Información General"
          content={activity.content}
        />
      )
    },

    ...(locationBlock ? [{
      title: locationBlock.blockName || "Ubicación",
      value: "ubicacion",
      content: (
        <LocationTab
          title="Ubicación y Acceso"
          data={locationBlock}
        />
      )
    }] : []),

    ...(agendaBlock ? [{
      title: agendaBlock.blockName || "Agenda",
      value: "agenda",
      content: (
        <AgendaTab
          data={agendaBlock}
        />
      )
    }] : []),

    ...(speakersBlock && speakers.length > 0 ? [{
      title: speakersBlock.blockName || "Ponentes",
      value: "ponentes",
      content: <SpeakersTabContent title={speakersBlock.title || "Ponentes"} speakers={speakers} />
    }] : []),

    ...(actionsBlock ? [{
      title: actionsBlock.blockName || "Inscripción",
      value: "inscripcion",
      content: (
        <ActionsTab
          data={actionsBlock}
        />
      )
    }] : []),

    ...(requirementsBlock ? [{
      title: requirementsBlock.blockName || "Requisitos",
      value: "requisitos",
      content: (
        <RequirementsTab
          data={requirementsBlock}
        />
      )
    }] : [])
  ]

  return (
    <div className="flex flex-col max-w-5xl mx-auto w-full items-start justify-start relative">
      <MotionTabs tabs={tabs} />
    </div>
  )
}