import { GROUPS } from '@/backend/constants/groups'
import { validateImageUsage } from '@/backend/hooks/HomeCarouselMedia/before-delete'
import type { CollectionConfig } from 'payload'

export const HomeCarouselMedia: CollectionConfig = {
  labels: {
    singular: 'Imagen de Carrusel',
    plural: 'Imágenes de Carrusel',
  },
  admin: {
    useAsTitle: 'alt',
    group: GROUPS.HOME,
    description: 'Imágenes para el carousel de la página de inicio',
    hidden: true,
  },
  slug: 'home-carousel-media',
  access: {
    read: () => true,
  },
  hooks: {
    beforeDelete: [validateImageUsage],
  },
  fields: [
    {
      name: 'alt',
      type: 'text',
      required: true,
      label: 'Texto alternativo',
      admin: {
        description: 'Descripción de la imagen para accesibilidad',
      },
    },
  ],
  upload: {
    staticDir: 'media',
    imageSizes: [
      {
        name: 'thumbnail',
        width: 400,
        height: 300,
        position: 'centre',
      },
      {
        name: 'desktop',
        width: 1920,
        height: 1080,
        position: 'centre',
      },
    ],
    mimeTypes: ['image/png', 'image/jpeg', 'image/jpg', 'image/webp'],
  },
}
