export async function revalidateTag(tag: string) {
    try {
        // Llama a la API de revalidación de Next.js
        await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/tags/revalidate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'revalidate-secret': process.env.REVALIDATE_SECRET || '', // Token de seguridad
            },
            body: JSON.stringify({
                tag: tag, // Tag que deseas revalidar
            }),
        })
    } catch (error) {
        console.error('Error al llamar al webhook de revalidación:', error)
    }
}