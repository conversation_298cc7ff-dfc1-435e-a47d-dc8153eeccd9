'use client'

import { forwardRef } from 'react'
import { cn, isExternalLink } from '@/lib/utils'
import { buttonVariants } from '@/components/ui/button'
import DynamicIcon, { IconName } from '@/backend/components/lucide/dynamic-icon'
import type { VariantProps } from 'class-variance-authority'
import Link from 'next/link'

interface LinkButtonProps {
    href: string
    text: string
    icon?: IconName
    variant?: VariantProps<typeof buttonVariants>
    className?: string
}

const LinkButton = forwardRef<HTMLAnchorElement, LinkButtonProps>(
    ({ href, text, icon, variant, className }, ref) => {
        const external = isExternalLink(href)


        const classes = cn(
            buttonVariants(variant),
            "flex items-center justify-center gap-2",
            className
        )

        const iconElement = icon ? (
            <DynamicIcon
                name={icon as IconName}
                className="w-5 h-5"
            />
        ) : null

        const linkProps = external
            ? {
                href,
                target: '_blank',
                rel: 'noopener noreferrer'
            }
            : { href }

        const LinkComponent = external ? 'a' : Link

        return (
            <LinkComponent
                {...linkProps}
                className={classes}
                ref={ref}
            >
                {iconElement}
                <span>{text}</span>
            </LinkComponent>
        )
    }
)

LinkButton.displayName = 'LinkButton'

export { LinkButton }