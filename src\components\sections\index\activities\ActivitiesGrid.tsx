'use client'

import { AcademicActivity } from '@/payload-types'
import { motion } from 'motion/react'
import ActivityCard from './ActivityCard'

interface ActivitiesGridProps {
  activities: AcademicActivity[]
}

export default function ActivitiesGrid({ activities }: ActivitiesGridProps) {
  // Configuración del grid para patrón 2x2: [ancho, delgado] [delgado, ancho]
  const getGridClass = (index: number) => {
    switch (index) {
      case 0:
        return 'col-span-6 lg:col-span-4' // Primera fila: ancho
      case 1:
        return 'col-span-6 lg:col-span-2' // Primera fila: delgado
      case 2:
        return 'col-span-6 lg:col-span-2' // Segunda fila: delgado
      case 3:
        return 'col-span-6 lg:col-span-4' // Segunda fila: ancho
      default:
        return 'col-span-6 lg:col-span-2'
    }
  }

  // Variantes de animación para el contenedor
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  }

  // Variantes para cada tarjeta
  const cardVariants = {
    hidden: {
      opacity: 0,
      y: 30,
      scale: 0.95
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  }

  // Preparar actividades (máximo 4)
  const displayActivities = activities.slice(0, 4)

  return (
    <motion.div
      className="grid grid-cols-6 gap-4 lg:gap-6 auto-rows-[500px] md:auto-rows-[500px] w-full mx-auto"
      variants={containerVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.2 }}
    >
      {displayActivities.map((activity, index) => (
        <motion.div
          key={activity?.id || `placeholder-${index}`}
          className={`${getGridClass(index)} min-h-[250px]`}
          variants={cardVariants}
          whileHover={{
            scale: 1.02,
            transition: { duration: 0.3 }
          }}
        >
          {activity ? (
            <ActivityCard
              activity={activity}
              isLarge={index === 0 || index === 3}
              className="h-full"
            />
          ) : (
            <div className="flex items-center justify-center h-full bg-gray-100 rounded-xl">
              <p className="text-gray-500 font-medium">Próximamente</p>
            </div>
          )}
        </motion.div>
      ))}
    </motion.div>
  )
}
