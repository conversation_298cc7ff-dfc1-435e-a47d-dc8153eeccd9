import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { AcademicActivitiesService } from '@/services/academic-activities/academic-activities.service'
import AcademicActivityDetailPage from '@/components/sections/actividades-academicas/slug'
import type { AcademicActivityCategory, AcademicActivityMedia, AcademicActivityTag } from '@/payload-types'

interface AcademicActivityPageProps {
    params: Promise<{
        slug: string
    }>
}

// Generación estática de parámetros
export async function generateStaticParams() {
    try {
        const slugs = await AcademicActivitiesService.getAllAcademicActivitySlugs()
        return slugs.map((slug) => ({
            slug,
        }))
    } catch (error) {
        console.error('Error generating static params:', error)
        return []
    }
}

// Generación de metadatos dinámicos
export async function generateMetadata({ params }: AcademicActivityPageProps): Promise<Metadata> {
    const { slug } = await params
    const activity = await AcademicActivitiesService.getAcademicActivityBySlug(slug)

    if (!activity) {
        return {
            title: 'Actividad no encontrada | Academia Peruana de Doctores',
            description: 'La actividad académica que buscas no existe o ha sido removida.',
        }
    }

    const category = activity.category as AcademicActivityCategory
    const tags = activity.tags as AcademicActivityTag[]

    const title = `${activity.title} | Academia Peruana de Doctores`
    const description = activity.excerpt || `Participa en ${activity.title}. ${category?.name ? `Categoría: ${category.name}.` : ''} Organizado por la Academia Peruana de Doctores.`

    // Obtener imagen principal
    const mainImage = activity.mainImage as AcademicActivityMedia
    const imageUrl = mainImage?.url ? `${process.env.NEXT_PUBLIC_SERVER_URL}${mainImage.url}` : undefined

    // Formatear fechas para structured data
    const startDate = new Date(activity.startDate).toISOString()
    const endDate = activity.endDate ? new Date(activity.endDate).toISOString() : startDate

    return {
        title,
        description,
        keywords: [
            'actividad académica',
            'evento académico',
            'Academia Peruana de Doctores',
            'APD',
            category?.name,
            ...(tags?.map(tag => tag.name) || []),
        ].filter(Boolean),
        openGraph: {
            title,
            description,
            type: 'article',
            publishedTime: new Date(activity.createdAt).toISOString(),
            modifiedTime: new Date(activity.updatedAt).toISOString(),
            authors: ['Academia Peruana de Doctores'],
            tags: tags?.map(tag => tag.name) || [],
            images: imageUrl ? [
                {
                    url: imageUrl,
                    width: 1200,
                    height: 630,
                    alt: activity.title,
                }
            ] : [],
        },
        twitter: {
            card: 'summary_large_image',
            title,
            description,
            images: imageUrl ? [imageUrl] : [],
        },
        alternates: {
            canonical: `${process.env.NEXT_PUBLIC_SERVER_URL}/actividades-academicas/${slug}`,
        },
        other: {
            // Structured Data para eventos
            'application/ld+json': JSON.stringify({
                '@context': 'https://schema.org',
                '@type': 'Event',
                name: activity.title,
                description: activity.excerpt,
                startDate,
                endDate,
                eventStatus: 'https://schema.org/EventScheduled',
                eventAttendanceMode: 'https://schema.org/MixedEventAttendanceMode', // Por defecto híbrido
                organizer: {
                    '@type': 'Organization',
                    name: 'Academia Peruana de Doctores',
                    url: process.env.NEXT_PUBLIC_SERVER_URL,
                },
                image: imageUrl,
                url: `${process.env.NEXT_PUBLIC_SERVER_URL}/actividades-academicas/${slug}`,
            }),
        },
    }
}

export default async function AcademicActivityPage({ params }: AcademicActivityPageProps) {
    const { slug } = await params
    const activity = await AcademicActivitiesService.getAcademicActivityBySlug(slug)

    if (!activity) {
        notFound()
    }

    // Obtener actividades relacionadas
    const category = activity.category as AcademicActivityCategory
    const tags = activity.tags as AcademicActivityTag[]

    const relatedActivities = await AcademicActivitiesService.getRelatedAcademicActivities(
        {
            currentActivityId: activity.id,
            categoryId: category?.id,
            tags: tags?.map(tag => tag.id.toString()),
            limit: 3
        }
    )

    return (
        <AcademicActivityDetailPage
            activity={activity}
            relatedActivities={relatedActivities}
        />
    )
}