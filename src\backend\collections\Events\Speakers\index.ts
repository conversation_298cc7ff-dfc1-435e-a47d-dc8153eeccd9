import type { CollectionConfig } from 'payload'
import { degreeOptions, speakerLinksOptions } from './utils'

export const Speakers: CollectionConfig = {
  slug: 'speakers',
  labels: {
    singular: 'Ponente',
    plural: 'Ponentes',
  },
  admin: {
    hidden: true, // Oculto en el dashboard principal
    useAsTitle: 'fullName',
    defaultColumns: ['fullName', 'specialty', 'degree'],
    group: 'Configuración',
  },
  access: {
    read: () => true,
    create: ({ req: { user } }) => <PERSON><PERSON><PERSON>(user),
    update: ({ req: { user } }) => <PERSON><PERSON>an(user),
    delete: ({ req: { user } }) => <PERSON><PERSON><PERSON>(user),
  },
  fields: [
    {
      name: 'fullName',
      type: 'text',
      required: true,
      label: 'Nombre Completo',
      admin: {
        description: 'Nombre completo del ponente',
      },
    },
    {
      name: 'photo',
      type: 'upload',
      relationTo: 'media',
      label: 'Foto del Ponente',
      admin: {
        description: 'Foto circular pequeña del ponente (recomendado: 200x200px)',
      },
    },
    {
      name: 'degree',
      type: 'select',
      label: 'Grado Académico',
      options: degreeOptions,
      admin: {
        description: 'Grado académico del ponente',
      },
    },
    {
      name: 'customDegree',
      type: 'text',
      label: 'Grado Personalizado',
      admin: {
        description: 'Solo si seleccionaste "Otro" en grado académico',
        condition: (data) => data.degree === 'other',
      },
    },
    {
      name: 'specialty',
      type: 'text',
      label: 'Especialidad',
      admin: {
        description: 'Área de especialización o campo de expertise',
      },
    },
    {
      name: 'institution',
      type: 'text',
      label: 'Institución',
      admin: {
        description: 'Universidad, empresa o institución de afiliación',
      },
    },
    {
      name: 'position',
      type: 'text',
      label: 'Cargo/Posición',
      admin: {
        description: 'Cargo actual en su institución',
      },
    },
    {
      name: 'bio',
      type: 'richText',
      label: 'Biografía Breve',
      admin: {
        description: 'Breve descripción profesional (opcional)',
      },
    },
    {
      admin: {
        position: 'sidebar',
      },
      name: 'socialLinks',
      type: 'array',
      label: 'Enlaces Sociales',
      dbName: 'social',
      labels: {
        singular: 'Enlace',
        plural: 'Enlaces',
      },
      fields: [
        {
          name: 'platform',
          type: 'select',
          required: true,
          label: 'Plataforma',
          options: speakerLinksOptions,
        },
        {
          name: 'customPlatform',
          type: 'text',
          label: 'Plataforma Personalizada',
          admin: {
            description: 'Solo si seleccionaste "Otra" en plataforma',
            condition: (_, siblingData) => siblingData.platform === 'other',
          },
        },
        {
          name: 'url',
          type: 'text',
          required: true,
          label: 'URL',
          admin: {
            description: 'Enlace completo (incluir https:// para websites)',
          },
        },
      ],
    },
    {
      name: 'active',
      type: 'checkbox',
      label: 'Activo',
      defaultValue: true,
      admin: {
        description: 'Ponente disponible',
        position: 'sidebar',
      },
    },
  ],
  timestamps: false, // Usamos campos personalizados
}
