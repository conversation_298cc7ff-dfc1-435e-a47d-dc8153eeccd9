"use client"

import React, { useState } from "react"
import {
  Users,
  Home,
  Images,
  Info,
  Briefcase,
  UserPlus,
  FolderOpen,
  BookOpen,
  Eye,
  Target,
  GraduationCap,
  Tag,
  Grid3X3,
  Search,
  Settings,
  ChevronDown,
  ChevronRight,
  Phone,
  Globe,
  Share2,
} from "lucide-react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { useAuth, useConfig } from "@payloadcms/ui"
import Link from "next/link"

interface CMSDashboardItem {
  name: string
  icon: React.ElementType
  description: string
  slug: string
}

interface CMSDashboardSection {
  id: string
  title: string
  description: string
  icon: React.ElementType
  color: string
  items: CMSDashboardItem[]
}

const dashboardSections: CMSDashboardSection[] = [
  {
    id: "auth",
    title: "Autenticación y Autorización",
    description: "Gestiona usuarios y permisos del sistema",
    icon: Users,
    color: "from-blue-500 to-blue-600",
    items: [{ name: "Usuarios", icon: Users, description: "Administrar cuentas de usuario", slug: "users" }],
  },
  {
    id: "homepage",
    title: "Página de Inicio",
    description: "Configura el contenido principal de tu sitio web",
    icon: Home,
    color: "from-emerald-500 to-emerald-600",
    items: [
      { name: "Carrusel de Imágenes", icon: Images, slug: "", description: "Gestionar slider principal" },
      { name: "Sobre Nosotros", icon: Info, slug: "", description: "Sección de presentación" },
      { name: "Áreas de Trabajo", icon: Briefcase, slug: "", description: "Departamentos y servicios" },
      { name: "Únete a Nosotros", icon: UserPlus, slug: "", description: "Llamada a la acción" },
    ],
  },
  {
    id: "media",
    title: "Gestión de Media",
    description: "Administra archivos, imágenes y recursos multimedia",
    icon: FolderOpen,
    color: "from-purple-500 to-purple-600",
    items: [{ name: "Biblioteca de Media", icon: FolderOpen, slug: "", description: "Archivos y recursos" }],
  },
  {
    id: "about",
    title: "Página de Sobre Nosotros",
    description: "Edita la información institucional y corporativa",
    icon: BookOpen,
    color: "from-orange-500 to-orange-600",
    items: [
      { name: "Historias", icon: BookOpen, slug: "", description: "Relatos institucionales" },
      { name: "Banner Principal", icon: Images, slug: "", description: "Imagen de cabecera" },
      { name: "Historia Resumida", icon: Eye, slug: "", description: "Resumen corporativo" },
      { name: "Misión y Visión", icon: Target, slug: "", description: "Valores institucionales" },
    ],
  },
  {
    id: "academic",
    title: "Actividades Académicas",
    description: "Gestiona contenido educativo y actividades",
    icon: GraduationCap,
    color: "from-red-500 to-red-600",
    items: [
      { name: "Actividades Académicas", icon: GraduationCap, slug: "", description: "Eventos educativos" },
      { name: "Tipos de Actividad", icon: Grid3X3, slug: "", description: "Categorías disponibles" },
      { name: "Etiquetas", icon: Tag, slug: "", description: "Sistema de clasificación" },
    ],
  },
  {
    id: "global-config",
    title: "Configuración Global",
    description: "Gestiona información general de la empresa y configuraciones del sitio",
    icon: Settings,
    color: "from-indigo-500 to-indigo-600",
    items: [
      { name: "Información Corporativa", icon: Info, slug: "", description: "Datos de la empresa" },
      { name: "Enlaces Sociales", icon: Share2, slug: "", description: "Redes sociales" },
      { name: "Datos de Contacto", icon: Phone, slug: "", description: "Información de contacto" },
      { name: "SEO y Metadatos", icon: Search, slug: "", description: "Optimización web" },
      { name: "Configuración General", icon: Globe, slug: "", description: "Ajustes del sitio" },
    ],
  },
]

export default function CMSDashboardView() {
  const [searchTerm, setSearchTerm] = useState("")
  const [expandedSections, setExpandedSections] = useState<string[]>([])

  const toggleSection = (sectionId: string) => {
    setExpandedSections((prev) =>
      prev.includes(sectionId) ? prev.filter((id) => id !== sectionId) : [...prev, sectionId],
    )
  }

  const filteredSections = dashboardSections.filter(
    (section) =>
      section.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      section.items.some((item) => item.name.toLowerCase().includes(searchTerm.toLowerCase())),
  )

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Main Content */}
      <section className="max-w-7xl mx-auto p-8 space-y-4">
        <div className="mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Gestión de Contenido</h2>
          <p className="text-xl text-gray-600 max-w-2xl">
            Administra todas las secciones de tu sitio web desde un panel centralizado y elegante
          </p>
        </div>

        <div className="relative">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <Input
            placeholder="Buscar en el CMS..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-12 w-80 h-12 bg-white/50 backdrop-blur-sm border-gray-200 focus:border-blue-300 rounded-xl"
          />
        </div>

        {/* Dashboard Grid - Fixed Layout */}
        <div className="gap-8"
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(400px, 1fr))",
          }}
        >
          {filteredSections.map((section) => (
            <div key={section.id} className="w-full">
              <Collapsible open={expandedSections.includes(section.id)} onOpenChange={() => toggleSection(section.id)}>
                <Card className="group hover:shadow-2xl transition-all duration-500 bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:scale-[1.02] overflow-hidden w-full min-h-[130px]">
                  <CollapsibleTrigger asChild>
                    <div className="cursor-pointer w-full">
                      <CardHeader className="pb-6 relative">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-5">
                            <div
                              className={`w-16 h-16 bg-gradient-to-r ${section.color} rounded-2xl flex items-center justify-center shadow-xl group-hover:scale-110 transition-transform duration-300 flex-shrink-0`}
                            >
                              <section.icon className="w-8 h-8 text-white" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <CardTitle className="text-2xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors mb-2">
                                {section.title}
                              </CardTitle>
                              <CardDescription className="text-gray-600 text-base leading-relaxed">
                                {section.description}
                              </CardDescription>
                            </div>
                          </div>
                          <div className="flex items-center space-x-3 flex-shrink-0">
                            <Badge variant="outline" className="text-sm px-3 py-1 bg-white/50 whitespace-nowrap">
                              {section.items.length} {section.items.length === 1 ? "sección" : "secciones"}
                            </Badge>
                            <ChevronDown
                              className={`w-6 h-6 text-gray-400 group-hover:text-blue-600 transition-all duration-300 flex-shrink-0 ${expandedSections.includes(section.id) ? "rotate-180" : ""
                                }`}
                            />
                          </div>
                        </div>
                      </CardHeader>
                    </div>
                  </CollapsibleTrigger>

                  <CollapsibleContent className="overflow-hidden">
                    <div
                      style={{
                        height: expandedSections.includes(section.id) ? `${section.items.length * 88 + 32}px` : "0px",
                      }}
                      className="transition-all duration-500 ease-in-out"
                    >
                      <CardContent className="pt-0 pb-8">
                        <div className="space-y-3">
                          {section.items.map((item) => (
                            <Link
                              key={item.slug}
                              href={`/admin/${item.slug}`}
                              className="group/item flex items-center justify-between p-5 bg-white/60 rounded-2xl hover:bg-blue-50/80 hover:shadow-lg transition-all duration-300 cursor-pointer border border-transparent hover:border-blue-200/50 h-20"
                              style={{
                                textDecoration: "none"
                              }}
                            >
                              <div className="flex items-center space-x-4 flex-1 min-w-0">
                                <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-sm group-hover/item:shadow-md transition-all duration-200 group-hover/item:scale-110 flex-shrink-0">
                                  <item.icon className="w-6 h-6 text-gray-600 group-hover/item:text-blue-600 transition-colors" />
                                </div>
                                <div className="flex-1 min-w-0">
                                  <h4 className="text-lg font-semibold text-gray-900 group-hover/item:text-blue-700 transition-colors truncate">
                                    {item.name}
                                  </h4>
                                  <p className="text-sm text-gray-500 mt-1 truncate">{item.description}</p>
                                </div>
                              </div>
                              <div className="flex items-center space-x-3 flex-shrink-0">
                                <ChevronRight className="w-5 h-5 text-gray-400 group-hover/item:text-blue-600 group-hover/item:translate-x-1 transition-all" />
                              </div>
                            </Link>
                          ))}
                        </div>
                      </CardContent>
                    </div>
                  </CollapsibleContent>
                </Card>
              </Collapsible>
            </div>
          ))}
        </div>

        {filteredSections.length === 0 && (
          <div className="text-center py-20">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Search className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-2xl font-semibold text-gray-900 mb-3">No se encontraron resultados</h3>
            <p className="text-gray-600 text-lg">Intenta con otros términos de búsqueda</p>
          </div>
        )}
      </section>
    </main>
  )
}
