'use client'
import WorkAreaCard from './work-area-card'
import { WorkAreasGridProps } from './work-areas-types'

export default function WorkAreasGrid({ items }: WorkAreasGridProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
      {items.map((item, index) => (
        <WorkAreaCard
          key={item.id || index}
          item={item}
          index={index}
        />
      ))}
    </div>
  )
}
