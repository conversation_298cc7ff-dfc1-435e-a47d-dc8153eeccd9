import { AcademicActivity } from "@/payload-types";

export type AcademicActivitySelect = {
    [K in keyof AcademicActivity]?: true | Record<string, true>;
};

export const LIST_ACADEMIC_ACTIVITIES_SELECT: AcademicActivitySelect = {
    "slug": true,
    "title": true,
    "mainImage": true,
    "excerpt": true,
    "category": true,
    "startDate": true,
    "endDate": true,
    "tags": true,
    "id": true,
} as const