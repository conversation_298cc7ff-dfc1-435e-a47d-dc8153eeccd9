'use client'

import { CorouselConstanst } from '@/constants/animations/carousel'
import { wait } from '@/lib/utils'
import { HomeCarousel } from '@/payload-types'
import { animate } from 'motion'
import { motion } from 'motion/react'
import { useEffect, useState } from 'react'

export default function CarouselContainer({
  children,
  length,
  items,
}: {
  children: React.ReactNode
  length: number
  items: HomeCarousel[]
}) {
  const [index, setIndex] = useState(0)

  const animateItem = async (index: number) => {
    animate(`#carousel-item-${index} h2`, {
      opacity: 1,
      y: 0,
    })

    animate(
      `#carousel-item-${index} .carousel-item-content .carousel-item-content-description`,
      {
        opacity: 1,
        y: 0,
      },
      {
        delay: 0.3,
      },
    )

    const item = items[index]

    for (let i = 0; i < (item.buttons ?? []).length; i++) {
      animate(
        `#carousel-item-${index} .carousel-item-content .carousel-item-content-button-${i}`,
        {
          opacity: 1,
          y: 0,
        },
        {
          delay: 0.35 + i * 0.1,
        },
      )
    }
  }

  const resetItem = async (index: number) => {
    animate(`#carousel-item-${index} .carousel-item-content h2`, {
      opacity: 0,
      y: 100,
    })

    animate(
      `#carousel-item-${index} .carousel-item-content .carousel-item-content-description`,
      {
        opacity: 0,
        y: 100,
      },
      {
        delay: 0.2,
      },
    )

    const item = items[index]

    for (let i = 0; i < (item.buttons ?? []).length; i++) {
      animate(
        `#carousel-item-${index} .carousel-item-content .carousel-item-content-button-${i}`,
        {
          opacity: 0,
          y: 100,
        },
        {
          delay: 0.3 + i * 0.1,
        },
      )
    }

    await wait(300) // This value is albitary. Just to make a smooth transition
  }

  useEffect(() => {
    if (length === 0) return

    let isCancelled = false

    animate('#carousel-background', {
      backgroundImage: `url(${items && items.length > 0 && items[index] && items[index].image
        ? typeof items[index].image === 'object' && items[index].image?.url
          ? items[index].image.url
          : ''
        : '/placeholder-image.jpg'
        })`,
    })

    const animationTotal = async () => {
      const nextIndex = (index + 1) % length
      await wait(CorouselConstanst.CAROUSEL_ANIMATION_TIME)
      if (isCancelled) return
      await animateItem(index)
      if (isCancelled) return
      await wait(CorouselConstanst.CAROUSEL_STEP_TIME)
      if (isCancelled || index === nextIndex) return
      await resetItem(index)
      setIndex(nextIndex)
    }

    animationTotal()

    return () => {
      isCancelled = true
    }
  }, [index, items, length])

  return (
    <>
      <div
        className="h-full overflow-hidden relative flex transition-transform duration-1500 ease-in-out"
        style={{ width: `${length * 100}%`, transform: `translateX(-${(index / length) * 100}%)` }}
      >
        {children}
      </div>
      <div className="absolute bottom-0 w-full flex justify-center">
        <div className="flex gap-2 mb-5">
          {items.map((item, i) => (
            <motion.button
              key={item.id}
              variants={{
                initial: { width: 8 },
                active: { width: 24 },
              }}
              initial="initial"
              animate={index === i ? 'active' : 'initial'}
              className={`h-2 rounded-full cursor-pointer ${index === i ? 'bg-secondary' : 'bg-white/50'}`}
              onClick={() => {
                if (index === i) return
                resetItem(index)
                setIndex(i)
              }}
            ></motion.button>
          ))}
        </div>
      </div>
    </>
  )
}