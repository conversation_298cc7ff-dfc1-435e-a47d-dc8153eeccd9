'use client'
import { motion } from 'motion/react'
import Link from 'next/link'
import { ReactNode, useState } from 'react'

interface NavLinkProps {
  href: string
  children: ReactNode
  className?: string
  external?: boolean
  active?: boolean
}

export const NavLink: React.FC<NavLinkProps> = ({
  href,
  children,
  className = '',
  external = false,
  active = false
}) => {
  const [hover, setHover] = useState(false)

  const linkProps = external
    ? {
      href,
      target: '_blank',
      rel: 'noopener noreferrer'
    }
    : { href }

  const LinkComponent = external ? 'a' : Link

  return (
    <LinkComponent
      {...linkProps}
      className={`group relative inline-block transition-colors duration-200 ${className}`}
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
    >
      <span className="relative z-10 block py-1">
        {children}
      </span>

      {/* Línea animada de hover */}
      <motion.div
        className="absolute bottom-0 left-0 h-0.5 bg-secondary"
        initial={{ width: 0 }}
        animate={{ width: hover || active ? '100%' : 0 }}
        transition={{
          duration: 0.3,
          ease: 'easeInOut'
        }}
      />
    </LinkComponent>
  )
}

export default NavLink
