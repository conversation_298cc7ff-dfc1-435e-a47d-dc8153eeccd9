'use client'

import { HomeAboutUs } from '@/payload-types'
import { Text } from '@/components/ui/text'
import { motion } from 'motion/react'
import AboutUsHighlights from './AboutUsHighlights'
import { LinkButton } from '@/components/ui/link-button'
import type { IconName } from '@/backend/components/lucide/dynamic-icon'

interface AboutUsContentProps {
  data: HomeAboutUs
}

export default function AboutUsContent({ data }: AboutUsContentProps) {
  return (
    <div className="w-full lg:w-fit space-y-6 px-4">
      <div className="text-center lg:text-left">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <div className="flex flex-col items-center lg:items-start gap-4 mb-6">
            <img src="/logo.svg" alt="Logo" width={96} height={96} className="w-24 h-24" />
            <Text className="max-w-[500px]">{data.description}</Text>
          </div>
        </motion.div>

        {data.buttons && data.buttons.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            viewport={{ once: true }}
            className="mt-6"
          >
            {data.buttons.map(({ variant, size, icon, ...button }, index) => (
              <LinkButton
                {...button}
                variant={{ variant, size }}
                icon={icon as IconName}
                key={button.id || index}
                className="mr-4"
              />
            ))}
          </motion.div>
        )}
      </div>

      {data.highlights && data.highlights.length > 0 && (
        <AboutUsHighlights highlights={data.highlights} />
      )}
    </div>
  )
}
