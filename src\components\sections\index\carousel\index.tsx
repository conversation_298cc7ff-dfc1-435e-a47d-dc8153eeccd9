import { CarouselService } from '@/services/carousel.service'
import CarouselContainer from './CarouselContainer'
import CarouselItemComponent from './CarouselItem'
import CarouselBackground from './CarouselBackground'
import { Text } from '@/components/ui/text'

export default async function CarouselMain() {
  const items = await CarouselService.getCarouselItems()

  if (!items || items.length === 0) {
    return (
      <div className="h-screen max-h-200 flex items-center justify-center bg-primary">
        <div className="text-center [&>*]:text-white">
          <Text variant="h2" className="mb-4">
            Carousel de Inicio
          </Text>
          <Text variant="description">No hay elementos configurados en el carousel.</Text>
        </div>
      </div>
    )
  }

  return (
    <CarouselBackground items={items}>
      <section className="h-screen max-h-200 overflow-hidden max-w-400 relative w-screen mx-auto">
        <CarouselContainer length={items.length} items={items}>
          {items.map((item, index) => (
            <CarouselItemComponent key={item.id} item={item} index={index} />
          ))}
        </CarouselContainer>
      </section>
    </CarouselBackground>
  )
}
