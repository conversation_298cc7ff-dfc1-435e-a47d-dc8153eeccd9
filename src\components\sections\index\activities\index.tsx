import { AcademicActivitiesService } from '@/services/academic-activities/academic-activities.service'
import { Text } from '@/components/ui/text'
import ActivitiesGrid from './ActivitiesGrid'
import Link from 'next/link'

export default async function HomeActivitiesCarouselSection() {
    const { docs: upcomingActivities } = await AcademicActivitiesService.getAcademicActivities({
        sort: "newest",
        findArgs: {
            select: {
                "title": true,
                "slug": true,
                "mainImage": true,
                "excerpt": true,
                "category": true,
                "startDate": true,
                "endDate": true,
                "eventBlocks": {
                    "blockType": true,
                    "location": true,
                }
            }
        }
    })

    if (!upcomingActivities || upcomingActivities.length === 0) {
        return (
            <section className="py-16 lg:py-20 bg-background">
                <div className="container mx-auto px-4 lg:px-6">
                    <div className="text-center">
                        <Text variant="h2" className="mb-4">
                            Actividades Académicas
                        </Text>
                        <Text variant="description" className="mb-8">
                            No hay actividades académicas programadas en este momento.
                        </Text>
                    </div>
                </div>
            </section>
        )
    }

    return (
        <section className="py-16 lg:py-20 bg-background">
            <div className="container mx-auto px-4">
                {/* Header */}
                <div className="text-center mb-12">
                    <Text variant="h2" className="mb-4">
                        Actividades Académicas
                    </Text>
                    <Text variant="description" className="max-w-2xl mx-auto">
                        Mantente actualizado con los últimos avances científicos, publicaciones
                        y actividades destacadas de nuestra comunidad académica.
                    </Text>
                </div>
                {/* Grid de eventos */}
                <ActivitiesGrid activities={upcomingActivities} />

                {/* Botón ver todos */}
                <div className="text-center mt-12">
                    <Link
                        href="/actividades-academicas"
                        className="inline-flex items-center justify-center px-8 py-3 bg-primary text-white font-semibold rounded-lg hover:bg-primary/90 transition-colors duration-300"
                    >
                        Ver todas las actividades
                    </Link>
                </div>
            </div>
        </section>
    )
}