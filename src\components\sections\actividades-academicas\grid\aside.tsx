'use client'
import { useRouter, useSearchParams } from 'next/navigation'
import { Text } from '@/components/ui/text'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import type { AcademicActivity, AcademicActivityCategory, AcademicActivityTag, Speaker } from '@/payload-types'
import { format } from 'date-fns'
import { es } from 'date-fns/locale'
import Link from 'next/link'
import { Stagger } from '@/components/motion'
import { speakerNameWithDegree } from '@/backend/collections/Events/Speakers/utils'

interface CategoryWithCount extends AcademicActivityCategory {
    activityCount: number
}

interface AcademicActivitiesGridAsideProps {
    categories?: CategoryWithCount[]
    upcomingActivities?: AcademicActivity[]
    popularTags?: AcademicActivityTag[]
}

export default function AcademicActivitiesGridAside({
    categories = [],
    upcomingActivities = [],
    popularTags = []
}: AcademicActivitiesGridAsideProps) {
    const router = useRouter()
    const searchParams = useSearchParams()

    const handleCategoryClick = (categoryId: number) => {
        const params = new URLSearchParams(searchParams.toString())
        params.set('category', categoryId.toString())
        params.delete('page') // Reset page when filtering
        router.push(`?${params.toString()}`, { scroll: false })
    }

    const handleTagClick = (tagName: string) => {
        const params = new URLSearchParams(searchParams.toString())
        params.set('search', tagName)
        params.delete('page') // Reset page when filtering
        router.push(`?${params.toString()}`, { scroll: false })
    }

    const formatDate = (dateString: string) => {
        const date = new Date(dateString)
        return format(date, "d MMMM, yyyy", { locale: es })
    }

    const getFirstSpeaker = (activity: AcademicActivity) => {
        const speakerBlock = activity.eventBlocks?.find(block => block.blockType === 'speakers')
        if (!speakerBlock || speakerBlock.blockType !== 'speakers' || !speakerBlock.speakers) return null

        if (speakerBlock.speakers.length > 0) {
            return speakerBlock.speakers[0] as Speaker
        }
        return null
    }

    return (
        <aside className="w-full lg:w-80 space-y-2 lg:sticky lg:top-8 bg-[#F9F9FF] p-6 rounded-lg">
            {/* Categorías */}
            <Stagger>
                <Text variant="h4" className="text-primary mb-4">
                    Categorías
                </Text>
                {
                    categories.length ? (
                        <div className='[&>button]:hover:text-secondary'>
                            <Button
                                variant="ghost"
                                className="w-full justify-start text-left h-fit py-1 px-0 hover:bg-gray-50"
                                onClick={() => {
                                    const params = new URLSearchParams(searchParams.toString())
                                    params.delete('category')
                                    params.delete('page')
                                    router.push(`?${params.toString()}`, { scroll: false })
                                }}
                            >
                                <span className="text-sm">Todas</span>
                            </Button>
                            {categories.map((category) => (
                                <Button
                                    key={category.id}
                                    variant="ghost"
                                    className="w-full justify-between text-left h-fit py-1 px-0 hover:bg-gray-50"
                                    onClick={() => handleCategoryClick(category.id)}
                                >
                                    <span className="text-sm">{category.name}</span>
                                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                                        {category.activityCount}
                                    </span>
                                </Button>
                            ))}
                        </div>
                    ) : (
                        <div className="flex flex-col items-center justify-center">
                            <Text variant="base" className="text-center text-gray-500">
                                No hay categorías disponibles
                            </Text>
                        </div>
                    )
                }
            </Stagger>

            {/* Próximos eventos */}
            <Stagger childAnimation='slide' staggerDelay={0.1}>
                <Text variant="h4" className="text-primary mb-4">
                    Próximos eventos
                </Text>
                {
                    upcomingActivities.length ? (
                        <div className="space-y-4">
                            {upcomingActivities.map((activity) => {
                                return (
                                    <Link
                                        key={activity.id}
                                        href={`/actividades-academicas/${activity.slug}`}
                                        className="block group"
                                    >
                                        <div className="border-l-2 border-secondary pl-3 hover:border-primary transition-colors">
                                            <div className="flex items-start justify-between mb-1">
                                                <span className="text-xs text-gray-500 font-medium">
                                                    {formatDate(activity.startDate)}
                                                </span>
                                            </div>
                                            <Text variant="base" className="text-sm font-medium text-gray-900 group-hover:text-primary transition-colors line-clamp-2">
                                                {activity.title}
                                            </Text>
                                            {
                                                getFirstSpeaker(activity) && (
                                                    <Text variant="base" className="text-xs text-gray-600 mt-1">
                                                        Por {speakerNameWithDegree(getFirstSpeaker(activity) as Speaker)}
                                                    </Text>
                                                )
                                            }
                                        </div>
                                    </Link>
                                )
                            })}
                        </div>
                    ) : (
                        <div className="flex flex-col items-center justify-center">
                            <Text variant="base" className="text-center text-gray-500">
                                No hay eventos próximos disponibles
                            </Text>
                        </div>
                    )
                }
            </Stagger>

            {/* Etiquetas populares */}

            <Stagger childAnimation='slide' staggerDelay={0.2}>
                <Text variant="h4" className="text-primary mb-4">
                    Etiquetas populares
                </Text>
                {
                    popularTags.length ? (
                        <div className="flex flex-wrap gap-2">
                            {popularTags.map((tag) => (
                                <Badge
                                    key={tag.id}
                                    variant="secondary"
                                    className="cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors text-xs"
                                    onClick={() => handleTagClick(tag.name)}
                                >
                                    {tag.name}
                                </Badge>
                            ))}
                        </div>
                    ) : (
                        <div className="flex flex-col items-center justify-center">
                            <Text variant="base" className="text-center text-gray-500">
                                No hay etiquetas populares disponibles
                            </Text>
                        </div>
                    )
                }
            </Stagger>
        </aside>
    )
}