import { format, isSameDay } from 'date-fns'
import { es } from "date-fns/locale"

export const areSameDates = (date1: string, date2: string | null | undefined) => {
    if (!date2) return true
    return isSameDay(new Date(date1), new Date(date2))
}

export const formatDate = (dateString: string, dateFormat?: string) => {
    const date = new Date(dateString)
    const _dateFormat = dateFormat || "EEEE, d 'de' MMMM 'de' yyyy"
    return format(date, _dateFormat, { locale: es })
}

export const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return format(date, 'HH:mm', { locale: es })
}