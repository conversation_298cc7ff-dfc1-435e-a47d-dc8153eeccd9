// Fade Animations
export {
  Fade,
  FadeUp,
  FadeDown,
  FadeLeft,
  FadeRight,
  FadeIn,
  FadeUpSlow,
  FadeUpFast,
  FadeUpBounce,
  type FadeProps,
  type FadeDirection
} from './Fade'

// Stagger Animations
export {
  Stagger,
  StaggerFade,
  StaggerSlideUp,
  StaggerSlideLeft,
  StaggerScale,
  StaggerRotate,
  StaggerFast,
  StaggerSlow,
  StaggerBounce,
  type StaggerProps
} from './Stagger'

// Scale Animations
export {
  Scale,
  ScaleIn,
  ScaleOut,
  ScaleBounce,
  ScalePulse,
  ScaleGrow,
  ScaleShrink,
  ScaleInSlow,
  ScaleInFast,
  ScaleInBig,
  type ScaleProps,
  type ScaleType
} from './Scale'

// Slide Animations
export {
  Slide,
  SlideUp,
  SlideDown,
  SlideLeft,
  SlideRight,
  SlideUpLeft,
  SlideUpRight,
  SlideDownLeft,
  SlideDownRight,
  SlideUpSlow,
  SlideUpFast,
  SlideUpBounce,
  SlideUpFar,
  SlideInFromBottom,
  SlideInFromTop,
  SlideInFromLeft,
  SlideInFromRight,
  type SlideProps,
  type SlideDirection
} from './Slide'

// Rotate Animations
export {
  Rotate,
  RotateIn,
  RotateInCounterclockwise,
  FlipX,
  FlipY,
  FlipDiagonal,
  RotateInSlow,
  RotateInFast,
  RotateInBounce,
  RotateInFull,
  RotateContinuous,
  RotatePulse,
  type RotateProps,
  type RotateDirection
} from './Rotate'

// Reveal Animations
export {
  Reveal,
  RevealClip,
  RevealMask,
  RevealCurtain,
  RevealTypewriter,
  RevealWave,
  RevealFromLeft,
  RevealFromRight,
  RevealFromTop,
  RevealFromBottom,
  RevealFromCenter,
  RevealDiagonal,
  RevealSlow,
  RevealFast,
  type RevealProps,
  type RevealDirection,
  type RevealType
} from './Reveal'

// Utility types
export type AnimationDirection = 'up' | 'down' | 'left' | 'right'
export type AnimationEasing = 'easeIn' | 'easeOut' | 'easeInOut' | 'linear' | 'backIn' | 'backOut' | 'backInOut'

// Common animation configurations
export const ANIMATION_DURATIONS = {
  fast: 0.3,
  normal: 0.6,
  slow: 1.2,
  verySlow: 2.0
} as const

export const ANIMATION_DELAYS = {
  none: 0,
  short: 0.1,
  medium: 0.3,
  long: 0.6
} as const

export const ANIMATION_DISTANCES = {
  small: 20,
  medium: 50,
  large: 100,
  extraLarge: 200
} as const

export const ANIMATION_EASINGS = {
  easeOut: 'easeOut',
  easeIn: 'easeIn',
  easeInOut: 'easeInOut',
  linear: 'linear',
  bounce: [0.68, -0.55, 0.265, 1.55],
  elastic: [0.25, 0.46, 0.45, 0.94],
  back: 'backOut'
} as const
