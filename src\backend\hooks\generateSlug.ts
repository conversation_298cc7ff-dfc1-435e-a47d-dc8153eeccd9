import type { FieldHook } from 'payload'

/**
 * Genera un slug a partir de un texto
 * Convierte el texto a minúsculas, reemplaza espacios y caracteres especiales con guiones
 * y elimina caracteres no válidos para URLs
 */
export const generateSlugFromText = (text: string): string => {
  return text
    .toLowerCase() // Convertir a minúsculas
    .trim() // Eliminar espacios al inicio y final
    .normalize('NFD') // Normalizar caracteres Unicode
    .replace(/[\u0300-\u036f]/g, '') // Eliminar acentos y diacríticos
    .replace(/[^a-z0-9\s-]/g, '') // Eliminar caracteres especiales excepto espacios y guiones
    .replace(/\s+/g, '-') // Reemplazar espacios con guiones
    .replace(/-+/g, '-') // Reemplazar múltiples guiones con uno solo
    .replace(/^-|-$/g, '') // Eliminar guiones al inicio y final
}

export const generateUniqueSlugHook: FieldHook = async ({ data, operation, value, req, collection }) => {
  let baseSlug = value

  if (operation === 'create' || operation === 'update' || !value) {
    const title = data?.title
    if (title && typeof title === 'string') {
      baseSlug = generateSlugFromText(title)
    }
  }

  if (!baseSlug) return value

  let isUnique = false
  let uniqueSlug = baseSlug
  let counter = 0

  while (!isUnique) {
    const testSlug = counter === 0 ? uniqueSlug : `${uniqueSlug}-${counter}`
    const existingDoc = await req.payload.find({
      collection: collection!.slug,
      where: {
        and: [
          { slug: { equals: testSlug } },
          ...(data?.id ? [{ id: { not_equals: data.id } }] : [])
        ]
      },
      limit: 1
    })

    if (existingDoc.docs.length === 0) {
      uniqueSlug = testSlug
      isUnique = true
    } else {
      counter++
    }
  }

  return uniqueSlug
}
