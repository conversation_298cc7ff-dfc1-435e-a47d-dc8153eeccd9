import type { Block } from 'payload'

export const AgendaBlock: Block = {
  slug: 'agenda',
  labels: {
    singular: 'Agenda',
    plural: 'Agendas',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      label: 'Título de la Sección',
      defaultValue: 'Agenda de la actividad',
      admin: {
        description: 'Título de la sección de agenda',
      },
    },
    {
      name: 'items',
      type: 'array',
      label: 'Elementos de la Agenda',
      admin: {
        description: 'Cronograma detallado del evento',
      },
      labels: {
        singular: 'Elemento',
        plural: 'Elementos',
      },
      fields: [
        {
          name: 'time',
          type: 'text',
          required: true,
          label: 'Hora',
          admin: {
            description: 'Hora del elemento (ej: "09:00", "09:00 - 10:30")',
          },
        },
        {
          name: 'title',
          type: 'text',
          required: true,
          label: 'Título',
          admin: {
            description: 'Título de la actividad',
          },
        },
        {
          name: 'description',
          type: 'textarea',
          label: 'Descripción',
          admin: {
            description: 'Descripción detallada de la actividad',
          },
        },
        {
          name: 'speaker',
          type: 'relationship',
          relationTo: 'speakers',
          label: 'Ponente/Responsable',
          admin: {
            description: 'Selecciona el ponente a cargo de esta actividad',
            allowCreate: false,
            sortOptions: 'fullName',
          },
          filterOptions: () => {
            return {
              active: { equals: true }
            }
          },
        },
        {
          name: 'type',
          type: 'select',
          label: 'Tipo de Actividad',
          options: [
            { label: 'Conferencia', value: 'conference' },
            { label: 'Taller', value: 'workshop' },
            { label: 'Panel', value: 'panel' },
            { label: 'Networking', value: 'networking' },
            { label: 'Descanso', value: 'break' },
            { label: 'Almuerzo', value: 'lunch' },
            { label: 'Registro', value: 'registration' },
            { label: 'Clausura', value: 'closing' },
            { label: 'Otra', value: 'other' },
          ],
          admin: {
            description: 'Tipo de actividad para aplicar estilos específicos',
          },
        },
        {
          name: 'customType',
          type: "text",
          label: "Tipo de Actividad Personalizado",
          admin: {
            description: 'Solo si no encuentras el tipo de actividad en la lista anterior',
            condition: (_, siblingData) => siblingData.type === 'other',
          },
        }
      ],
    },
  ],
}
