import type { Field } from 'payload'

interface IconSelectorFieldProps {
  defaultValue?: string
  label?: string
  description?: string
  required?: boolean
}

export const IconFields = ({
  defaultValue, description, label, required = false
}: IconSelectorFieldProps = {}): Field[] => {
  return [{
    name: 'icon',
    type: 'text',
    label: label || 'Ícono',
    required: required,
    defaultValue: defaultValue,
    admin: {
      description: description || 'Selecciona un ícono de la lista disponible',
      components: {
        Field: '/backend/components/fields/IconSelectorField',
      },
    },
  }]
} 