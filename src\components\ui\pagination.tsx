'use client'
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface PaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  pageSize?: number
  className?: string
  showFirstLast?: boolean
  showPrevNext?: boolean
  maxVisiblePages?: number
}

export function Pagination({
  currentPage,
  totalPages,
  onPageChange,
  // pageSize = 10,
  className,
  showFirstLast = true,
  showPrevNext = true,
  maxVisiblePages = 5,
}: PaginationProps) {
  if (totalPages <= 1) return null

  const getVisiblePages = () => {
    const pages: (number | string)[] = []

    if (totalPages <= maxVisiblePages) {
      // Si hay pocas páginas, mostrar todas
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      // Lógica para páginas con ellipsis
      const halfVisible = Math.floor(maxVisiblePages / 2)
      let startPage = Math.max(1, currentPage - halfVisible)
      let endPage = Math.min(totalPages, currentPage + halfVisible)

      // Ajustar si estamos cerca del inicio
      if (currentPage <= halfVisible) {
        endPage = Math.min(totalPages, maxVisiblePages)
      }

      // Ajustar si estamos cerca del final
      if (currentPage > totalPages - halfVisible) {
        startPage = Math.max(1, totalPages - maxVisiblePages + 1)
      }

      // Agregar primera página y ellipsis si es necesario
      if (startPage > 1) {
        pages.push(1)
        if (startPage > 2) {
          pages.push('...')
        }
      }

      // Agregar páginas visibles
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i)
      }

      // Agregar ellipsis y última página si es necesario
      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          pages.push('...')
        }
        pages.push(totalPages)
      }
    }

    return pages
  }

  const visiblePages = getVisiblePages()

  return (
    <nav
      role="navigation"
      aria-label="Paginación"
      className={cn("flex items-center justify-center space-x-1", className)}
    >
      {/* Botón Primera página */}
      {showFirstLast && currentPage > 1 && (
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(1)}
          aria-label="Ir a la primera página"
        >
          Primera
        </Button>
      )}

      {/* Botón Anterior */}
      {showPrevNext && currentPage > 1 && (
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage - 1)}
          aria-label="Página anterior"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
      )}

      {/* Números de página */}
      {visiblePages.map((page, index) => {
        if (page === '...') {
          return (
            <span
              key={`ellipsis-${index}`}
              className="flex h-9 w-9 items-center justify-center"
              aria-hidden="true"
            >
              <MoreHorizontal className="h-4 w-4" />
            </span>
          )
        }

        const pageNumber = page as number
        const isCurrentPage = pageNumber === currentPage

        return (
          <Button
            key={pageNumber}
            variant={isCurrentPage ? "default" : "outline"}
            size="sm"
            onClick={() => onPageChange(pageNumber)}
            aria-label={`Ir a la página ${pageNumber}`}
            aria-current={isCurrentPage ? "page" : undefined}
            className={cn(
              "h-9 w-9",
              isCurrentPage && "pointer-events-none"
            )}
          >
            {pageNumber}
          </Button>
        )
      })}

      {/* Botón Siguiente */}
      {showPrevNext && currentPage < totalPages && (
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage + 1)}
          aria-label="Página siguiente"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      )}

      {/* Botón Última página */}
      {showFirstLast && currentPage < totalPages && (
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(totalPages)}
          aria-label="Ir a la última página"
        >
          Última
        </Button>
      )}
    </nav>
  )
}

// Componente de información de paginación
interface PaginationInfoProps {
  currentPage: number
  totalPages: number
  pageSize: number
  totalItems: number
  className?: string
}

export function PaginationInfo({
  currentPage,
  // totalPages,
  pageSize,
  totalItems,
  className,
}: PaginationInfoProps) {
  const startItem = (currentPage - 1) * pageSize + 1
  const endItem = Math.min(currentPage * pageSize, totalItems)

  return (
    <div className={cn("text-sm text-muted-foreground", className)}>
      Mostrando {startItem} a {endItem} de {totalItems} resultados
    </div>
  )
}

// Componente completo de paginación con información
interface PaginationWithInfoProps extends PaginationProps {
  totalItems: number
  showInfo?: boolean
}

export function PaginationWithInfo({
  totalItems,
  showInfo = true,
  ...paginationProps
}: PaginationWithInfoProps) {
  return (
    <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
      {showInfo && (
        <PaginationInfo
          currentPage={paginationProps.currentPage}
          totalPages={paginationProps.totalPages}
          pageSize={paginationProps.pageSize || 10}
          totalItems={totalItems}
        />
      )}
      <Pagination {...paginationProps} />
    </div>
  )
}

export default Pagination
