'use client'
import { useRouter } from 'next/navigation'
import { motion } from 'motion/react'
import { AcademicActivity } from '@/payload-types'
import { PaginationWithInfo } from '@/components/ui/pagination'
import AcademicActivitiesGridAside from './aside'
import ActivityCard from './activity-card'
import { ACTIVITIES_GRID_PAGE_SIZE } from '../constants'

interface AcademicActivitiesGridProps {
    initialData?: {
        activities: AcademicActivity[]
        totalPages: number
        currentPage: number
        totalItems: number
    }
    asideData?: {
        categories: any[]
        upcomingActivities: AcademicActivity[]
        popularTags: any[]
    }
}

export default function AcademicActivitiesGrid({ initialData, asideData }: AcademicActivitiesGridProps) {
    const router = useRouter()

    // Usar directamente los datos pasados desde el servidor
    const activities = initialData?.activities || []
    const totalPages = initialData?.totalPages || 0
    const currentPage = initialData?.currentPage || 1
    const totalItems = initialData?.totalItems || 0
    const pageSize = ACTIVITIES_GRID_PAGE_SIZE

    const handlePageChange = (page: number) => {
        const params = new URLSearchParams(window.location.search)
        params.set('page', page.toString())
        router.push(`?${params.toString()}`, {
            scroll: false,
        })
    }

    return (
        <section className="py-16">
            <div className="container mx-auto px-4">
                <div className="flex flex-col lg:flex-row gap-8">
                    {/* Grid principal */}
                    <div className="flex-1">
                        {activities.length > 0 ? (
                            <>
                                <motion.div
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ duration: 0.5 }}
                                    className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12"
                                >
                                    {activities.map((activity, index) => (
                                        <motion.div
                                            key={activity.id}
                                            initial={{ opacity: 0, y: 30 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{
                                                duration: 0.5,
                                                delay: index * 0.1,
                                                ease: "easeOut"
                                            }}
                                        >
                                            <ActivityCard activity={activity} />
                                        </motion.div>
                                    ))}
                                </motion.div>

                                {/* Paginación */}
                                {totalPages > 1 && (
                                    <motion.div
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.5, delay: 0.3 }}
                                        className="flex justify-center"
                                    >
                                        <div className="bg-white rounded-lg shadow-sm border p-4">
                                            <PaginationWithInfo
                                                currentPage={currentPage}
                                                totalPages={totalPages}
                                                totalItems={totalItems}
                                                pageSize={pageSize}
                                                onPageChange={handlePageChange}
                                                showInfo={true}
                                            />
                                        </div>
                                    </motion.div>
                                )}
                            </>
                        ) : (
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.5 }}
                                className="text-center"
                            >
                                <div className="p-12">
                                    <div className="text-gray-400 mb-4">
                                        <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                        No se encontraron actividades
                                    </h3>
                                    <p className="text-gray-600 mb-6">
                                        No hay actividades académicas que coincidan con los filtros seleccionados.
                                    </p>
                                </div>
                            </motion.div>
                        )}
                    </div>

                    {/* Aside */}
                    <AcademicActivitiesGridAside
                        categories={asideData?.categories}
                        upcomingActivities={asideData?.upcomingActivities}
                        popularTags={asideData?.popularTags}
                    />
                </div>
            </div>
        </section>
    )
}