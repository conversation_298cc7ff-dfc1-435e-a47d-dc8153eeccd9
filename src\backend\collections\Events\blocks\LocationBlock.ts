import type { Block } from 'payload'

export const LocationBlock: Block = {
  slug: 'location',
  labels: {
    singular: 'Ubicación',
    plural: 'Ubicaciones',
  },
  fields: [
    {
      name: 'modality',
      type: 'select',
      required: true,
      label: 'Modalidad',
      options: [
        { label: 'Online', value: 'online' },
        { label: 'Presencial', value: 'presencial' },
        { label: 'Híbrido', value: 'hybrid' },
      ],
      admin: {
        description: 'Modalidad del evento',
      },
    },
    {
      name: 'conferenceLink',
      type: 'text',
      label: 'Enlace de Conferencia',
      admin: {
        description: 'Enlace para eventos online (Zoom, Meet, etc.)',
        condition: (_, siblingData) =>
          siblingData.modality === 'online' ||
          siblingData.modality === 'hybrid',
      },
    },
    {
      name: 'platform',
      type: 'select',
      label: 'Plataforma',
      options: [
        { label: 'Zoom', value: 'zoom' },
        { label: 'Google Meet', value: 'meet' },
        { label: 'Microsoft Teams', value: 'teams' },
        { label: 'YouTube Live', value: 'youtube' },
        { label: 'Otra', value: 'other' },
      ],
      admin: {
        description: 'Plataforma para eventos online',
        condition: (_, siblingData) =>
          siblingData.modality === 'online' ||
          siblingData.modality === 'hybrid',
      },
    },
    {
      name: 'address',
      type: 'textarea',
      label: 'Dirección',
      admin: {
        description: 'Dirección física para eventos presenciales',
        condition: (_, siblingData) =>
          siblingData.modality === 'presencial' ||
          siblingData.modality === 'hybrid',
      },
    },
    {
      name: 'venue',
      type: 'text',
      label: 'Lugar/Sede',
      admin: {
        description: 'Nombre del lugar o sede del evento',
        condition: (data) => data.modality === 'presencial' || data.modality === 'hybrid',
      },
    },
    {
      name: 'instructions',
      type: 'textarea',
      label: 'Instrucciones de Acceso',
      admin: {
        description: 'Instrucciones especiales para acceder al evento',
      },
    },
  ],
}
