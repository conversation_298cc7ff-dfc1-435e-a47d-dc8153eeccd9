'use client'
import { AcademicActivity } from '@/payload-types'
import { type MotionTab, MotionTabs } from '@/components/ui/motion/motion-tabs'
import {
  SpeakersTabContent,
  LocationTab,
  AgendaTab,
  RequirementsTab,
  ContentTab
} from './tabs'
import type { AgendaBlock, LocationBlock, RequirementsBlock, SpeakersBlock, TabEventBlock } from './tabs/types'

interface AcademicActivityContentProps {
  activity: AcademicActivity
}

export default function AcademicActivityContent(
  { activity }: AcademicActivityContentProps
) {
  // Tab principal
  const mainTab: MotionTab = {
    title: "Información general",
    value: "informacion-general",
    content: (
      <ContentTab
        title="Información General"
        content={activity.content}
      />
    )
  }

  // Mapear eventBlocks a tabs en orden
  const blockTabMap: Record<string, (block: LocationBlock | AgendaBlock | SpeakersBlock | RequirementsBlock) => MotionTab | null> = {
    location: (block) => ({
      title: block.blockName || "Ubicación y Acceso",
      value: "ubicacion",
      content: <LocationTab title={block.blockName || "Ubicación y Acceso"} data={block as LocationBlock} />
    }),
    agenda: (block) => ({
      title: block.blockName || "Agenda",
      value: "agenda",
      content: <AgendaTab data={block as AgendaBlock} />
    }),
    speakers: (block) => ({
      title: block.blockName || "Ponentes",
      value: "ponentes",
      content: <SpeakersTabContent data={block as SpeakersBlock} />
    }),
    requirements: (block) => ({
      title: block.blockName || "Información Adicional",
      value: "requisitos",
      content: <RequirementsTab data={block as RequirementsBlock} />
    })
  }

  // Generar tabs en orden de eventBlocks
  const blockTabs: MotionTab[] = (activity.eventBlocks as TabEventBlock[] || [])
    .map((block) => {
      if (blockTabMap[block.blockType]) {
        return blockTabMap[block.blockType](block)
      }
      return null
    })
    .filter(Boolean) as MotionTab[]

  const tabs: MotionTab[] = [mainTab, ...blockTabs]

  return (
    <div className="flex flex-col max-w-5xl mx-auto w-full items-start justify-start relative">
      <MotionTabs tabs={tabs} />
    </div>
  )
}