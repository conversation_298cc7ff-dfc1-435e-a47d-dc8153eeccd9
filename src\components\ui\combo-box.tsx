"use client"

import { useId, useState } from "react"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import { Label } from "@/components/ui/label"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { ChevronDownIcon, X } from "lucide-react"

type Option = {
  value: string
  label: string
  icon?: React.FC<React.ComponentProps<'svg'>>
}

interface ComboBoxProps {
  label: string;
  defaultValue?: string;
  placeholder?: string;
  items: Option[];
  emptyMessage?: string;

  handleSelect?: (value: string) => void;
  handleClear?: () => void;
}

export default function ComboBox(
  { defaultValue, label, items, placeholder, ...props }: ComboBoxProps
) {
  const id = useId()
  const [open, setOpen] = useState<boolean>(false)
  const [value, setValue] = useState<string>(defaultValue || "")

  function handleSelect(currentValue: string) {
    setValue(currentValue === value ? "" : currentValue)
    setOpen(false)
    props.handleSelect?.(currentValue)
  }

  function handleClear() {
    setValue("")
    setOpen(false)
    props.handleClear?.()
  }

  return (
    <div className="*:not-first:mt-2">
      <Label htmlFor={id}>{label}</Label>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            type="button"
            id={id}
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="bg-background hover:bg-background border-input w-full justify-between px-3 font-normal outline-offset-0 outline-none focus-visible:outline-[3px]"
          >
            {value ? (
              <span className="flex min-w-0 items-center gap-2">
                {(() => {
                  const selectedItem = items.find(
                    (item) => item.value === value
                  )
                  if (selectedItem && selectedItem.icon) {
                    const Icon = selectedItem.icon
                    return <Icon className="text-muted-foreground size-4" />
                  }
                  return null
                })()}
                <span className="truncate">
                  {items.find((item) => item.value === value)?.label}
                </span>
              </span>
            ) : (
              <span className="text-muted-foreground">
                {placeholder || "Seleccione una opción"}
              </span>
            )}

            <div className="flex gap-2 items-center">
              {
                props.handleClear && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 hover:bg-gray-100"
                    onClick={handleClear}
                    type="button"
                    asChild
                  >
                    <X size={8} className="text-muted-foreground" />
                  </Button>
                )
              }
              <ChevronDownIcon
                size={16}
                className="text-muted-foreground/80 shrink-0"
                aria-hidden="true"
              />
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="border-input w-full min-w-[var(--radix-popper-anchor-width)] p-0"
          align="start"
        >
          <Command>
            <CommandInput placeholder="Buscar" />
            <CommandList>
              <CommandEmpty>
                {props.emptyMessage || "No se encontraron resultados"}
              </CommandEmpty>
              <CommandGroup>
                {items.map((item) => (
                  <CommandItem
                    key={item.value}
                    value={item.value}
                    onSelect={(currentValue) => handleSelect(currentValue)}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center gap-2">
                      {
                        item.icon && (
                          <item.icon className="text-muted-foreground size-4" />
                        )
                      }
                      {item.label}
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
