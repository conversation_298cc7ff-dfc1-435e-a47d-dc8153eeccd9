'use client'
import { FadeUp } from "@/components/motion";
import { Text } from "@/components/ui/text";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  MapPinIcon,
  GlobeIcon,
  VideoIcon,
  BuildingIcon,
  ClipboardListIcon,
  ExternalLinkIcon
} from "lucide-react";

interface LocationBlockData {
  modality: 'online' | 'presencial' | 'hybrid';
  conferenceLink?: string | null;
  platform?: 'zoom' | 'meet' | 'teams' | 'youtube' | 'other' | null;
  address?: string | null;
  venue?: string | null;
  instructions?: string | null;
}

interface LocationTabProps {
  title?: string;
  data: LocationBlockData;
}

export default function LocationTab({ title = "Ubicación y Acceso", data }: LocationTabProps) {
  const getModalityLabel = (modality: string) => {
    const labels = {
      online: 'En línea',
      presencial: 'Presencial',
      hybrid: 'Híbrido'
    };
    return labels[modality as keyof typeof labels] || modality;
  };

  const getModalityIcon = (modality: string) => {
    const icons = {
      online: VideoIcon,
      presencial: BuildingIcon,
      hybrid: GlobeIcon
    };
    return icons[modality as keyof typeof icons] || GlobeIcon;
  };

  const getPlatformLabel = (platform: string) => {
    const labels = {
      zoom: 'Zoom',
      meet: 'Google Meet',
      teams: 'Microsoft Teams',
      youtube: 'YouTube Live',
      other: 'Otra plataforma'
    };
    return labels[platform as keyof typeof labels] || platform;
  };

  const ModalityIcon = getModalityIcon(data.modality);

  return (
    <section className="p-8 space-y-8">
      <FadeUp delay={0.1}>
        <div className="text-center mb-12">
          <Text variant="h3" className="text-primary mb-3 font-light">
            {title}
          </Text>
          <div className="w-16 h-0.5 bg-gradient-to-r from-primary to-secondary mx-auto"></div>
        </div>
      </FadeUp>

      {/* Modalidad - Diseño destacado */}
      <FadeUp delay={0.2}>
        <div className="text-center py-8">
          <div className="inline-flex items-center gap-4 bg-gradient-to-r from-primary/5 to-secondary/5 px-8 py-4 rounded-full border border-gray-100">
            <ModalityIcon className="w-6 h-6 text-primary" />
            <div className="flex items-center gap-3">
              <Text className="text-gray-700 font-medium">Modalidad:</Text>
              <Badge variant="outline" className="border-primary/20 text-primary bg-white/80">
                {getModalityLabel(data.modality)}
              </Badge>
            </div>
          </div>
        </div>
      </FadeUp>

      {/* Información específica según modalidad */}
      <div className="space-y-6">
        {/* Online/Híbrido - Enlace de conferencia */}
        {(data.modality === 'online' || data.modality === 'hybrid') && data.conferenceLink && (
          <FadeUp delay={0.3}>
            <div className="relative overflow-hidden">
              {/* Fondo con patrón sutil */}
              <div className="absolute inset-0 bg-gradient-to-br from-secondary/5 via-transparent to-primary/5"></div>
              <div className="relative border-l-4 border-secondary pl-6 py-6">
                <div className="flex items-center gap-3 mb-4">
                  <VideoIcon className="w-5 h-5 text-secondary" />
                  <Text variant="h4" className="text-gray-900 font-medium">
                    Enlace de Acceso
                  </Text>
                </div>

                {data.platform && (
                  <div className="mb-4">
                    <span className="inline-flex items-center gap-2 text-sm text-gray-600 bg-white/60 px-3 py-1 rounded-full">
                      <div className="w-2 h-2 bg-secondary rounded-full"></div>
                      {getPlatformLabel(data.platform)}
                    </span>
                  </div>
                )}

                <Button asChild variant="default" size="lg" className="bg-primary hover:bg-primary/90 shadow-lg">
                  <a
                    href={data.conferenceLink}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-3"
                  >
                    <ExternalLinkIcon className="w-5 h-5" />
                    Unirse a la conferencia
                  </a>
                </Button>
              </div>
            </div>
          </FadeUp>
        )}

        {/* Presencial/Híbrido - Ubicación física */}
        {(data.modality === 'presencial' || data.modality === 'hybrid') && (data.address || data.venue) && (
          <FadeUp delay={0.4}>
            <div className="grid md:grid-cols-12 gap-6 items-center">
              <div className="md:col-span-1 flex justify-center">
                <div className="w-16 h-16 bg-primary rounded-2xl flex items-center justify-center shadow-lg">
                  <MapPinIcon className="w-8 h-8 text-white" />
                </div>
              </div>

              <div className="md:col-span-11 space-y-2">
                <Text variant="h4" className="text-gray-900 font-medium mb-3">
                  Dirección
                </Text>

                {data.venue && (
                  <div className="bg-gray-50 rounded-lg px-4 py-3 border-l-4 border-primary">
                    <Text className="text-gray-900 font-semibold">
                      {data.venue}
                    </Text>
                  </div>
                )}

                {data.address && (
                  <div className="flex items-start gap-3 text-gray-600">
                    <div className="w-1 h-1 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                    <Text className="text-sm leading-relaxed">
                      {data.address}
                    </Text>
                  </div>
                )}
              </div>
            </div>
          </FadeUp>
        )}

        {/* Instrucciones de acceso */}
        {data.instructions && (
          <FadeUp delay={0.5}>
            <div className="bg-gradient-to-r from-gray-50 to-white p-6 rounded-2xl border border-gray-100">
              <div className="flex items-center gap-3 mb-4">
                <ClipboardListIcon className="w-5 h-5 text-secondary" />
                <Text variant="h4" className="text-gray-900 font-medium">
                  Instrucciones de Acceso
                </Text>
              </div>

              <div className="relative">
                <div className="absolute left-0 top-0 bottom-0 w-0.5 bg-gradient-to-b from-secondary to-primary opacity-30"></div>
                <div className="pl-6">
                  <Text className="text-gray-700 leading-relaxed">
                    {data.instructions}
                  </Text>
                </div>
              </div>
            </div>
          </FadeUp>
        )}
      </div>

      {/* Información adicional para modalidad híbrida */}
      {data.modality === 'hybrid' && (
        <FadeUp delay={0.6}>
          <div className="relative mt-12 mb-8">
            {/* Línea decorativa superior */}
            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-32 h-0.5 bg-gradient-to-r from-primary to-secondary"></div>

            <div className="text-center pt-8">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-primary to-secondary rounded-full mb-6 shadow-lg">
                <GlobeIcon className="w-10 h-10 text-white" />
              </div>

              <Text variant="h4" className="text-gray-900 font-medium mb-4">
                Modalidad Híbrida
              </Text>

              <div className="max-w-md mx-auto">
                <Text className="text-gray-600 leading-relaxed">
                  Este evento se realizará de forma híbrida. Puedes participar tanto presencialmente
                  como en línea. Elige la modalidad que mejor se adapte a tus necesidades.
                </Text>
              </div>

              {/* Indicadores visuales */}
              <div className="flex justify-center gap-8 mt-6">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <div className="w-3 h-3 bg-primary rounded-full"></div>
                  <span>Presencial</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <div className="w-3 h-3 bg-secondary rounded-full"></div>
                  <span>En línea</span>
                </div>
              </div>
            </div>
          </div>
        </FadeUp>
      )}
    </section>
  );
}
