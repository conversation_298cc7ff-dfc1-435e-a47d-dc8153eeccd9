import { getPayloadClient } from '@/lib/payload'
import { HomeAboutUs } from '@/payload-types'

export const HomeAboutUsService = {
  async getHomeAboutUsData(): Promise<HomeAboutUs | null> {
    try {
      const payload = await getPayloadClient()

      // Obtener los datos de la sección "Sobre Nosotros" desde el global
      const aboutUsData = await payload.findGlobal({
        slug: 'home-about-us',
        depth: 2, // Para cargar las relaciones con las imágenes
      })

      return aboutUsData as HomeAboutUs
    } catch (error) {
      console.error('Error al obtener los datos de Sobre Nosotros:', error)
      return null
    }
  },
}
