import { unstable_cache } from 'next/cache'
import { getPayload } from 'payload'
import config from '@payload-config'
import { CACHE_TAGS } from './constants'
import type { GeneralSetting } from '@/payload-types'


async function getSiteSettingsUncached(): Promise<GeneralSetting | null> {
  try {
    const payload = await getPayload({ config })

    const result = await payload.findGlobal({
      slug: 'general-settings',
    })

    if (!result) {
      return null
    }

    return result
  } catch (error) {
    console.error('Error fetching site settings:', error)
    return null
  }
}

// Versión con caché
export const getSiteSettings = unstable_cache(
  getSiteSettingsUncached,
  CACHE_TAGS.GENERAL_SETTINGS,
  {
    tags: CACHE_TAGS.GENERAL_SETTINGS,
  }
)

// Versión sin caché para casos específicos
export { getSiteSettingsUncached }
