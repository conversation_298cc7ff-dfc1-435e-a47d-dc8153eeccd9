import { checkRole } from '@/backend/access/utils'
import { GROUPS } from '@/backend/constants/groups'
import type { CollectionConfig } from 'payload'

export const AcademicActivityTags: CollectionConfig = {
  slug: 'academic-activity-tags',
  dbName: 'actTags',
  labels: {
    singular: 'Etiqueta',
    plural: 'Etiquetas',
  },
  admin: {
    useAsTitle: 'name',
    group: GROUPS.EVENTS,
    description: 'Etiquetas para clasificar y filtrar actividades académicas',
  },
  access: {
    read: () => true,
    create: ({ req: { user } }) => checkRole(['admin', 'activities-coordinator'], user),
    update: ({ req: { user } }) => checkRole(['admin', 'activities-coordinator'], user),
    delete: ({ req: { user } }) => checkRole(['admin'], user),
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Nombre',
      admin: {
        description: 'Nombre de la etiqueta',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Descripción',
      admin: {
        description: 'Descripción opcional de la etiqueta',
      },
    },
    {
      name: 'active',
      type: 'checkbox',
      label: 'Activa',
      defaultValue: true,
      admin: {
        description: 'Determina si esta etiqueta está disponible para usar',
      },
    },
  ],
}
