"use client"

import { CarouselItem } from "@/types/carousel.items"
import { motion } from "motion/react"

export default function CarouselBackground(
  {
    children,
    items
  }: {
    children: React.ReactNode
    items: CarouselItem[]
  }
) {
  return (
    <div
      className="w-full h-full relative overflow-hidden bg-primary"
    >
      <motion.div
        initial={{
          backgroundImage: `url(${items[0].image})`,
        }}
        style={{
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
        className="absolute top-0 left-0 w-full h-full z-0 blur-md transition-all duration-1000 ease-in-out"
        id="carousel-background"
      />
      <div 
        className="absolute top-0 left-0 w-full h-full z-0 bg-primary opacity-80" 
      />

      {children}
    </div>
  )
}