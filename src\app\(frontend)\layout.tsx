import React from 'react'
import './globals.css'
import { Playfair_Display, Lato } from 'next/font/google'
import { Metadata } from 'next'
import Nav from '@/components/layout/nav'

const playfairDisplay = Playfair_Display({
  variable: '--font-playfair-display',
  subsets: ['latin'],
})

const lato = Lato({
  weight: ['100', '300', '400', '700'],
  subsets: ['latin'],
})

export const metadata: Metadata = {
  title: {
    default: "Academia Peruana de Doctores",
    template: "%s | Academia Peruana de Doctores",
  },
  description: 'Generated by create next app',
  icons: {
    icon: [
      {
        media: '(prefers-color-scheme: light)',
        url: '/favicon-light.webp',
        href: '/favicon-light.webp',
      },
      {
        media: '(prefers-color-scheme: dark)',
        url: '/favicon.ico',
        href: '/favicon.ico',
      },
    ],
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={`${playfairDisplay.variable} ${lato.className} antialiased relative`}>
        <Nav />
        {children}
      </body>
    </html>
  )
}
