'use client'

import { motion, MotionProps, Variants } from 'motion/react'
import { ReactNode } from 'react'

export type SlideDirection = 'up' | 'down' | 'left' | 'right' | 'up-left' | 'up-right' | 'down-left' | 'down-right'

export interface SlideProps extends Omit<MotionProps, 'variants' | 'initial' | 'animate' | 'exit'> {
  children: ReactNode
  /** Dirección del deslizamiento */
  direction?: SlideDirection
  /** Duración de la animación en segundos */
  duration?: number
  /** Retraso antes de iniciar la animación */
  delay?: number
  /** Distancia del deslizamiento en píxeles */
  distance?: number
  /** Opacidad inicial */
  initialOpacity?: number
  /** Opacidad final */
  finalOpacity?: number
  /** Función de easing personalizada */
  ease?: string | number[]
  /** Si la animación debe ejecutarse una sola vez */
  once?: boolean
  /** Umbral de visibilidad para activar la animación */
  threshold?: number
  /** Margen para el viewport */
  margin?: string
  /** Si debe animar al salir */
  animateExit?: boolean
  /** Si debe usar el viewport completo como referencia */
  useViewport?: boolean
  /** Clase CSS adicional */
  className?: string
}

const createSlideVariants = (
  direction: SlideDirection,
  distance: number,
  initialOpacity: number,
  finalOpacity: number,
  duration: number,
  delay: number,
  ease: string | number[],
  useViewport: boolean
): Variants => {
  const getInitialTransform = () => {
    const viewportDistance = useViewport ? '100vw' : distance

    switch (direction) {
      case 'up':
        return { y: useViewport ? '100vh' : distance, opacity: initialOpacity }
      case 'down':
        return { y: useViewport ? '-100vh' : -distance, opacity: initialOpacity }
      case 'left':
        return { x: viewportDistance, opacity: initialOpacity }
      case 'right':
        return { x: useViewport ? '-100vw' : -distance, opacity: initialOpacity }
      case 'up-left':
        return { 
          x: viewportDistance, 
          y: useViewport ? '100vh' : distance, 
          opacity: initialOpacity 
        }
      case 'up-right':
        return { 
          x: useViewport ? '-100vw' : -distance, 
          y: useViewport ? '100vh' : distance, 
          opacity: initialOpacity 
        }
      case 'down-left':
        return { 
          x: viewportDistance, 
          y: useViewport ? '-100vh' : -distance, 
          opacity: initialOpacity 
        }
      case 'down-right':
        return { 
          x: useViewport ? '-100vw' : -distance, 
          y: useViewport ? '-100vh' : -distance, 
          opacity: initialOpacity 
        }
      default:
        return { y: distance, opacity: initialOpacity }
    }
  }

  const getFinalTransform = () => {
    return { 
      x: 0, 
      y: 0, 
      opacity: finalOpacity,
      transition: {
        duration,
        delay,
        ease,
      }
    }
  }

  return {
    hidden: getInitialTransform(),
    visible: getFinalTransform(),
    exit: getInitialTransform(),
  }
}

export const Slide: React.FC<SlideProps> = ({
  children,
  direction = 'up',
  duration = 0.6,
  delay = 0,
  distance = 50,
  initialOpacity = 0,
  finalOpacity = 1,
  ease = 'easeOut',
  once = true,
  threshold = 0.1,
  margin = '0px',
  animateExit = false,
  useViewport = false,
  className,
  ...motionProps
}) => {
  const variants = createSlideVariants(
    direction,
    distance,
    initialOpacity,
    finalOpacity,
    duration,
    delay,
    ease,
    useViewport
  )

  return (
    <motion.div
      className={className}
      variants={variants}
      initial="hidden"
      whileInView="visible"
      exit={animateExit ? "exit" : undefined}
      viewport={{ 
        once, 
        amount: threshold,
        margin 
      }}
      {...motionProps}
    >
      {children}
    </motion.div>
  )
}

// Componentes predefinidos para direcciones específicas
export const SlideUp: React.FC<Omit<SlideProps, 'direction'>> = (props) => (
  <Slide direction="up" {...props} />
)

export const SlideDown: React.FC<Omit<SlideProps, 'direction'>> = (props) => (
  <Slide direction="down" {...props} />
)

export const SlideLeft: React.FC<Omit<SlideProps, 'direction'>> = (props) => (
  <Slide direction="left" {...props} />
)

export const SlideRight: React.FC<Omit<SlideProps, 'direction'>> = (props) => (
  <Slide direction="right" {...props} />
)

export const SlideUpLeft: React.FC<Omit<SlideProps, 'direction'>> = (props) => (
  <Slide direction="up-left" {...props} />
)

export const SlideUpRight: React.FC<Omit<SlideProps, 'direction'>> = (props) => (
  <Slide direction="up-right" {...props} />
)

export const SlideDownLeft: React.FC<Omit<SlideProps, 'direction'>> = (props) => (
  <Slide direction="down-left" {...props} />
)

export const SlideDownRight: React.FC<Omit<SlideProps, 'direction'>> = (props) => (
  <Slide direction="down-right" {...props} />
)

// Variantes con configuraciones específicas
export const SlideUpSlow: React.FC<Omit<SlideProps, 'direction' | 'duration'>> = (props) => (
  <Slide direction="up" duration={1.2} {...props} />
)

export const SlideUpFast: React.FC<Omit<SlideProps, 'direction' | 'duration'>> = (props) => (
  <Slide direction="up" duration={0.3} {...props} />
)

export const SlideUpBounce: React.FC<Omit<SlideProps, 'direction' | 'ease'>> = (props) => (
  <Slide direction="up" ease={[0.68, -0.55, 0.265, 1.55]} {...props} />
)

export const SlideUpFar: React.FC<Omit<SlideProps, 'direction' | 'distance'>> = (props) => (
  <Slide direction="up" distance={100} {...props} />
)

// Variantes que usan viewport completo
export const SlideInFromBottom: React.FC<Omit<SlideProps, 'direction' | 'useViewport'>> = (props) => (
  <Slide direction="up" useViewport {...props} />
)

export const SlideInFromTop: React.FC<Omit<SlideProps, 'direction' | 'useViewport'>> = (props) => (
  <Slide direction="down" useViewport {...props} />
)

export const SlideInFromLeft: React.FC<Omit<SlideProps, 'direction' | 'useViewport'>> = (props) => (
  <Slide direction="right" useViewport {...props} />
)

export const SlideInFromRight: React.FC<Omit<SlideProps, 'direction' | 'useViewport'>> = (props) => (
  <Slide direction="left" useViewport {...props} />
)

export default Slide
