/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    users: User;
    'home-carousel': HomeCarousel;
    'home-carousel-media': HomeCarouselMedia;
    media: Media;
    'historia-list': HistoriaList;
    'academic-activities': AcademicActivity;
    'academic-activity-categories': AcademicActivityCategory;
    'academic-activity-tags': AcademicActivityTag;
    'academic-activity-media': AcademicActivityMedia;
    speakers: Speaker;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    users: UsersSelect<false> | UsersSelect<true>;
    'home-carousel': HomeCarouselSelect<false> | HomeCarouselSelect<true>;
    'home-carousel-media': HomeCarouselMediaSelect<false> | HomeCarouselMediaSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    'historia-list': HistoriaListSelect<false> | HistoriaListSelect<true>;
    'academic-activities': AcademicActivitiesSelect<false> | AcademicActivitiesSelect<true>;
    'academic-activity-categories': AcademicActivityCategoriesSelect<false> | AcademicActivityCategoriesSelect<true>;
    'academic-activity-tags': AcademicActivityTagsSelect<false> | AcademicActivityTagsSelect<true>;
    'academic-activity-media': AcademicActivityMediaSelect<false> | AcademicActivityMediaSelect<true>;
    speakers: SpeakersSelect<false> | SpeakersSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: number;
  };
  globals: {
    'home-about-us': HomeAboutUs;
    'home-work-areas': HomeWorkArea;
    'home-join-us': HomeJoinUs;
    'about-us-page-banner': AboutUsPageBanner;
    'about-us-page-historia-resumen': AboutUsPageHistoriaResuman;
    'about-us-page-mision-vision': AboutUsPageMisionVision;
  };
  globalsSelect: {
    'home-about-us': HomeAboutUsSelect<false> | HomeAboutUsSelect<true>;
    'home-work-areas': HomeWorkAreasSelect<false> | HomeWorkAreasSelect<true>;
    'home-join-us': HomeJoinUsSelect<false> | HomeJoinUsSelect<true>;
    'about-us-page-banner': AboutUsPageBannerSelect<false> | AboutUsPageBannerSelect<true>;
    'about-us-page-historia-resumen': AboutUsPageHistoriaResumenSelect<false> | AboutUsPageHistoriaResumenSelect<true>;
    'about-us-page-mision-vision': AboutUsPageMisionVisionSelect<false> | AboutUsPageMisionVisionSelect<true>;
  };
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: unknown;
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * Gestiona los usuarios de la aplicación
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: number;
  name: string;
  lastName: string;
  roles: ('admin' | 'content-editor' | 'media-manager' | 'activities-coordinator')[];
  /**
   * Usuario que creó el registro
   */
  createdBy?: (number | null) | User;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  password?: string | null;
}
/**
 * Carousel de imágenes para la página de inicio
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "home-carousel".
 */
export interface HomeCarousel {
  id: number;
  /**
   * Título principal que se mostrará en el carousel
   */
  title: string;
  /**
   * Texto descriptivo que aparecerá debajo del título
   */
  description: string;
  /**
   * Imagen de fondo para el slide del carousel
   */
  image: number | HomeCarouselMedia;
  /**
   * Botones de acción para este slide
   */
  buttons?:
    | {
        /**
         * Texto que se mostrará en el botón
         */
        text: string;
        /**
         * URL a la que dirigirá el botón
         */
        href: string;
        /**
         * Selecciona un ícono de la lista disponible
         */
        icon?: string | null;
        /**
         * Estilo visual del botón
         */
        variant: 'white' | 'secondary' | 'default' | 'link' | 'destructive';
        /**
         * Tamaño del botón
         */
        size: 'default' | 'sm' | 'lg';
        id?: string | null;
      }[]
    | null;
  /**
   * Posición en la que aparecerá este slide (menor número = aparece primero)
   */
  order?: number | null;
  /**
   * Determina si este slide se muestra en el carousel
   */
  active?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * Imágenes para el carousel de la página de inicio
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "home-carousel-media".
 */
export interface HomeCarouselMedia {
  id: number;
  /**
   * Descripción de la imagen para accesibilidad
   */
  alt: string;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
  sizes?: {
    thumbnail?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    desktop?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: number;
  alt: string;
  /**
   * Usuario que creó el registro
   */
  createdBy?: (number | null) | User;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
}
/**
 * Lista de historias para la sección de Historia
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "historia-list".
 */
export interface HistoriaList {
  id: number;
  /**
   * Año de la historia
   */
  year: string;
  /**
   * Resumen de la historia
   */
  resume: string;
  /**
   * Descripción de la historia
   */
  description: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  /**
   * Imágenes de la historia
   */
  images?:
    | {
        /**
         * Imagen de la historia
         */
        image: number | Media;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * Gestión de Actividades Académicas
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "academic-activities".
 */
export interface AcademicActivity {
  id: number;
  /**
   * Imagen principal de la actividad
   */
  mainImage: number | AcademicActivityMedia;
  /**
   * Título de la actividad
   */
  title: string;
  /**
   * URL amigable generada automáticamente desde el título
   */
  slug?: string | null;
  /**
   * Descripción breve de la actividad que aparecerá en las tarjetas
   */
  excerpt: string;
  /**
   * Contenido detallado de la actividad
   */
  content?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  /**
   * Define si la actividad se muestra en la página
   */
  status: 'active' | 'inactive';
  /**
   * Fecha y hora de inicio de la actividad
   */
  startDate: string;
  /**
   * Fecha y hora de finalización de la actividad (opcional). Si no se especifica, se asume que es el mismo día.
   */
  endDate?: string | null;
  /**
   * Tipo de actividad
   */
  category?: (number | null) | AcademicActivityCategory;
  /**
   * Etiquetas asociadas a la actividad
   */
  tags?: (number | AcademicActivityTag)[] | null;
  /**
   * Agrega y organiza el contenido específico de la actividad usando bloques flexibles
   */
  eventBlocks?:
    | (
        | {
            /**
             * Modalidad del evento
             */
            modality: 'online' | 'presencial' | 'hybrid';
            /**
             * Enlace para eventos online (Zoom, Meet, etc.)
             */
            conferenceLink?: string | null;
            /**
             * Plataforma para eventos online
             */
            platform?: ('zoom' | 'meet' | 'teams' | 'youtube' | 'other') | null;
            /**
             * Dirección física para eventos presenciales
             */
            address?: string | null;
            /**
             * Nombre del lugar o sede del evento
             */
            venue?: string | null;
            /**
             * Instrucciones adicionales para acceder a la actividad
             */
            instructions?: {
              root: {
                type: string;
                children: {
                  type: string;
                  version: number;
                  [k: string]: unknown;
                }[];
                direction: ('ltr' | 'rtl') | null;
                format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                indent: number;
                version: number;
              };
              [k: string]: unknown;
            } | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'location';
          }
        | {
            /**
             * Título que aparecerá encima de la lista de ponentes
             */
            title?: string | null;
            /**
             * Selecciona los ponentes que participarán en esta actividad
             */
            speakers?: (number | Speaker)[] | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'speakers';
          }
        | {
            /**
             * Título de la sección de agenda
             */
            title?: string | null;
            /**
             * Cronograma detallado del evento
             */
            items?:
              | {
                  /**
                   * Hora del elemento (ej: "09:00", "09:00 - 10:30")
                   */
                  time: string;
                  /**
                   * Título de la actividad
                   */
                  title: string;
                  /**
                   * Descripción detallada de la actividad
                   */
                  description?: string | null;
                  /**
                   * Selecciona el ponente a cargo de esta actividad
                   */
                  speaker?: (number | null) | Speaker;
                  /**
                   * Tipo de actividad para aplicar estilos específicos
                   */
                  type?:
                    | (
                        | 'conference'
                        | 'workshop'
                        | 'panel'
                        | 'networking'
                        | 'break'
                        | 'lunch'
                        | 'registration'
                        | 'closing'
                        | 'other'
                      )
                    | null;
                  /**
                   * Solo si no encuentras el tipo de actividad en la lista anterior
                   */
                  customType?: string | null;
                  id?: string | null;
                }[]
              | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'agenda';
          }
        | {
            /**
             * Título que aparecerá encima de los botones
             */
            title?: string | null;
            /**
             * Texto descriptivo que aparecerá antes de los botones
             */
            description?: string | null;
            /**
             * Botones de acción para este evento (registro, más información, etc.)
             */
            buttons?:
              | {
                  /**
                   * Texto que se mostrará en el botón
                   */
                  text: string;
                  /**
                   * URL a la que dirigirá el botón
                   */
                  href: string;
                  /**
                   * Selecciona un ícono de la lista disponible
                   */
                  icon?: string | null;
                  /**
                   * Estilo visual del botón
                   */
                  variant: 'white' | 'secondary' | 'default' | 'link' | 'destructive';
                  /**
                   * Tamaño del botón
                   */
                  size: 'default' | 'sm' | 'lg';
                  id?: string | null;
                }[]
              | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'actions';
          }
        | {
            /**
             * Título que aparecerá encima de los requisitos
             */
            title?: string | null;
            /**
             * Requisitos, materiales u otra información importante para el evento
             */
            content?: {
              root: {
                type: string;
                children: {
                  type: string;
                  version: number;
                  [k: string]: unknown;
                }[];
                direction: ('ltr' | 'rtl') | null;
                format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                indent: number;
                version: number;
              };
              [k: string]: unknown;
            } | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'requirements';
          }
      )[]
    | null;
  /**
   * Usuario que creó el registro
   */
  createdBy?: (number | null) | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * Imágenes utilizadas en las actividades académicas
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "academic-activity-media".
 */
export interface AcademicActivityMedia {
  id: number;
  /**
   * Descripción de la imagen para accesibilidad
   */
  alt?: string | null;
  /**
   * Usuario que creó el registro
   */
  createdBy?: (number | null) | User;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
  sizes?: {
    thumbnail?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    card?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    hero?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
  };
}
/**
 * Categorías para clasificar las actividades académicas
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "academic-activity-categories".
 */
export interface AcademicActivityCategory {
  id: number;
  /**
   * Nombre de la categoría
   */
  name: string;
  /**
   * Descripción opcional de la categoría
   */
  description?: string | null;
  /**
   * Color hexadecimal para identificar la categoría (ej: #FF5733)
   */
  color?: string | null;
  /**
   * Determina si esta categoría está disponible para usar
   */
  active?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * Etiquetas para clasificar y filtrar actividades académicas
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "academic-activity-tags".
 */
export interface AcademicActivityTag {
  id: number;
  /**
   * Nombre de la etiqueta
   */
  name: string;
  /**
   * Descripción opcional de la etiqueta
   */
  description?: string | null;
  /**
   * Determina si esta etiqueta está disponible para usar
   */
  active?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "speakers".
 */
export interface Speaker {
  id: number;
  /**
   * Nombre completo del ponente
   */
  fullName: string;
  /**
   * Foto circular pequeña del ponente (recomendado: 200x200px)
   */
  photo?: (number | null) | Media;
  /**
   * Grado académico del ponente
   */
  degree?: ('doctor' | 'doctora' | 'magister' | 'licenciado' | 'ingeniero' | 'profesor' | 'other') | null;
  /**
   * Solo si seleccionaste "Otro" en grado académico
   */
  customDegree?: string | null;
  /**
   * Área de especialización o campo de expertise
   */
  specialty?: string | null;
  /**
   * Universidad, empresa o institución de afiliación
   */
  institution?: string | null;
  /**
   * Cargo actual en su institución
   */
  position?: string | null;
  /**
   * Breve descripción profesional (opcional)
   */
  bio?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  socialLinks?:
    | {
        platform: 'linkedin' | 'twitter' | 'website' | 'email' | 'other';
        /**
         * Solo si seleccionaste "Otra" en plataforma
         */
        customPlatform?: string | null;
        /**
         * Enlace completo (incluir https:// para websites)
         */
        url: string;
        id?: string | null;
      }[]
    | null;
  /**
   * Ponente disponible
   */
  active?: boolean | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: number;
  document?:
    | ({
        relationTo: 'users';
        value: number | User;
      } | null)
    | ({
        relationTo: 'home-carousel';
        value: number | HomeCarousel;
      } | null)
    | ({
        relationTo: 'home-carousel-media';
        value: number | HomeCarouselMedia;
      } | null)
    | ({
        relationTo: 'media';
        value: number | Media;
      } | null)
    | ({
        relationTo: 'historia-list';
        value: number | HistoriaList;
      } | null)
    | ({
        relationTo: 'academic-activities';
        value: number | AcademicActivity;
      } | null)
    | ({
        relationTo: 'academic-activity-categories';
        value: number | AcademicActivityCategory;
      } | null)
    | ({
        relationTo: 'academic-activity-tags';
        value: number | AcademicActivityTag;
      } | null)
    | ({
        relationTo: 'academic-activity-media';
        value: number | AcademicActivityMedia;
      } | null)
    | ({
        relationTo: 'speakers';
        value: number | Speaker;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: number;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: number;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  name?: T;
  lastName?: T;
  roles?: T;
  createdBy?: T;
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "home-carousel_select".
 */
export interface HomeCarouselSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  image?: T;
  buttons?:
    | T
    | {
        text?: T;
        href?: T;
        icon?: T;
        variant?: T;
        size?: T;
        id?: T;
      };
  order?: T;
  active?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "home-carousel-media_select".
 */
export interface HomeCarouselMediaSelect<T extends boolean = true> {
  alt?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
  sizes?:
    | T
    | {
        thumbnail?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        desktop?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  createdBy?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "historia-list_select".
 */
export interface HistoriaListSelect<T extends boolean = true> {
  year?: T;
  resume?: T;
  description?: T;
  images?:
    | T
    | {
        image?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "academic-activities_select".
 */
export interface AcademicActivitiesSelect<T extends boolean = true> {
  mainImage?: T;
  title?: T;
  slug?: T;
  excerpt?: T;
  content?: T;
  status?: T;
  startDate?: T;
  endDate?: T;
  category?: T;
  tags?: T;
  eventBlocks?:
    | T
    | {
        location?:
          | T
          | {
              modality?: T;
              conferenceLink?: T;
              platform?: T;
              address?: T;
              venue?: T;
              instructions?: T;
              id?: T;
              blockName?: T;
            };
        speakers?:
          | T
          | {
              title?: T;
              speakers?: T;
              id?: T;
              blockName?: T;
            };
        agenda?:
          | T
          | {
              title?: T;
              items?:
                | T
                | {
                    time?: T;
                    title?: T;
                    description?: T;
                    speaker?: T;
                    type?: T;
                    customType?: T;
                    id?: T;
                  };
              id?: T;
              blockName?: T;
            };
        actions?:
          | T
          | {
              title?: T;
              description?: T;
              buttons?:
                | T
                | {
                    text?: T;
                    href?: T;
                    icon?: T;
                    variant?: T;
                    size?: T;
                    id?: T;
                  };
              id?: T;
              blockName?: T;
            };
        requirements?:
          | T
          | {
              title?: T;
              content?: T;
              id?: T;
              blockName?: T;
            };
      };
  createdBy?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "academic-activity-categories_select".
 */
export interface AcademicActivityCategoriesSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  color?: T;
  active?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "academic-activity-tags_select".
 */
export interface AcademicActivityTagsSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  active?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "academic-activity-media_select".
 */
export interface AcademicActivityMediaSelect<T extends boolean = true> {
  alt?: T;
  createdBy?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
  sizes?:
    | T
    | {
        thumbnail?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        card?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        hero?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "speakers_select".
 */
export interface SpeakersSelect<T extends boolean = true> {
  fullName?: T;
  photo?: T;
  degree?: T;
  customDegree?: T;
  specialty?: T;
  institution?: T;
  position?: T;
  bio?: T;
  socialLinks?:
    | T
    | {
        platform?: T;
        customPlatform?: T;
        url?: T;
        id?: T;
      };
  active?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "home-about-us".
 */
export interface HomeAboutUs {
  id: number;
  /**
   * Título principal que se mostrará en la sección 'Sobre nosotros' de la página principal
   */
  title: string;
  /**
   * Subtítulo que se mostrará en la sección 'Sobre nosotros' de la página principal
   */
  subtitle: string;
  /**
   * Descripción que se mostrará en la sección 'Sobre nosotros' de la página principal
   */
  description: string;
  /**
   * Botones de acción para este slide
   */
  buttons?:
    | {
        /**
         * Texto que se mostrará en el botón
         */
        text: string;
        /**
         * URL a la que dirigirá el botón
         */
        href: string;
        /**
         * Selecciona un ícono de la lista disponible
         */
        icon?: string | null;
        /**
         * Estilo visual del botón
         */
        variant: 'white' | 'secondary' | 'default' | 'link' | 'destructive';
        /**
         * Tamaño del botón
         */
        size: 'default' | 'sm' | 'lg';
        id?: string | null;
      }[]
    | null;
  /**
   * Lista de logros o datos destacados en indicadores cuantificables
   */
  highlights?:
    | {
        number: number;
        description: string;
        /**
         * Activa esta opción para mostrar un '+' después del número.
         */
        plus?: boolean | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Sube imágenes para la galería de la sección "Sobre Nosotros".
   */
  gallery?:
    | {
        /**
         * Selecciona o sube una imagen para la galería.
         */
        image: number | Media;
        id?: string | null;
      }[]
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "home-work-areas".
 */
export interface HomeWorkArea {
  id: number;
  /**
   * Imagen de fondo para la sección
   */
  backgroundImage: number | Media;
  /**
   * Título principal que se mostrará en la sección
   */
  title: string;
  /**
   * Descripción que se mostrará en la sección
   */
  description: string;
  /**
   * Lista de áreas de trabajo o elementos que se mostrarán en esta sección
   */
  items?:
    | {
        /**
         * Selecciona un ícono de la lista disponible
         */
        icon?: string | null;
        /**
         * Título del elemento
         */
        title: string;
        /**
         * Descripción detallada del elemento
         */
        description: string;
        id?: string | null;
      }[]
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * Configuración de la sección "Únete a Nuestra Comunidad Académica" en la página de inicio
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "home-join-us".
 */
export interface HomeJoinUs {
  id: number;
  /**
   * Título principal de la sección
   */
  title: string;
  /**
   * Descripción que aparecerá debajo del título
   */
  description: string;
  /**
   * Imagen que se mostrará en la sección
   */
  image: number | Media;
  /**
   * Razones para unirse a la comunidad académica
   */
  items?:
    | {
        /**
         * Selecciona un ícono de la lista disponible
         */
        icon?: string | null;
        /**
         * Título de la razón
         */
        title: string;
        /**
         * Descripción opcional de la razón
         */
        description?: string | null;
        id?: string | null;
      }[]
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "about-us-page-banner".
 */
export interface AboutUsPageBanner {
  id: number;
  /**
   * Banner que se mostrará en la parte superior de la página de Sobre Nosotros
   */
  banner: number | Media;
  /**
   * Título que se mostrará en la parte superior de la página de Sobre Nosotros
   */
  title: string;
  /**
   * Descripción que se mostrará en la parte superior de la página de Sobre Nosotros
   */
  description: string;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "about-us-page-historia-resumen".
 */
export interface AboutUsPageHistoriaResuman {
  id: number;
  /**
   * Pre-título que se mostrará en la página de Sobre Nosotros
   */
  'pre-title': string;
  /**
   * Título que se mostrará en la página de Sobre Nosotros
   */
  title: string;
  /**
   * Historia que se mostrará en la página de Sobre Nosotros
   */
  historia: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "about-us-page-mision-vision".
 */
export interface AboutUsPageMisionVision {
  id: number;
  /**
   * Misión que se mostrará en la página de Sobre Nosotros
   */
  mision: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  /**
   * Visión que se mostrará en la página de Sobre Nosotros
   */
  vision: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "home-about-us_select".
 */
export interface HomeAboutUsSelect<T extends boolean = true> {
  title?: T;
  subtitle?: T;
  description?: T;
  buttons?:
    | T
    | {
        text?: T;
        href?: T;
        icon?: T;
        variant?: T;
        size?: T;
        id?: T;
      };
  highlights?:
    | T
    | {
        number?: T;
        description?: T;
        plus?: T;
        id?: T;
      };
  gallery?:
    | T
    | {
        image?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "home-work-areas_select".
 */
export interface HomeWorkAreasSelect<T extends boolean = true> {
  backgroundImage?: T;
  title?: T;
  description?: T;
  items?:
    | T
    | {
        icon?: T;
        title?: T;
        description?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "home-join-us_select".
 */
export interface HomeJoinUsSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  image?: T;
  items?:
    | T
    | {
        icon?: T;
        title?: T;
        description?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "about-us-page-banner_select".
 */
export interface AboutUsPageBannerSelect<T extends boolean = true> {
  banner?: T;
  title?: T;
  description?: T;
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "about-us-page-historia-resumen_select".
 */
export interface AboutUsPageHistoriaResumenSelect<T extends boolean = true> {
  'pre-title'?: T;
  title?: T;
  historia?: T;
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "about-us-page-mision-vision_select".
 */
export interface AboutUsPageMisionVisionSelect<T extends boolean = true> {
  mision?: T;
  vision?: T;
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}