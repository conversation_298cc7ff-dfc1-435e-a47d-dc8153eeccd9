'use client'
import { useState } from 'react'
import { usePathname } from 'next/navigation'
import { Menu, X } from 'lucide-react'
import Logo from '@/components/shared/logo'
import NavContainer from './NavContainer'
import Link from 'next/link'
import { NavLink } from '@/components/shared/nav-link'
import { ROUTES, Route } from '@/constants/routes'
import {
  Sheet,
  <PERSON>etContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetClose,
} from '@/components/ui/sheet'
import { Button } from '@/components/ui/button'

export default function Nav() {
  const [isOpen, setIsOpen] = useState(false)
  const pathname = usePathname()

  const handleNavClick = () => {
    setIsOpen(false)
  }

  const isActiveRoute = (routePath: string): boolean => {
    if (routePath === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(routePath)
  }

  return (
    <NavContainer>
      <div className="container mx-auto py-3">
        <div className='flex items-center justify-between'>
          <Link href="/">
            <Logo variant="dark" />
          </Link>

          {/* Desktop Navigation */}
          <div className='hidden md:flex gap-8'>
            {ROUTES.map((route: Route) => (
              <NavLink
                key={route.path}
                href={route.path}
                className={`text-white hover:text-secondary transition-colors duration-200 font-medium ${isActiveRoute(route.path) ? 'text-secondary' : ''
                  }`}
                active={isActiveRoute(route.path)}
              >
                {route.label}
              </NavLink>
            ))}
          </div>

          {/* Mobile Navigation */}
          <div className='block md:hidden'>
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <button
                  className="p-2 text-white hover:text-secondary transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                  aria-label="Abrir menú de navegación"
                >
                  <Menu className="w-6 h-6" />
                </button>
              </SheetTrigger>

              <SheetContent
                side="right"
                hideCloseButton
                className="bg-primary text-white border-none w-full sm:max-w-sm p-0 flex flex-col"
              >
                {/* Header con logo y close button */}
                <SheetHeader className="border-b border-white/10 p-6"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex flex-col gap-3">
                      <Logo variant="dark" />
                      <SheetTitle className="text-white text-lg font-semibold">
                        Menú
                      </SheetTitle>
                    </div>
                    <SheetClose asChild>
                      <Button
                        className="text-white"
                        aria-label="Cerrar menú"
                        variant={"ghost"}
                      >
                        <X className="w-5 h-5" />
                      </Button>
                    </SheetClose>
                  </div>
                </SheetHeader>

                {/* Navigation Links */}
                <nav className="flex-1 px-6 py-8">
                  <div className="space-y-1">
                    {ROUTES.map((route: Route) => (
                      <div key={route.path} onClick={handleNavClick}>
                        <NavLink
                          href={route.path}
                          className={"block py-4 text-lg font-medium transition-all duration-200"}
                          active={isActiveRoute(route.path)}
                        >
                          <span>
                            {route.label}
                          </span>
                        </NavLink>
                      </div>
                    ))}
                  </div>
                </nav>

                {/* Footer del sheet */}
                <div className="border-t border-white/10 p-6">
                  <div className="text-center">
                    <p className="text-white/80 text-sm font-medium mb-1">
                      Academia Peruana de Doctores
                    </p>
                    <p className="text-white/60 text-xs">
                      Contribuyendo al progreso científico
                    </p>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </NavContainer>
  )
}
