'use client'
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Text } from '@/components/ui/text'
import { Mic, Users, Coffee, BookOpen, Award, Clock } from 'lucide-react'
import { Media, Speaker } from '@/payload-types'

interface AgendaBlockProps {
  block: {
    blockType: 'agenda'
    title?: string
    items?: Array<{
      time: string
      title: string
      description?: string
      speaker?: Speaker | string
      customSpeaker?: string
      type?: 'conference' | 'workshop' | 'panel' | 'networking' | 'break' | 'lunch' | 'registration' | 'closing'
    }>
  }
}

const activityIcons = {
  conference: Mic,
  workshop: BookOpen,
  panel: Users,
  networking: Users,
  break: Coffee,
  lunch: Coffee,
  registration: Users,
  closing: Award,
}

const activityColors = {
  conference: 'text-blue-500',
  workshop: 'text-purple-500',
  panel: 'text-green-500',
  networking: 'text-pink-500',
  break: 'text-orange-500',
  lunch: 'text-orange-500',
  registration: 'text-gray-500',
  closing: 'text-red-500',
}

const activityBgColors = {
  conference: 'bg-blue-50 border-blue-200',
  workshop: 'bg-purple-50 border-purple-200',
  panel: 'bg-green-50 border-green-200',
  networking: 'bg-pink-50 border-pink-200',
  break: 'bg-orange-50 border-orange-200',
  lunch: 'bg-orange-50 border-orange-200',
  registration: 'bg-gray-50 border-gray-200',
  closing: 'bg-red-50 border-red-200',
}

const activityLabels = {
  conference: 'Conferencia',
  workshop: 'Taller',
  panel: 'Panel',
  networking: 'Networking',
  break: 'Descanso',
  lunch: 'Almuerzo',
  registration: 'Registro',
  closing: 'Clausura',
}

export default function AgendaBlock({ block }: AgendaBlockProps) {
  if (!block.items || block.items.length === 0) {
    return null
  }

  const getSpeakerInfo = (item: any) => {
    if (item.speaker && typeof item.speaker === 'object') {
      const speaker = item.speaker as Speaker
      return {
        name: speaker.fullName,
        avatar: (speaker.photo as Media)?.url,
        degree: speaker.degree,
        specialty: speaker.specialty,
      }
    }

    if (item.customSpeaker) {
      return {
        name: item.customSpeaker,
        avatar: undefined,
        degree: undefined,
        specialty: undefined,
      }
    }

    return null
  }

  const getSpeakerDisplayName = (speakerInfo: any) => {
    if (!speakerInfo) return 'Por definir'

    let name = speakerInfo.name
    if (speakerInfo.degree && speakerInfo.degree !== 'other') {
      const degreeLabels = {
        doctor: 'Dr.',
        doctora: 'Dra.',
        magister: 'Mg.',
        licenciado: 'Lic.',
        ingeniero: 'Ing.',
        profesor: 'Prof.',
        phd: 'PhD',
        msc: 'MSc',
      }
      const degreeLabel = degreeLabels[speakerInfo.degree as keyof typeof degreeLabels]
      if (degreeLabel) {
        name = `${degreeLabel} ${name}`
      }
    }

    return name
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="w-5 h-5 text-primary" />
          {block.title || 'Agenda de la Actividad'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {block.items.map((item, index) => {
            const activityType = item.type || 'conference'
            const Icon = activityIcons[activityType]
            const iconColor = activityColors[activityType]
            const bgColor = activityBgColors[activityType]
            const speakerInfo = getSpeakerInfo(item)

            return (
              <div
                key={index}
                className={`flex gap-4 p-4 border rounded-lg transition-all hover:shadow-sm ${bgColor}`}
              >
                {/* Hora */}
                <div className="flex-shrink-0 w-16 text-sm font-medium text-muted-foreground">
                  {item.time}
                </div>

                {/* Icono */}
                <div className="flex items-center gap-2 flex-shrink-0">
                  <div className="p-1.5 rounded-full bg-white">
                    <Icon className={`h-4 w-4 ${iconColor}`} />
                  </div>
                </div>

                {/* Contenido */}
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-2">
                    <Text variant="h4" className="text-base">
                      {item.title}
                    </Text>
                    <Badge variant="outline" className="text-xs capitalize ml-2">
                      {activityLabels[activityType]}
                    </Badge>
                  </div>

                  {item.description && (
                    <Text variant="base" className="text-sm text-muted-foreground mb-3">
                      {item.description}
                    </Text>
                  )}

                  {/* Speaker Info */}
                  {speakerInfo && (
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarImage
                          src={speakerInfo.avatar || '/placeholder-avatar.jpg'}
                          alt={speakerInfo.name}
                        />
                        <AvatarFallback className="text-xs">
                          {speakerInfo.name
                            .split(' ')
                            .map((n: string) => n[0])
                            .join('')
                            .toUpperCase()
                            .slice(0, 2)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col">
                        <span className="text-sm font-medium">
                          {getSpeakerDisplayName(speakerInfo)}
                        </span>
                        {speakerInfo.specialty && (
                          <span className="text-xs text-muted-foreground">
                            {speakerInfo.specialty}
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
