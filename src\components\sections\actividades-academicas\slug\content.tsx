'use client'
import { AcademicActivity } from '@/payload-types'
import { Card, CardContent } from '@/components/ui/card'
import { FadeUp } from '@/components/motion'

interface AcademicActivityContentProps {
  activity: AcademicActivity
}

export default function AcademicActivityContent({ activity }: AcademicActivityContentProps) {
  return (
    <div className="space-y-6">
      {/* Excerpt/Resumen */}
      {activity.excerpt && (
        <FadeUp>
          <div className="text-lg text-muted-foreground leading-relaxed">
            {activity.excerpt}
          </div>
        </FadeUp>
      )}

      {/* Contenido Principal */}
      {activity.content && (
        <FadeUp delay={0.1}>
          <div
            className="prose prose-sm max-w-none prose-headings:text-primary prose-a:text-primary hover:prose-a:text-primary/80"
            dangerouslySetInnerHTML={{ __html: activity.content }}
          />
        </FadeUp>
      )}
    </div>
  )
}
