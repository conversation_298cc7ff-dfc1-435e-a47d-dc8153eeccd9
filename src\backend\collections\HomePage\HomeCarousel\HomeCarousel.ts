import { ButtonFields, ButtonLabels } from '@/backend/constants/fields/buttons.field'
import { GROUPS } from '@/backend/constants/groups'
import type { CollectionConfig } from 'payload'

export const HomeCarousel: CollectionConfig = {
  slug: 'home-carousel',
  labels: {
    singular: 'Item de Carrusel',
    plural: 'Carrusel de Imágenes',
  },
  admin: {
    useAsTitle: 'title',
    group: GROUPS.HOME,
    description: 'Carousel de imágenes para la página de inicio',
  },
  access: {
    read: () => true,
  },
  fields: [
    // Layout de dos columnas: contenido principal y visualización
    {
      type: 'row',
      fields: [
        // Columna izquierda - Contenido principal
        {
          type: 'collapsible',
          label: 'Contenido Principal',
          admin: {
            initCollapsed: false,
            width: '70%',
          },
          fields: [
            {
              name: 'title',
              type: 'text',
              required: true,
              label: 'Título',
              admin: {
                description: 'Título principal que se mostrará en el carousel',
              },
            },
            {
              name: 'description',
              type: 'textarea',
              required: true,
              label: 'Descripción',
              admin: {
                description: 'Texto descriptivo que aparecerá debajo del título',
              },
            },
            {
              name: 'image',
              type: 'upload',
              relationTo: 'home-carousel-media',
              required: true,
              label: 'Imagen',
              admin: {
                description: 'Imagen de fondo para el slide del carousel',
              },
            },
            {
              name: 'buttons',
              type: 'array',
              label: 'Botones',
              admin: {
                description: 'Botones de acción para este slide',
              },
              labels: ButtonLabels,
              fields: ButtonFields,
            },
          ],
        },
        // Columna derecha - Visualización
        {
          type: 'collapsible',
          label: 'Visualización',
          admin: {
            initCollapsed: false,
            width: '30%',
          },
          fields: [
            {
              name: 'order',
              type: 'number',
              label: 'Orden',
              admin: {
                description: 'Posición en la que aparecerá este slide (menor número = aparece primero)',
              },
              defaultValue: 0,
            },
            {
              name: 'active',
              type: 'checkbox',
              label: 'Activo',
              defaultValue: true,
              admin: {
                description: 'Determina si este slide se muestra en el carousel',
              },
            },
          ],
        },
      ],
    },
  ],
}
