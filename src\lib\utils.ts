import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export async function wait(time: number) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true)
    }, time)
  })
}

export function camelToKebab(str: string) {
  return str
    .replace(/([a-z0-9])([A-Z])/g, '$1-$2')
    .replace(/([A-Z])([A-Z][a-z])/g, '$1-$2')
    .toLowerCase();
}


export function hexToRgb(hex: string) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16),
    }
    : null;
}


/** Get the text color automatically based on the background color for better contrast
 * @param backgroundColor - The background color of the text in hexadecimal format
 * @returns The text color in hexadecimal format
 */
export const getTextColor = (backgroundColor: string) => {
  const calculateLuminance = ({ r, g, b }: { r: number; g: number; b: number }) => {
    const [R, G, B] = [r, g, b].map((channel) => {
      const c = channel / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * R + 0.7152 * G + 0.0722 * B;
  };

  const rgb = hexToRgb(backgroundColor);

  if (!rgb) {
    return "#FFFFFF";
  }

  const luminance = calculateLuminance(rgb);

  // Devuelve blanco o negro según la luminancia
  return luminance > 0.5 ? "#000000" : "#FFFFFF";
};

/** Determina si un link es externo, empieza por http:// o https:// y no contiene
 * el dominio de la página actual que viene en process.env.NEXT_PUBLIC_SERVER_URL
 */
export const isExternalLink = (href: string) => {
  const serverUrl = process.env.NEXT_PUBLIC_SERVER_URL;
  const isExternal = href.startsWith("http://") || href.startsWith("https://");
  const isInternal = serverUrl && href.startsWith(serverUrl);

  if (isInternal) return false;

  return isExternal;
};