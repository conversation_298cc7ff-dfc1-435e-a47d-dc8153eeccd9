import { AboutUsPageService } from "@/services/about-us.service"
import { Text } from "@/components/ui/text"

export default async function NosotrosBanner() {
    const banner = await AboutUsPageService.getBanner()

    if (!banner) {
        return <div><PERSON><PERSON>r al cargar el banner</div>
    }

    return (
        <section className="relative"
            style={{
                backgroundImage: `url(${typeof banner?.banner === "object" && banner?.banner !== null && "url" in banner.banner
                    ? (banner.banner as { url: string }).url
                    : ""
                    })`,
                backgroundSize: "cover",
                backgroundPosition: "center",
                height: "400px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                color: "#fff",
            }}
        >
            <div className="absolute inset-0 bg-primary/72 flex items-center justify-center">
                <div className="flex flex-col items-center gap-7">
                    <Text variant="h2" className="text-white text-5xl text-center">
                        {banner.title}
                    </Text>
                    <Text className="text-white/90 max-w-md text-center font-bold">
                        {banner.description}
                    </Text>
                </div>
            </div>

        </section>
    )
}