import { FadeDown } from "@/components/motion";
import { Text } from "@/components/ui/text";
import { Speaker } from "@/payload-types";
import SpeakersList from "./speakers-list";

export default function SpeakersTabContent({
    title, speakers
}: { title: string, speakers: Speaker[] }) {
    return (
        <section className="p-8">
            <FadeDown delay={0.2}>
                <Text variant="h3" className="text-primary mb-2">
                    {title}
                </Text>
            </FadeDown>

            <SpeakersList speakers={speakers} />
        </section>
    );
}
