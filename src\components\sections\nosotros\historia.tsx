import { AboutUsPageService } from "@/services/about-us.service"
import { Text } from "@/components/ui/text"
import { RichText } from "@payloadcms/richtext-lexical/react"
import Link from "next/link";
import { buttonVariants } from "@/components/ui/button";

export default async function NosotrosHistoria() {
    const historia = await AboutUsPageService.getHistoriaResume();
    if (!historia) {
        return (
            <section>
                <div className="container mx-auto py-20 flex">
                    <div className="flex-1">
                        <h2 className="text-4xl font-bold">Historia</h2>
                    </div>
                    <div className="flex-1">
                        <p>No se encontró la historia.</p>
                    </div>
                </div>
            </section>
        )
    }

    return (
        <section>
            <div className="container mx-auto py-20 flex gap-10">
                <div className="flex-1">
                    {/* Pre title */}
                    <div className="flex items-start">
                        <div className="border-secondary border-b-2 mb-4">
                            <Text variant="h4">
                                {historia["pre-title"]}
                            </Text>
                        </div>
                    </div>

                    {/* Title */}
                    <Text variant="h2" className="font-bold mb-4">
                        {historia.title}
                    </Text>

                    {/* Historia */}
                    <div>
                        <RichText
                            data={historia.historia}
                        />
                    </div>

                    <div className="mt-4">
                        <Link href={'/nosotros/historia'} className={buttonVariants()}>
                            Ver Historia Completa
                        </Link>
                    </div>

                </div>
                <div className="flex-1">
                    <div className="w-100 h-100 bg-gray-200 rounded-lg">

                    </div>
                </div>
            </div>

        </section>
    )
}