'use client'

import { AcademicActivity, AcademicActivityCategory } from '@/payload-types'
import { motion } from 'motion/react'
import { Text } from '@/components/ui/text'
import Link from 'next/link'
import { Calendar, MailOpenIcon, MapPin, Users } from 'lucide-react'
import { cn } from '@/lib/utils'
import { academicActivityMediaSizes } from '@/backend/collections/Events/AcademicActivityMedia'

interface ActivityCardProps {
  activity: AcademicActivity
  isLarge?: boolean
  className?: string
}

export default function ActivityCard({ activity, isLarge = false, className }: ActivityCardProps) {
  // Obtener la imagen de la actividad
  const getActivityImage = (): {
    url: string,
    width: number,
    height: number,
    focalPoint: { x: string, y: string }
  } => {
    if (activity.mainImage && typeof activity.mainImage === 'object' && activity.mainImage.url) {
      let imageWidth = null
      let imageHeight = null

      if (isLarge) {
        imageWidth = academicActivityMediaSizes.hero.width
        imageHeight = academicActivityMediaSizes.hero.height
      } else {
        imageWidth = academicActivityMediaSizes.card.width
        imageHeight = academicActivityMediaSizes.card.height
      }

      return {
        url: activity.mainImage.url,
        width: imageWidth,
        height: imageHeight,
        focalPoint: { x: `${activity.mainImage.focalX || 50}%`, y: `${activity.mainImage.focalY || 50}%` }
      }
    }
    return {
      url: '/placeholder-event.jpg',
      width: 1200,
      height: 630,
      focalPoint: { x: '50%', y: '50%' }
    }
  }

  // Obtener el nombre de la categoría
  const getCategoryName = () => {
    if (activity.category && typeof activity.category === 'object') {
      return (activity.category as AcademicActivityCategory).name
    }
    return ''
  }

  // Obtener información de fecha y hora
  const getDateTimeInfo = () => {
    if (!activity.startDate) return { date: '', time: '', isMultiDay: false }

    const startDate = new Date(activity.startDate)
    const endDate = activity.endDate ? new Date(activity.endDate) : null

    const date = startDate.toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    })

    const time = startDate.toLocaleTimeString('es-ES', {
      hour: '2-digit',
      minute: '2-digit'
    })

    const isMultiDay = endDate && startDate.toDateString() !== endDate.toDateString()

    return { date, time, isMultiDay, endDate }
  }

  // Obtener información de ubicación de los bloques
  const getLocationInfo = () => {
    if (!activity.eventBlocks) return { venue: '', modality: '' }

    const locationBlock = activity.eventBlocks.find(block => block.blockType === 'location')
    if (!locationBlock || locationBlock.blockType !== 'location') return { venue: '', modality: '' }

    const venue = locationBlock.venue || locationBlock.address || ''
    const modality = locationBlock.modality || ''

    return { venue, modality }
  }

  const { date, time, isMultiDay, endDate } = getDateTimeInfo()
  const { venue, modality } = getLocationInfo()

  return (
    <Link href={`/actividades-academicas/${activity.slug}`} className={cn("block h-full", className)}>
      <motion.div
        className="relative h-full rounded-xl overflow-hidden group cursor-pointer"
        whileHover={{ scale: 1.02 }}
        transition={{ duration: 0.3 }}
      >
        {/* Imagen de fondo */}
        <div
          className="absolute inset-0"
        >
          <img
            src={getActivityImage().url}
            alt={activity.title}
            width={getActivityImage().width}
            height={getActivityImage().height}
            className="w-full h-full object-cover"
            style={{
              objectPosition: `${getActivityImage().focalPoint.x} ${getActivityImage().focalPoint.y}`
            }}
          />
        </div>

        {/* Overlay gradiente */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-black/20 group-hover:from-black/90 group-hover:via-black/60 group-hover:to-black/30 transition-all duration-300" />

        {/* Badge de categoría */}
        {getCategoryName() && (
          <motion.div
            className="absolute top-4 right-4 z-10"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-secondary text-primary">
              {getCategoryName()}
            </span>
          </motion.div>
        )}

        {/* Contenido */}
        <div className="absolute inset-0 p-4 lg:p-6 flex flex-col justify-end">
          {/* Fecha y hora */}
          <motion.div
            className="flex flex-col gap-1 mb-3 text-secondary text-sm relative w-fit"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <span className='bg-primary/50 backdrop-blur-md absolute -left-12 px-32 py-4 -top-1 bottom-0 inset-0 z-0'></span>

            {date && (
              <div className="flex items-center gap-1 z-[10]">
                <Calendar className="w-4 h-4" />
                <span>{date}</span>
                {time && (
                  <>
                    <span>•</span>
                    <span>{time}</span>
                  </>
                )}
              </div>
            )}
            {isMultiDay && endDate && (
              <div className="flex items-center gap-1 text-xs opacity-80">
                <span>Hasta:</span>
                <span>
                  {endDate.toLocaleDateString('es-ES', {
                    day: 'numeric',
                    month: 'long',
                    year: 'numeric'
                  })}
                </span>
                <span>
                  {endDate.toLocaleTimeString('es-ES', {
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </span>
              </div>
            )}
          </motion.div>

          {/* Título */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Text
              variant={isLarge ? "h3" : "h4"}
              className="text-white mb-2 line-clamp-4 group-hover:text-secondary transition-colors duration-300"
            >
              {activity.title}
            </Text>
          </motion.div>

          {/* Excerpt */}
          {activity.excerpt && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <Text
                variant="description"
                className={cn(
                  "text-gray-300 group-hover:text-gray-200 transition-colors duration-300",
                  isLarge ? "line-clamp-3" : "line-clamp-2"
                )}
              >
                {activity.excerpt}
              </Text>
            </motion.div>
          )}

          {/* Información adicional */}
          <motion.div
            className="mt-4 flex items-center gap-4 text-xs text-gray-300 opacity-0 transform translate-y-2 group-hover:opacity-100 group-hover:translate-y-0 transition-all duration-300"
          >
            {venue && (
              <div className="flex items-center gap-1">
                <MapPin className="w-3 h-3" />
                <span className="line-clamp-1">{venue}</span>
              </div>
            )}
            {modality && (
              <div className="flex items-center gap-1">
                <Users className="w-3 h-3" />
                <span className="capitalize">{modality}</span>
              </div>
            )}
          </motion.div>
        </div>

        {/* Efecto de hover adicional */}
        <motion.div
          className="absolute inset-0 bg-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          initial={false}
        />
      </motion.div>
    </Link>
  )
}
