import { getPayloadClient } from '@/lib/payload'
import type { AcademicActivity } from '@/payload-types'
import type { CollectionSlug, DataFromCollectionSlug, FindArgs, PaginatedDocs } from 'payload'

export interface GetAcademicActivitiesParams {
  page?: number
  limit?: number
  search?: string
  category?: string
  tags?: string[]
  startDateFrom?: Date
  startDateTo?: Date
  endDateFrom?: Date
  endDateTo?: Date
  status?: AcademicActivity["status"]
  sort?: 'newest' | 'oldest' | 'start-date-asc' | 'start-date-desc' | 'end-date-asc' | 'end-date-desc' | 'a-z' | 'z-a',
  findArgs?: Omit<FindArgs, "collection">
}

export interface AcademicActivitiesResponse extends PaginatedDocs<DataFromCollectionSlug<CollectionSlug>> {
  docs: AcademicActivity[]
}

/**
 * Servicio principal para obtener eventos con filtros y paginación
 */
export const AcademicActivitiesService = {
  /**
   * Obtiene actividades académicas con filtros, paginación y ordenamiento
   */
  async getAcademicActivities(params: GetAcademicActivitiesParams = {}): Promise<AcademicActivitiesResponse> {
    const {
      page = 1,
      limit = 10,
      search = '',
      category = '',
      tags = [],
      startDateFrom,
      startDateTo,
      endDateFrom,
      endDateTo,
      status = 'active',
      sort = 'newest',
      findArgs
    } = params

    try {
      const payload = await getPayloadClient()

      const query: FindArgs = {
        collection: 'academic-activities',
        where: {
          status: {
            equals: status,
          },
        },
        page,
        limit,
        select: { "content": false, ...findArgs?.select },
        ...findArgs
      }

      // Inicializar where si no existe
      query.where = query.where || {}

      // Filtro por búsqueda de texto
      if (search) {
        query.where.or = [
          { title: { like: search } },
        ]
      }

      // Filtro por categoría
      if (category) {
        query.where.category = { equals: category }
      }

      // Filtro por etiquetas
      if (tags.length > 0) {
        query.where.tags = {
          in: tags,
        }
      }

      // Filtro por rango de fechas de inicio
      if (startDateFrom || startDateTo) {
        query.where.startDate = {}
        if (startDateFrom) {
          query.where.startDate.greater_than_equal = startDateFrom
        }
        if (startDateTo) {
          query.where.startDate.less_than_equal = startDateTo
        }
      }

      // Filtro por rango de fechas de fin
      if (endDateFrom || endDateTo) {
        query.where.endDate = {}
        if (endDateFrom) {
          query.where.endDate.greater_than_equal = endDateFrom
        }
        if (endDateTo) {
          query.where.endDate.less_than_equal = endDateTo
        }
      }

      // Configurar ordenamiento
      switch (sort) {
        case 'newest':
          query.sort = '-createdAt'
          break
        case 'oldest':
          query.sort = 'createdAt'
          break
        case 'start-date-asc':
          query.sort = 'startDate'
          break
        case 'start-date-desc':
          query.sort = '-startDate'
          break
        case 'end-date-asc':
          query.sort = 'endDate'
          break
        case 'end-date-desc':
          query.sort = '-endDate'
          break
        case 'a-z':
          query.sort = 'title'
          break
        case 'z-a':
          query.sort = '-title'
          break
        default:
          query.sort = '-startDate'
      }

      const response = await payload.find({
        ...query,
        depth: 2,
        locale: 'all',
      })

      return {
        docs: response.docs as AcademicActivity[],
        totalDocs: response.totalDocs,
        limit: response.limit,
        totalPages: response.totalPages,
        page: response.page || 1,
        pagingCounter: response.pagingCounter,
        hasPrevPage: response.hasPrevPage,
        hasNextPage: response.hasNextPage,
        prevPage: response.prevPage || null,
        nextPage: response.nextPage || null,
      }
    } catch (error) {
      return {
        docs: [],
        totalDocs: 0,
        limit,
        totalPages: 0,
        page,
        pagingCounter: 0,
        hasPrevPage: false,
        hasNextPage: false,
        prevPage: null,
        nextPage: null,
      }
    }
  },

  /**
   * Obtiene actividades académicas próximas para la página de inicio
   */
  async getUpcomingAcademicActivities({
    limit = 4,
    findArgs
  }: { limit?: number, findArgs?: FindArgs } = {}): Promise<AcademicActivity[]> {
    try {
      const now = new Date()

      const response = await this.getAcademicActivities({
        limit,
        status: 'active',
        startDateFrom: now, // Solo actividades que empiecen desde ahora
        sort: 'start-date-asc', // Ordenar por fecha de inicio ascendente
        findArgs
      })

      return response.docs
    } catch (error) {
      console.error('Error al obtener actividades próximas:', error)
      return []
    }
  },

  /**
   * Obtiene una actividad académica por su ID
   */
  async getAcademicActivityById(id: string): Promise<AcademicActivity | null> {
    try {
      const payload = await getPayloadClient()

      const response = await payload.find({
        collection: 'academic-activities',
        where: {
          id: { equals: parseInt(id) },
          status: { equals: 'active' },
        },
        depth: 3,
        limit: 1,
      })

      return response.docs.length > 0 ? (response.docs[0] as AcademicActivity) : null
    } catch (error) {
      console.error('Error al obtener actividad académica por ID:', error)
      return null
    }
  },

  /**
   * Obtiene una actividad académica por su slug
   */
  async getAcademicActivityBySlug(slug: string): Promise<AcademicActivity | null> {
    try {
      const payload = await getPayloadClient()

      const response = await payload.find({
        collection: 'academic-activities',
        where: {
          slug: { equals: slug },
          status: { equals: 'active' },
        },
        depth: 3,
        limit: 1,
      })

      return response.docs.length > 0 ? (response.docs[0] as AcademicActivity) : null
    } catch {
      return null
    }
  },

  /**
   * Obtiene actividades académicas que están ocurriendo ahora
   */
  async getCurrentAcademicActivities(limit: number = 10): Promise<AcademicActivity[]> {
    try {
      const now = new Date()

      const response = await this.getAcademicActivities({
        limit,
        status: 'active',
        startDateTo: now, // Que ya hayan empezado
        endDateFrom: now, // Que aún no hayan terminado (o no tengan fecha de fin)
        sort: 'start-date-asc',
      })

      return response.docs
    } catch (error) {
      console.error('Error al obtener actividades actuales:', error)
      return []
    }
  },

  /**
   * Obtiene todos los slugs de actividades académicas para generación estática
   */
  async getAllAcademicActivitySlugs(): Promise<string[]> {
    try {
      const payload = await getPayloadClient()

      const response = await payload.find({
        collection: 'academic-activities',
        where: {
          status: { equals: 'active' },
        },
        limit: 1000,
        depth: 0,
        select: { "slug": true },
      })

      return response.docs
        .map((activity) => activity.slug)
        .filter((slug): slug is string => Boolean(slug))
    } catch (error) {
      console.error('Error al obtener slugs de actividades:', error)
      return []
    }
  },

  /**
   * Obtiene actividades relacionadas basadas en categoría y etiquetas
   */
  async getRelatedAcademicActivities(
    currentActivityId: number,
    categoryId?: string | number,
    tags?: string[],
    limit: number = 3
  ): Promise<AcademicActivity[]> {
    try {
      const query: any = {
        status: { equals: 'active' },
        id: { not_equals: currentActivityId },
      }

      // Priorizar por categoría si existe
      if (categoryId) {
        query.category = { equals: categoryId }
      }

      // Si hay etiquetas, buscar actividades con etiquetas similares
      if (tags && tags.length > 0) {
        query.tags = { in: tags }
      }

      const response = await this.getAcademicActivities({
        limit,
        sort: 'start-date-asc',
      })

      return response.docs
    } catch (error) {
      console.error('Error al obtener actividades relacionadas:', error)
      return []
    }
  },

}
