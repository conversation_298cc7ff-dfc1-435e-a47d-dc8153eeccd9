'use client'

import { motion, MotionProps, Variants } from 'motion/react'
import { ReactNode } from 'react'

export type ScaleType = 'in' | 'out' | 'bounce' | 'pulse' | 'grow' | 'shrink'

export interface ScaleProps extends Omit<MotionProps, 'variants' | 'initial' | 'animate' | 'exit'> {
  children: ReactNode
  /** Tipo de animación de escala */
  type?: ScaleType
  /** Duración de la animación en segundos */
  duration?: number
  /** Retraso antes de iniciar la animación */
  delay?: number
  /** Escala inicial */
  initialScale?: number
  /** Escala final */
  finalScale?: number
  /** Opacidad inicial */
  initialOpacity?: number
  /** Opacidad final */
  finalOpacity?: number
  /** Función de easing personalizada */
  ease?: string | number[]
  /** Si la animación debe ejecutarse una sola vez */
  once?: boolean
  /** Umbral de visibilidad para activar la animación */
  threshold?: number
  /** Margen para el viewport */
  margin?: string
  /** Si debe animar al salir */
  animateExit?: boolean
  /** Punto de origen de la transformación */
  transformOrigin?: string
  /** Si debe repetirse la animación */
  repeat?: number | boolean
  /** Dirección de la repetición */
  repeatType?: 'loop' | 'reverse' | 'mirror'
  /** Clase CSS adicional */
  className?: string
}

const createScaleVariants = (
  type: ScaleType,
  initialScale: number,
  finalScale: number,
  initialOpacity: number,
  finalOpacity: number,
  duration: number,
  delay: number,
  ease: string | number[],
  repeat?: number | boolean,
  repeatType?: 'loop' | 'reverse' | 'mirror'
): Variants => {
  const baseTransition = {
    duration,
    delay,
    ease,
    repeat: repeat === true ? Infinity : repeat,
    repeatType,
  }

  switch (type) {
    case 'in':
      return {
        hidden: { 
          scale: initialScale, 
          opacity: initialOpacity 
        },
        visible: { 
          scale: finalScale, 
          opacity: finalOpacity,
          transition: baseTransition
        },
        exit: { 
          scale: initialScale, 
          opacity: initialOpacity,
          transition: { duration: duration * 0.5 }
        },
      }

    case 'out':
      return {
        hidden: { 
          scale: finalScale, 
          opacity: finalOpacity 
        },
        visible: { 
          scale: initialScale, 
          opacity: initialOpacity,
          transition: baseTransition
        },
        exit: { 
          scale: finalScale, 
          opacity: finalOpacity,
          transition: { duration: duration * 0.5 }
        },
      }

    case 'bounce':
      return {
        hidden: { 
          scale: initialScale, 
          opacity: initialOpacity 
        },
        visible: { 
          scale: finalScale, 
          opacity: finalOpacity,
          transition: {
            ...baseTransition,
            ease: [0.68, -0.55, 0.265, 1.55]
          }
        },
        exit: { 
          scale: initialScale, 
          opacity: initialOpacity 
        },
      }

    case 'pulse':
      return {
        hidden: { 
          scale: finalScale, 
          opacity: finalOpacity 
        },
        visible: { 
          scale: [finalScale, finalScale * 1.1, finalScale],
          opacity: finalOpacity,
          transition: {
            duration: duration,
            delay,
            repeat: repeat === true ? Infinity : repeat || Infinity,
            repeatType: 'reverse',
            ease: 'easeInOut'
          }
        },
        exit: { 
          scale: finalScale, 
          opacity: initialOpacity 
        },
      }

    case 'grow':
      return {
        hidden: { 
          scale: 0, 
          opacity: 0 
        },
        visible: { 
          scale: finalScale, 
          opacity: finalOpacity,
          transition: {
            ...baseTransition,
            ease: 'backOut'
          }
        },
        exit: { 
          scale: 0, 
          opacity: 0 
        },
      }

    case 'shrink':
      return {
        hidden: { 
          scale: finalScale * 2, 
          opacity: initialOpacity 
        },
        visible: { 
          scale: finalScale, 
          opacity: finalOpacity,
          transition: baseTransition
        },
        exit: { 
          scale: 0, 
          opacity: 0 
        },
      }

    default:
      return {
        hidden: { 
          scale: initialScale, 
          opacity: initialOpacity 
        },
        visible: { 
          scale: finalScale, 
          opacity: finalOpacity,
          transition: baseTransition
        },
        exit: { 
          scale: initialScale, 
          opacity: initialOpacity 
        },
      }
  }
}

export const Scale: React.FC<ScaleProps> = ({
  children,
  type = 'in',
  duration = 0.6,
  delay = 0,
  initialScale = 0.8,
  finalScale = 1,
  initialOpacity = 0,
  finalOpacity = 1,
  ease = 'easeOut',
  once = true,
  threshold = 0.1,
  margin = '0px',
  animateExit = false,
  transformOrigin = 'center',
  repeat,
  repeatType = 'loop',
  className,
  ...motionProps
}) => {
  const variants = createScaleVariants(
    type,
    initialScale,
    finalScale,
    initialOpacity,
    finalOpacity,
    duration,
    delay,
    ease,
    repeat,
    repeatType
  )

  return (
    <motion.div
      className={className}
      style={{ transformOrigin }}
      variants={variants}
      initial="hidden"
      whileInView="visible"
      exit={animateExit ? "exit" : undefined}
      viewport={{ 
        once, 
        amount: threshold,
        margin 
      }}
      {...motionProps}
    >
      {children}
    </motion.div>
  )
}

// Componentes predefinidos para casos comunes
export const ScaleIn: React.FC<Omit<ScaleProps, 'type'>> = (props) => (
  <Scale type="in" {...props} />
)

export const ScaleOut: React.FC<Omit<ScaleProps, 'type'>> = (props) => (
  <Scale type="out" {...props} />
)

export const ScaleBounce: React.FC<Omit<ScaleProps, 'type'>> = (props) => (
  <Scale type="bounce" {...props} />
)

export const ScalePulse: React.FC<Omit<ScaleProps, 'type'>> = (props) => (
  <Scale type="pulse" {...props} />
)

export const ScaleGrow: React.FC<Omit<ScaleProps, 'type'>> = (props) => (
  <Scale type="grow" {...props} />
)

export const ScaleShrink: React.FC<Omit<ScaleProps, 'type'>> = (props) => (
  <Scale type="shrink" {...props} />
)

// Variantes con configuraciones específicas
export const ScaleInSlow: React.FC<Omit<ScaleProps, 'type' | 'duration'>> = (props) => (
  <Scale type="in" duration={1.2} {...props} />
)

export const ScaleInFast: React.FC<Omit<ScaleProps, 'type' | 'duration'>> = (props) => (
  <Scale type="in" duration={0.3} {...props} />
)

export const ScaleInBig: React.FC<Omit<ScaleProps, 'type' | 'initialScale'>> = (props) => (
  <Scale type="in" initialScale={0.3} {...props} />
)

export default Scale
