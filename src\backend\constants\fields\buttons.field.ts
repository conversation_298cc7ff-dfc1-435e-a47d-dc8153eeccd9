import type { Field } from 'payload'
import type { Labels } from 'payload'
import { IconFields } from './icon.field'

export const ButtonLabels: Labels = {
  singular: 'Botón',
  plural: 'Botones',
}

export const ButtonFields: Field[] = [
  {
    name: 'text',
    type: 'text',
    required: true,
    label: 'Texto',
    admin: {
      description: 'Texto que se mostrará en el botón',
    },
  },
  {
    name: 'href',
    type: 'text',
    required: true,
    label: 'Enlace',
    admin: {
      description: 'URL a la que dirigirá el botón',
    },
  },
  ...IconFields({
    description: "Ícono que aparecerá a la izquierda del texto (opcional)",
  }),
  {
    name: 'variant',
    type: 'select',
    required: true,
    label: 'Variante',
    options: [
      { label: 'Blanco', value: 'white' },
      { label: 'Secundario', value: 'secondary' },
      { label: 'Primario', value: 'default' },
      { label: 'Enlace', value: 'link' },
      { label: 'Destructivo', value: 'destructive' },
    ],
    defaultValue: 'default',
    admin: {
      description: 'Estilo visual del botón',
      components: {
        Field: '/backend/components/fields/ButtonVariantField',
      },
    },
  },
  {
    name: 'size',
    type: 'select',
    required: true,
    label: 'Tamaño',
    options: [
      { label: 'Por defecto', value: 'default' },
      { label: 'Pequeño', value: 'sm' },
      { label: 'Grande', value: 'lg' },
    ],
    defaultValue: 'default',
    admin: {
      description: 'Tamaño del botón',
    },
  },
]
