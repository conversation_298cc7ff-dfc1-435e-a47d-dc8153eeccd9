'use client'
import { AcademicActivity, AcademicActivityCategory, AcademicActivityTag, AcademicActivityMedia } from '@/payload-types'
import { Badge } from '@/components/ui/badge'
import { Text } from '@/components/ui/text'
import { FadeUp } from '@/components/motion'
import { Tag } from 'lucide-react'
import { getTextColor } from '@/lib/utils'
import { DEFAULT_BADGE_COLOR } from '../constants'

interface AcademicActivityHeroProps {
    activity: AcademicActivity
}

export default function AcademicActivityHero({ activity }: AcademicActivityHeroProps) {
    const mainImage = activity.mainImage as AcademicActivityMedia

    const getImageUrl = () => {
        const image = activity.mainImage as AcademicActivityMedia
        return image?.url || '/placeholder-activity.jpg'
    }

    const getCategoryName = () => {
        const category = activity.category as AcademicActivityCategory
        return category?.name || ''
    }

    const getCategoryColor = () => {
        const category = activity.category as AcademicActivityCategory
        return category?.color || DEFAULT_BADGE_COLOR
    }

    const getTags = () => {
        if (!activity.tags) return []
        return (activity.tags as AcademicActivityTag[]).slice(0, 4)
    }

    return (
        <>
            {/* Hero Image */}
            <div className="relative overflow-hidden"
                style={{
                    height: mainImage.sizes?.hero?.height || mainImage.sizes?.card?.height || 400,
                    width: "100%",
                }}
            >
                {/* Background blur */}
                <div
                    className="absolute inset-0 w-full h-full bg-cover bg-center bg-no-repeat blur-md scale-110"
                    style={{
                        backgroundImage: `url(${getImageUrl()})`,
                    }}
                />

                {/* Overlay */}
                <div className="absolute inset-0 bg-black/40" />

                {/* Main image centered and maintaining aspect ratio */}
                <div className="absolute inset-0 flex items-center justify-center">
                    <img
                        src={getImageUrl()}
                        alt={activity.title}
                        className="max-w-full max-h-full object-contain md:object-cover rounded-lg shadow-2xl h-full"
                    />
                </div>

                {/* Gradient overlay for text readability */}
                <div className="absolute inset-0 bg-gradient-to-t from-primary/70 via-transparent to-transparent" />

                {/* Overlay Content */}
                <div className="absolute inset-0 flex items-end">
                    <div className="container mx-auto px-4 pb-8">
                        <FadeUp delay={0.2} className="max-w-4xl">
                            {/* Category Badge */}
                            {getCategoryName() && (
                                <div className="mb-4">
                                    <span
                                        className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium text-white"
                                        style={{ backgroundColor: getCategoryColor(), color: getTextColor(getCategoryColor()) }}
                                    >
                                        {getCategoryName()}
                                    </span>
                                </div>
                            )}

                            {/* Tags */}
                            {getTags().length > 0 && (
                                <div className="flex flex-wrap gap-2">
                                    {getTags().map((tag) => (
                                        <Badge key={tag.id} variant="secondary" className="bg-white/20 text-white border-white/30 hover:bg-white/30">
                                            <Tag className="w-3 h-3 mr-1" />
                                            {tag.name}
                                        </Badge>
                                    ))}
                                </div>
                            )}
                        </FadeUp>
                    </div>
                </div>
            </div>

            <article className='bg-primary'>
                {/* Title */}
                <div className='container mx-auto px-4 py-8'>
                    <Text variant="h1" className="text-white mb-4 leading-tight text-3xl">
                        {activity.title}
                    </Text>
                </div>
            </article>
        </>
    )
}