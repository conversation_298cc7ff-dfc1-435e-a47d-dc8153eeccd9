'use client'
import Link from 'next/link'
import { buttonVariants } from '@/components/ui/button'
import { motion } from 'motion/react'
import { Text } from '@/components/ui/text'
import { HomeCarousel } from '@/payload-types'

export default function CarouselItemComponent({
  item,
  index,
}: {
  item: HomeCarousel
  index: number
}) {
  return (
    <div className="relative h-full w-screen" id={`carousel-item-${index}`}>
      <img
        src={typeof item.image === 'object' && item.image?.url ? item.image.url : ''}
        alt={item.title}
        className="w-full h-full object-cover"
      />
      <div className="absolute top-0 left-0 w-full h-full bg-primary/50">
        <div className="container mx-auto h-full flex items-end justify-start carousel-item-content">
          <div className="mb-20  space-y-5 max-w-160">
            <Text
              className="text-white font-bold text-5xl"
              variant="h2"
              animationProps={{
                initial: { opacity: 0, y: 100 },
              }}
            >
              {item.title}
            </Text>
            <motion.div
              initial={{ opacity: 0, y: 100 }}
              className="py-1 px-1 backdrop-blur-md bg-primary/30 rounded w-full flex items-stretch gap-2 carousel-item-content-description"
            >
              <div className="w-[3px] bg-secondary"></div>
              <Text className="text-white italic">{item.description}</Text>
            </motion.div>
            {item.buttons && (
              <div className="flex gap-3">
                {item.buttons.map((button, buttonIndex: number) => (
                  <motion.div
                    key={button.id}
                    className={`carousel-item-content-button-${buttonIndex}`}
                    initial={{ opacity: 0, y: 100 }}
                  >
                    <Link
                      href={button.href}
                      className={buttonVariants({
                        variant: button.variant as 'default',
                        size: 'sm',
                      })}
                    >
                      {button.text}
                    </Link>
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}