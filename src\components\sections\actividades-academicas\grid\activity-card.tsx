'use client'
import { motion } from 'motion/react'
import { Calendar, Clock, MapPin, Monitor, Users } from 'lucide-react'
import { Text } from '@/components/ui/text'
import { Badge } from '@/components/ui/badge'
import { AcademicActivity, AcademicActivityCategory, AcademicActivityTag, AcademicActivityMedia } from '@/payload-types'
import Link from 'next/link'
import { formatDate } from '@/lib/date-utils'
import { DEFAULT_BADGE_COLOR } from '../constants'
import { getTextColor } from '@/lib/utils'

interface ActivityCardProps {
  activity: AcademicActivity
}

export default function ActivityCard({ activity }: ActivityCardProps) {
  const getImageUrl = () => {
    const image = activity.mainImage as AcademicActivityMedia
    return image?.url || '/placeholder-activity.jpg'
  }

  const getCategoryName = () => {
    const category = activity.category as AcademicActivityCategory
    return category?.name || ''
  }

  const getCategoryColor = () => {
    const category = activity.category as AcademicActivityCategory
    return category?.color || DEFAULT_BADGE_COLOR
  }

  const getTags = () => {
    if (!activity.tags) return []
    return (activity.tags as AcademicActivityTag[]).slice(0, 2)
  }

  const getLocationInfo = () => {
    const locationBlock = activity.eventBlocks?.find(block => block.blockType === 'location')
    if (!locationBlock || locationBlock.blockType !== 'location') return null

    const modality = locationBlock.modality
    let locationText = ''
    let icon = MapPin

    switch (modality) {
      case 'online':
        locationText = 'Virtual'
        icon = Monitor
        break
      case 'presencial':
        locationText = locationBlock.venue || locationBlock.address || 'Presencial'
        icon = MapPin
        break
      case 'hybrid':
        locationText = 'Híbrido'
        icon = Users
        break
      default:
        locationText = 'Por definir'
    }

    return { text: locationText, icon, modality }
  }


  const locationInfo = getLocationInfo()

  return (
    <Link href={`/actividades-academicas/${activity.slug}`}>
      <motion.div
        whileHover={{ y: -5 }}
        transition={{ duration: 0.2 }}
        className="h-[500px] group bg-white rounded-lg shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden cursor-pointer border"
      >
        {/* Imagen */}
        <div className="relative h-[250px] overflow-hidden">
          <img
            src={getImageUrl()}
            alt={activity.title}
            className="object-cover group-hover:scale-105 transition-transform duration-300 w-full h-full"
          />

          {/* Overlay gradiente */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />

          {/* Badge de categoría */}
          {getCategoryName() && (
            <div className="absolute top-3 right-3 z-10">
              <span
                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                style={{ backgroundColor: getCategoryColor(), color: getTextColor(getCategoryColor()) }}
              >
                {getCategoryName()}
              </span>
            </div>
          )}

          {/* Fecha en la esquina inferior izquierda */}
          <div className="absolute bottom-3 left-3 z-10">
            <div className="bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2">
              <div className="flex items-center gap-2 text-xs">
                <Calendar className="w-3 h-3 text-primary" />
                <span className="font-medium text-gray-900">
                  {formatDate(activity.startDate, "d MMM")}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Contenido */}
        <div className="p-4">
          <Text variant="h4" className="text-primary mb-2 group-hover:text-primary/90 transition-colors line-clamp-2">
            {activity.title}
          </Text>

          <Text variant="base" className="text-gray-600 text-sm mb-4 line-clamp-2">
            {activity.excerpt}
          </Text>

          {/* Etiquetas */}
          {getTags().length > 0 && (
            <div className="flex flex-wrap gap-1 mb-4">
              {getTags().map((tag) => (
                <Badge key={tag.id} variant="secondary" className="text-xs">
                  {tag.name}
                </Badge>
              ))}
            </div>
          )}

          {/* Información del evento */}
          <div className="flex flex-col gap-2 text-xs text-gray-500">

            {/* Ubicación/Modalidad */}
            {locationInfo && (
              <div className="flex items-center gap-2">
                <locationInfo.icon className="w-3 h-3" />
                <span className="truncate">
                  {locationInfo.text}
                </span>
              </div>
            )}
          </div>
        </div>
      </motion.div>
    </Link>
  )
}
