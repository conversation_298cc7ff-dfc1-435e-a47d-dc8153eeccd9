'use client'
import { But<PERSON> } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { useField } from '@payloadcms/ui'
import type { SelectFieldClientComponent } from 'payload'

const VARIANTS = [
  { key: 'default', label: 'Primario' },
  { key: 'secondary', label: 'Secundario' },
  { key: 'destructive', label: 'Destructivo' },
  { key: 'white', label: 'Blanco' },
  { key: 'link', label: 'Enlace' },
] as const

const ButtonVariantField: SelectFieldClientComponent = ({ path, field }) => {
  const { value, setValue } = useField({ path })

  return (
    <div className="field-type">
      {/* Label */}
      {field?.label && (
        <label className="field-label">
          {typeof field.label === 'string' ? field.label : 'Variante'}
        </label>
      )}


      {/* Button Variants */}
      <div className="py-2 flex gap-4 flex-wrap">
        {VARIANTS.map((variant) => (
          <Button
            key={variant.key}
            type="button"
            variant={variant.key}
            className={
              cn(
                "border-none",
                value === variant.key ? "ring-1 ring-primary-500 ring-offset-4 ring-primary" : ""
              )
            }
            onClick={() => setValue(variant.key)}
          >
            {variant.label}
          </Button>
        ))}
      </div>
      {/* Description */}
      {field?.admin?.description && (
        <div className="field-description">
          {typeof field.admin.description === 'string' ? field.admin.description : ''}
        </div>
      )}

    </div>
  )
}

export default ButtonVariantField
