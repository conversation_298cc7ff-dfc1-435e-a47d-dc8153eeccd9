import { getPayload } from 'payload'
import type { Payload } from 'payload'
import config from '@/payload.config'

/* eslint-disable no-var */
declare global {
  var payloadClient: Payload | undefined
}

export const getPayloadClient = async (): Promise<Payload> => {
  if (!process.env.PAYLOAD_SECRET) {
    throw new Error('PAYLOAD_SECRET environment variable is missing')
  }

  if (!globalThis.payloadClient) {
    const payloadConfig = await config

    globalThis.payloadClient = await getPayload({
      config: payloadConfig,
    })
  }

  return globalThis.payloadClient
}
